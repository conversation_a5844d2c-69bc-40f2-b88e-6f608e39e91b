{"version": 3, "file": "Struct.d.ts", "sourceRoot": "", "sources": ["../../src/Struct.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAE/C,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAEnC,OAAO,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAA;AAEvD;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,SAAS,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS;SAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG;KAAE,EACxF,CAAC,EAAE,CAAC,KACD,WAAW,CAAC,CAAC,EAAE;SAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IACpF;;;;;;;;;;;;;OAaG;IACH,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC,CAAC,EAAE;SAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;CAYtJ,CAAA;AAED;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,SAAS,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,SAAS;SAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG;KAAE,EAAE,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACpI;;;;;;;;;;;;;OAaG;IACH,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CAUtG,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAClF,aAAa,EAAE,CAAC,KACb,WAAW,CAAC,WAAW,CAC1B;IAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;CAAE,CACtE,CAAA;AAEtB;;;;;;;;GAQG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,SAAS;IAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;CAAE,EAC1E,MAAM,EAAE,CAAC,KACN,KAAK,CAAC,KAAK,CAAC;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;CAAE,CAAgB,CAAA;AAEtG,KAAK,WAAW,CAAC,CAAC,EAAE,CAAC,IACjB,OAAO,GACP;KACC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACvG,CAAA;AACH,KAAK,gBAAgB,CAAC,CAAC,EAAE,CAAC,IAAI;KAC3B,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,OAAO;CAChG,CAAA;AACD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAChE;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAa7D,CAAA;AAED;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,GAAG,GACb,CAAC,SAAS,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,GAAE,EAAE,GAAG,CAAC,KAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CACtG,CAAA;AAEV;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,SAAS,EAAE,EAAE,GAAG,CAAC,KAAG,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAgD,CAAA"}