{"version": 3, "file": "ManagedRuntime.d.ts", "sourceRoot": "", "sources": ["../../src/ManagedRuntime.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AACtC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAGxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAkC,CAAA;AAE9D;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;;;GAKG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,KAAK,IAAI,cAAc,CAAC,OAAO,EAAE,OAAO,CAA6B,CAAA;AAExH;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC;;;OAGG;IACH,KAAY,OAAO,CAAC,CAAC,SAAS,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,GAC3G,KAAK,CAAA;IACT;;;OAGG;IACH,KAAY,KAAK,CAAC,CAAC,SAAS,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;CACtH;AAED;;;GAGG;AACH,MAAM,WAAW,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAE,SAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACzF,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAA;IAC/B,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC7D,QAAQ,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAEnD;;;OAGG;IACH,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EACrB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC5B,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,KAC7B,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;IAElC;;;;;OAKG;IACH,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;IAEpF;;;;;OAKG;IACH,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;IAE7D;;;;;;OAMG;IACH,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC9B,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,KACxD,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;IAE9B;;;;;;;OAOG;IACH,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;QACpE,QAAQ,CAAC,MAAM,CAAC,EAAE,WAAW,GAAG,SAAS,CAAA;KAC1C,KAAK,OAAO,CAAC,CAAC,CAAC,CAAA;IAEhB;;;;;;OAMG;IACH,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;QACxE,QAAQ,CAAC,MAAM,CAAC,EAAE,WAAW,GAAG,SAAS,CAAA;KAC1C,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;IAEnC;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA;IAErC;;OAEG;IACH,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IAEzD,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAA;IACxD,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,yBAAyB,CAAA;CAC1D;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAmB,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IACxG,cAAc,CAAC,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;CAC9E;AAED;;;GAGG;AACH,MAAM,WAAW,yBAA0B,SAAQ,MAAM,CAAC,iBAAiB;IACzE,MAAM,CAAC,EAAE,IAAI,CAAA;CACd;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EACtB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAC/B,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,SAAS,KAChC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAiB,CAAA"}