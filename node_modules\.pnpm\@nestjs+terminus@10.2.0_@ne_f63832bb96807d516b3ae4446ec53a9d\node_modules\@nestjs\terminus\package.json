{"name": "@nestjs/terminus", "version": "10.2.0", "description": "Terminus integration provides readiness/liveness health checks for NestJS.", "repository": {"type": "git", "url": "https://github.com/nestjs/terminus"}, "license": "MIT", "author": "Livio B<PERSON>", "main": "dist/index.js", "scripts": {"build": "tsc -p tsconfig.build.json && gulp move:protos && gulp move", "build:all": "npm run build && gulp build:all", "build:samples": "gulp install:samples && npm run build && gulp build:samples && gulp test:samples && gulp test:e2e:samples", "clean": "gulp clean", "format": "prettier --check \"{lib,e2e,sample,tools}/**/*.ts\"", "format:fix": "prettier --write \"{lib,e2e,sample,tools}/**/*.ts\"", "lint": "eslint \"lib/**/*.ts\"", "lint:fix": "eslint \"lib/**/*.ts\" \"e2e/**/*.ts\" \"tools/**/*.ts\" --fix", "precommit": "lint-staged", "prepublish": "npm run build", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "test": "npm run test:ut && npm run test:import", "test:e2e": "jest --config=e2e/jest-e2e.json --detectOpenHandles --forceExit", "test:ut": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:ut:cov": "npm run test:ut -- --coverage", "test:import": "ts-node tools/import-check.ts", "release": "release-it", "prepare": "husky install"}, "dependencies": {"boxen": "5.1.2", "check-disk-space": "3.4.0"}, "devDependencies": {"@commitlint/cli": "17.8.1", "@commitlint/config-angular": "17.8.1", "@grpc/grpc-js": "1.9.11", "@grpc/proto-loader": "0.7.10", "@mikro-orm/core": "5.8.1", "@mikro-orm/mongodb": "5.8.1", "@mikro-orm/mysql": "5.8.1", "@mikro-orm/nestjs": "5.2.2", "@nestjs/axios": "3.0.1", "@nestjs/common": "10.2.10", "@nestjs/core": "10.2.10", "@nestjs/microservices": "10.2.10", "@nestjs/mongoose": "10.0.2", "@nestjs/platform-express": "10.2.10", "@nestjs/platform-fastify": "10.2.10", "@nestjs/sequelize": "10.0.0", "@nestjs/swagger": "7.1.16", "@nestjs/testing": "10.2.10", "@nestjs/typeorm": "10.0.1", "@prisma/client": "4.16.2", "@release-it/conventional-changelog": "5.1.1", "@types/express": "4.17.21", "@types/jest": "29.5.10", "@types/node": "16.18.65", "@types/supertest": "2.0.16", "@typescript-eslint/eslint-plugin": "6.7.3", "@typescript-eslint/parser": "6.7.3", "amqp-connection-manager": "4.1.14", "amqplib": "0.10.3", "axios": "^1.4.0", "cli-color": "2.0.3", "delete-empty": "3.0.0", "eslint": "8.49.0", "eslint-config-prettier": "9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-unused-imports": "^3.0.0", "fastify": "4.23.2", "gulp": "4.0.2", "gulp-clean": "0.4.0", "gulp-debug": "4.0.0", "husky": "8.0.3", "ioredis": "5.3.2", "jest": "29.7.0", "kafkajs": "^2.2.4", "lint-staged": "14.0.1", "mongoose": "7.6.5", "mysql2": "3.6.1", "nats": "2.16.0", "portfinder": "1.0.32", "prettier": "3.1.0", "prisma": "4.16.2", "reflect-metadata": "0.1.13", "release-it": "15.11.0", "rollup": "3.29.4", "rollup-plugin-dts": "6.0.2", "rxjs-compat": "6.6.7", "sequelize": "6.33.0", "sequelize-typescript": "2.1.5", "supertest": "6.3.3", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typeorm": "0.3.17", "typescript": "5.2.2", "wait-port": "1.0.4"}, "peerDependencies": {"@grpc/grpc-js": "*", "@grpc/proto-loader": "*", "@mikro-orm/core": "*", "@mikro-orm/nestjs": "*", "@nestjs/axios": "^1.0.0 || ^2.0.0 || ^3.0.0", "@nestjs/common": "^9.0.0 || ^10.0.0", "@nestjs/core": "^9.0.0 || ^10.0.0", "@nestjs/microservices": "^9.0.0 || ^10.0.0", "@nestjs/mongoose": "^9.0.0 || ^10.0.0", "@nestjs/sequelize": "^9.0.0 || ^10.0.0", "@nestjs/typeorm": "^9.0.0 || ^10.0.0", "@prisma/client": "*", "mongoose": "*", "reflect-metadata": "0.1.x", "rxjs": "7.x", "sequelize": "*", "typeorm": "*"}, "peerDependenciesMeta": {"@nestjs/microservices": {"optional": true}, "@grpc/grpc-js": {"optional": true}, "@grpc/proto-loader": {"optional": true}, "@nestjs/axios": {"optional": true}, "@mikro-orm/nestjs": {"optional": true}, "@mikro-orm/core": {"optional": true}, "@nestjs/mongoose": {"optional": true}, "@nestjs/sequelize": {"optional": true}, "@nestjs/typeorm": {"optional": true}, "mongoose": {"optional": true}, "sequelize": {"optional": true}, "typeorm": {"optional": true}, "@prisma/client": {"optional": true}}, "lint-staged": {"*.ts": ["prettier --write"]}, "prettier": {"trailingComma": "all", "singleQuote": true}, "renovate": {"semanticCommits": true, "packageRules": [{"depTypeList": ["devDependencies"], "automerge": true}], "extends": ["config:base"]}, "commitlint": {"extends": ["@commitlint/config-angular"], "rules": {"subject-case": [2, "always", ["sentence-case", "start-case", "pascal-case", "upper-case", "lower-case"]], "type-enum": [2, "always", ["build", "chore", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test", "sample"]]}}, "jest": {"moduleFileExtensions": ["ts", "tsx", "js", "json"], "transform": {"^.+\\.ts$": "ts-jest", "^.+\\.tsx?$": "ts-jest"}, "setupFiles": ["<rootDir>/node_modules/reflect-metadata/Reflect.js"], "testRegex": "/lib/.*\\.(test|spec).(ts|tsx|js)$", "collectCoverageFrom": ["lib/**/*.{js,jsx,tsx,ts}", "!**/node_modules/**", "!**/vendor/**"], "coverageReporters": ["json", "lcov"], "coverageDirectory": "./coverage", "testEnvironment": "node"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint/eslint-plugin", "unused-imports"], "extends": ["plugin:deprecation/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "root": true, "env": {"node": true, "jest": true}, "overrides": [{"files": ["*.{js,ts}"], "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": ["./tsconfig.json", "./e2e/tsconfig.json"]}}}}, {"files": ["*.spec.ts"], "env": {"jest": true}, "extends": ["plugin:jest/recommended"]}], "rules": {"curly": 1, "prefer-const": 1, "no-console": 1, "strict": 1, "no-debugger": 1, "@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/prefer-optional-chain": 1, "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/consistent-type-imports": [2, {"prefer": "type-imports", "fixStyle": "inline-type-imports"}], "import/newline-after-import": 2, "import/order": [2, {"groups": ["builtin", "external", "internal", ["parent", "sibling", "index"]], "pathGroups": [{"pattern": "@nestjs*", "group": "external", "position": "before"}], "distinctGroup": false, "alphabetize": {"order": "asc", "orderImportKind": "asc", "caseInsensitive": true}, "newlines-between": "never", "pathGroupsExcludedImportTypes": []}]}}}