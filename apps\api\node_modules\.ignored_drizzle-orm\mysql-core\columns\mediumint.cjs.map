{"version": 3, "sources": ["../../../src/mysql-core/columns/mediumint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\nimport type { MySqlIntConfig } from './int.ts';\n\nexport type MySqlMediumIntBuilderInitial<TName extends string> = MySqlMediumIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlMediumInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlMediumIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlMediumInt'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlMediumIntBuilder';\n\n\tconstructor(name: T['name'], config?: MySqlIntConfig) {\n\t\tsuper(name, 'number', 'MySqlMediumInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlMediumInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlMediumInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlMediumInt<T extends ColumnBaseConfig<'number', 'MySqlMediumInt'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlMediumInt';\n\n\tgetSQLType(): string {\n\t\treturn `mediumint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function mediumint<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlIntConfig,\n): MySqlMediumIntBuilderInitial<TName> {\n\treturn new MySqlMediumIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkF;AAY3E,MAAM,8BACJ,kDACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,2CACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO,YAAY,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EAC3D;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,UACf,MACA,QACsC;AACtC,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}