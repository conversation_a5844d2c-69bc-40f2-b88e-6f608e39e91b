{"version": 3, "sources": ["../../src/expo-sqlite/session.ts"], "sourcesContent": ["import type { SQLiteDatabase, SQLiteRunResult, SQLiteStatement } from 'expo-sqlite/next';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport {\n    type PreparedQueryConfig as PreparedQueryConfigBase,\n    SQLitePreparedQuery,\n    type SQLiteExecuteMethod,\n    SQLiteSession,\n    type SQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface ExpoSQLiteSessionOptions {\n    logger?: Logger;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class ExpoSQLiteSession<\n    TFullSchema extends Record<string, unknown>,\n    TSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'sync', SQLiteRunResult, TFullSchema, TSchema> {\n    static readonly [entityKind]: string = 'ExpoSQLiteSession';\n\n    private logger: Logger;\n\n    constructor(\n        private client: SQLiteDatabase,\n        dialect: SQLiteSyncDialect,\n        private schema: RelationalSchemaConfig<TSchema> | undefined,\n        options: ExpoSQLiteSessionOptions = {},\n\n    ) {\n        super(dialect);\n        this.logger = options.logger ?? new NoopLogger();\n    }\n\n    prepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n        query: Query,\n        fields: SelectedFieldsOrdered | undefined,\n        executeMethod: SQLiteExecuteMethod,\n        customResultMapper?: (rows: unknown[][]) => unknown,\n    ): ExpoSQLitePreparedQuery<T> {\n        const stmt = this.client.prepareSync(query.sql);\n        return new ExpoSQLitePreparedQuery(stmt, query, this.logger, fields, executeMethod, customResultMapper);\n    }\n\n    override transaction<T>(\n        transaction: (tx: ExpoSQLiteTransaction<TFullSchema, TSchema>) => T,\n        config: SQLiteTransactionConfig = {},\n    ): T {\n        const tx = new ExpoSQLiteTransaction('sync', this.dialect, this, this.schema);\n        this.run(sql.raw(`begin${config?.behavior ? ' ' + config.behavior : ''}`));\n        try {\n            const result = transaction(tx);\n            this.run(sql`commit`);\n            return result;\n        } catch (err) {\n            this.run(sql`rollback`);\n            throw err;\n        }\n    }\n}\n\nexport class ExpoSQLiteTransaction<\n    TFullSchema extends Record<string, unknown>,\n    TSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'sync', SQLiteRunResult, TFullSchema, TSchema> {\n    static readonly [entityKind]: string = 'ExpoSQLiteTransaction';\n\n    override transaction<T>(transaction: (tx: ExpoSQLiteTransaction<TFullSchema, TSchema>) => T): T {\n        const savepointName = `sp${this.nestedIndex}`;\n        const tx = new ExpoSQLiteTransaction('sync', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n        this.session.run(sql.raw(`savepoint ${savepointName}`));\n        try {\n            const result = transaction(tx);\n            this.session.run(sql.raw(`release savepoint ${savepointName}`));\n            return result;\n        } catch (err) {\n            this.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n            throw err;\n        }\n    }\n}\n\nexport class ExpoSQLitePreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n    { type: 'sync'; run: SQLiteRunResult; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n    static readonly [entityKind]: string = 'ExpoSQLitePreparedQuery';\n\n    constructor(\n\t\tprivate stmt: SQLiteStatement,\n        query: Query,\n        private logger: Logger,\n        private fields: SelectedFieldsOrdered | undefined,\n        executeMethod: SQLiteExecuteMethod,\n        private customResultMapper?: (rows: unknown[][]) => unknown,\n    ) {\n        super('sync', executeMethod, query);\n    }\n\n    run(placeholderValues?: Record<string, unknown>): SQLiteRunResult {\n        const params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n        this.logger.logQuery(this.query.sql, params);\n        const { changes, lastInsertRowId } = this.stmt.executeSync(params as any[]);\n        return {\n            changes,\n            lastInsertRowId,\n        };\n    }\n\n    all(placeholderValues?: Record<string, unknown>): T['all'] {\n        const { fields, joinsNotNullableMap, query, logger, stmt, customResultMapper } = this;\n        if (!fields && !customResultMapper) {\n            const params = fillPlaceholders(query.params, placeholderValues ?? {});\n            logger.logQuery(query.sql, params);\n            return stmt.executeSync(params as any[]).getAllSync();\n        }\n\n        const rows = this.values(placeholderValues) as unknown[][];\n        if (customResultMapper) {\n            return customResultMapper(rows) as T['all'];\n        }\n        return rows.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n    }\n\n    get(placeholderValues?: Record<string, unknown>): T['get'] {\n        const params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n        this.logger.logQuery(this.query.sql, params);\n\n        const { fields, stmt, joinsNotNullableMap, customResultMapper } = this;\n        if (!fields && !customResultMapper) {\n            return stmt.executeSync(params as any[]).getFirstSync();\n        }\n\n        const rows = this.values(placeholderValues) as unknown[][];\n        const row = rows[0];\n\n        if (!row) {\n            return undefined;\n        }\n\n        if (customResultMapper) {\n            return customResultMapper(rows) as T['get'];\n        }\n\n        return mapResultRow(fields!, row, joinsNotNullableMap);\n    }\n\n    values(placeholderValues?: Record<string, unknown>): T['values'] {\n        const params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n        this.logger.logQuery(this.query.sql, params);\n        return this.stmt.executeForRawResultSync(params as any[]).getAllSync();\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAE3B,oBAA2B;AAE3B,iBAAkD;AAElD,yBAAkC;AAElC,qBAMO;AACP,mBAA6B;AAQtB,MAAM,0BAGH,6BAA6D;AAAA,EAKnE,YACY,QACR,SACQ,QACR,UAAoC,CAAC,GAEvC;AACE,UAAM,OAAO;AANL;AAEA;AAKR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EACnD;AAAA,EAbA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAaR,aACI,OACA,QACA,eACA,oBAC0B;AAC1B,UAAM,OAAO,KAAK,OAAO,YAAY,MAAM,GAAG;AAC9C,WAAO,IAAI,wBAAwB,MAAM,OAAO,KAAK,QAAQ,QAAQ,eAAe,kBAAkB;AAAA,EAC1G;AAAA,EAES,YACL,aACA,SAAkC,CAAC,GAClC;AACD,UAAM,KAAK,IAAI,sBAAsB,QAAQ,KAAK,SAAS,MAAM,KAAK,MAAM;AAC5E,SAAK,IAAI,eAAI,IAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO,WAAW,EAAE,EAAE,CAAC;AACzE,QAAI;AACA,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,IAAI,sBAAW;AACpB,aAAO;AAAA,IACX,SAAS,KAAK;AACV,WAAK,IAAI,wBAAa;AACtB,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AAEO,MAAM,8BAGH,qCAAiE;AAAA,EACvE,QAAiB,wBAAU,IAAY;AAAA,EAE9B,YAAe,aAAwE;AAC5F,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,sBAAsB,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAC1G,SAAK,QAAQ,IAAI,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACA,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,QAAQ,IAAI,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACX,SAAS,KAAK;AACV,WAAK,QAAQ,IAAI,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AAEO,MAAM,gCAAqF,mCAEhG;AAAA,EAGE,YACM,MACF,OACQ,QACA,QACR,eACQ,oBACV;AACE,UAAM,QAAQ,eAAe,KAAK;AAPhC;AAEM;AACA;AAEA;AAAA,EAGZ;AAAA,EAXA,QAAiB,wBAAU,IAAY;AAAA,EAavC,IAAI,mBAA8D;AAC9D,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,UAAM,EAAE,SAAS,gBAAgB,IAAI,KAAK,KAAK,YAAY,MAAe;AAC1E,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,IAAI,mBAAuD;AACvD,UAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,MAAM,mBAAmB,IAAI;AACjF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AAChC,YAAM,aAAS,6BAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,KAAK,YAAY,MAAe,EAAE,WAAW;AAAA,IACxD;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAC1C,QAAI,oBAAoB;AACpB,aAAO,mBAAmB,IAAI;AAAA,IAClC;AACA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EAC5E;AAAA,EAEA,IAAI,mBAAuD;AACvD,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,MAAM,qBAAqB,mBAAmB,IAAI;AAClE,QAAI,CAAC,UAAU,CAAC,oBAAoB;AAChC,aAAO,KAAK,YAAY,MAAe,EAAE,aAAa;AAAA,IAC1D;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAC1C,UAAM,MAAM,KAAK,CAAC;AAElB,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACX;AAEA,QAAI,oBAAoB;AACpB,aAAO,mBAAmB,IAAI;AAAA,IAClC;AAEA,eAAO,2BAAa,QAAS,KAAK,mBAAmB;AAAA,EACzD;AAAA,EAEA,OAAO,mBAA0D;AAC7D,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,KAAK,wBAAwB,MAAe,EAAE,WAAW;AAAA,EACzE;AACJ;", "names": []}