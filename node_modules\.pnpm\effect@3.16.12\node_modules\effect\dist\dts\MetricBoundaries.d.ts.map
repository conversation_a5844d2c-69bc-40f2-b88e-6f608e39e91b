{"version": 3, "file": "MetricBoundaries.d.ts", "sourceRoot": "", "sources": ["../../src/MetricBoundaries.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,OAAO,MAAwC,CAAA;AAEpF;;;GAGG;AACH,MAAM,MAAM,sBAAsB,GAAG,OAAO,sBAAsB,CAAA;AAElE;;;GAGG;AACH,MAAM,WAAW,gBAAiB,SAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ;IAC7D,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE,sBAAsB,CAAA;IACzD,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;CACvC;AAED;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,gBAA8C,CAAA;AAEpG;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,gBAAwC,CAAA;AAEnG;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE,CACnB,OAAO,EAAE;IACP,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;CACvB,KACE,gBAAkC,CAAA;AAEvC;;;;;;GAMG;AACH,eAAO,MAAM,WAAW,EAAE,CACxB,OAAO,EAAE;IACP,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;CACvB,KACE,gBAAuC,CAAA"}