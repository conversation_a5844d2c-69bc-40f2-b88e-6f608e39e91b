var q=function(E){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},q(E)},z=function(E,C){var X=Object.keys(E);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(E);C&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(E,x).enumerable})),X.push.apply(X,Z)}return X},W=function(E){for(var C=1;C<arguments.length;C++){var X=arguments[C]!=null?arguments[C]:{};C%2?z(Object(X),!0).forEach(function(Z){t(E,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(X)):z(Object(X)).forEach(function(Z){Object.defineProperty(E,Z,Object.getOwnPropertyDescriptor(X,Z))})}return E},t=function(E,C,X){if(C=H1(C),C in E)Object.defineProperty(E,C,{value:X,enumerable:!0,configurable:!0,writable:!0});else E[C]=X;return E},H1=function(E){var C=J1(E,"string");return q(C)=="symbol"?C:String(C)},J1=function(E,C){if(q(E)!="object"||!E)return E;var X=E[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(E,C||"default");if(q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(E)};(function(E){var C=Object.defineProperty,X=function H(B,U){for(var J in U)C(B,J,{get:U[J],enumerable:!0,configurable:!0,set:function G(Y){return U[J]=function(){return Y}}})},Z={lessThanXSeconds:{one:"mwens pase yon segond",other:"mwens pase {{count}} segond"},xSeconds:{one:"1 segond",other:"{{count}} segond"},halfAMinute:"30 segond",lessThanXMinutes:{one:"mwens pase yon minit",other:"mwens pase {{count}} minit"},xMinutes:{one:"1 minit",other:"{{count}} minit"},aboutXHours:{one:"anviwon in\xE8",other:"anviwon {{count}} \xE8"},xHours:{one:"1 l\xE8",other:"{{count}} l\xE8"},xDays:{one:"1 jou",other:"{{count}} jou"},aboutXWeeks:{one:"anviwon 1 sem\xE8n",other:"anviwon {{count}} sem\xE8n"},xWeeks:{one:"1 sem\xE8n",other:"{{count}} sem\xE8n"},aboutXMonths:{one:"anviwon 1 mwa",other:"anviwon {{count}} mwa"},xMonths:{one:"1 mwa",other:"{{count}} mwa"},aboutXYears:{one:"anviwon 1 an",other:"anviwon {{count}} an"},xYears:{one:"1 an",other:"{{count}} an"},overXYears:{one:"plis pase 1 an",other:"plis pase {{count}} an"},almostXYears:{one:"pr\xE8ske 1 an",other:"pr\xE8ske {{count}} an"}},x=function H(B,U,J){var G,Y=Z[B];if(typeof Y==="string")G=Y;else if(U===1)G=Y.one;else G=Y.other.replace("{{count}}",String(U));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"nan "+G;else return"sa f\xE8 "+G;return G};function D(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,J=H.formats[U]||H.formats[H.defaultWidth];return J}}var M={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'nan l\xE8' {{time}}",long:"{{date}} 'nan l\xE8' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},S={date:D({formats:M,defaultWidth:"full"}),time:D({formats:$,defaultWidth:"full"}),dateTime:D({formats:R,defaultWidth:"full"})},L={lastWeek:"eeee 'pase nan l\xE8' p",yesterday:"'y\xE8 nan l\xE8' p",today:"'jodi a' p",tomorrow:"'demen nan l\xE8' p'",nextWeek:"eeee 'pwochen nan l\xE8' p",other:"P"},V=function H(B,U,J,G){return L[B]};function O(H){return function(B,U){var J=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",G;if(J==="formatting"&&H.formattingValues){var Y=H.defaultFormattingWidth||H.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Y;G=H.formattingValues[I]||H.formattingValues[Y]}else{var T=H.defaultWidth,K=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;G=H.values[K]||H.values[T]}var A=H.argumentCallback?H.argumentCallback(B):B;return G[A]}}var v={narrow:["av. J.-K","ap. J.-K"],abbreviated:["av. J.-K","ap. J.-K"],wide:["anvan Jezi Kris","apre Jezi Kris"]},f={narrow:["T1","T2","T3","T4"],abbreviated:["1ye trim.","2y\xE8m trim.","3y\xE8m trim.","4y\xE8m trim."],wide:["1ye trim\xE8s","2y\xE8m trim\xE8s","3y\xE8m trim\xE8s","4y\xE8m trim\xE8s"]},j={narrow:["J","F","M","A","M","J","J","O","S","O","N","D"],abbreviated:["janv.","fevr.","mas","avr.","me","jen","jiy\xE8","out","sept.","okt.","nov.","des."],wide:["janvye","fevrye","mas","avril","me","jen","jiy\xE8","out","septanm","okt\xF2b","novanm","desanm"]},P={narrow:["D","L","M","M","J","V","S"],short:["di","le","ma","m\xE8","je","va","sa"],abbreviated:["dim.","len.","mad.","m\xE8k.","jed.","van.","sam."],wide:["dimanch","lendi","madi","m\xE8kredi","jedi","vandredi","samdi"]},w={narrow:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"swa",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"maten",afternoon:"apr\xE8midi",evening:"swa",night:"maten"},wide:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"nan maten",afternoon:"nan apr\xE8midi",evening:"nan asw\xE8",night:"nan maten"}},_=function H(B,U){var J=Number(B);if(J===0)return String(J);var G=J===1?"ye":"y\xE8m";return J+G},F={ordinalNumber:_,era:O({values:v,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function H(B){return B-1}}),month:O({values:j,defaultWidth:"wide"}),day:O({values:P,defaultWidth:"wide"}),dayPeriod:O({values:w,defaultWidth:"wide"})};function Q(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=U.width,G=J&&H.matchPatterns[J]||H.matchPatterns[H.defaultMatchWidth],Y=B.match(G);if(!Y)return null;var I=Y[0],T=J&&H.parsePatterns[J]||H.parsePatterns[H.defaultParseWidth],K=Array.isArray(T)?k(T,function(N){return N.test(I)}):h(T,function(N){return N.test(I)}),A;A=H.valueCallback?H.valueCallback(K):K,A=U.valueCallback?U.valueCallback(A):A;var a=B.slice(I.length);return{value:A,rest:a}}}var h=function H(B,U){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&U(B[J]))return J;return},k=function H(B,U){for(var J=0;J<B.length;J++)if(U(B[J]))return J;return};function m(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=B.match(H.matchPattern);if(!J)return null;var G=J[0],Y=B.match(H.parsePattern);if(!Y)return null;var I=H.valueCallback?H.valueCallback(Y[0]):Y[0];I=U.valueCallback?U.valueCallback(I):I;var T=B.slice(G.length);return{value:I,rest:T}}}var b=/^(\d+)(ye|yèm)?/i,c=/\d+/i,y={narrow:/^(av\.J\.K|ap\.J\.K|ap\.J\.-K)/i,abbreviated:/^(av\.J\.-K|av\.J-K|apr\.J\.-K|apr\.J-K|ap\.J-K)/i,wide:/^(avan Jezi Kris|apre Jezi Kris)/i},p={any:[/^av/i,/^ap/i]},g={narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](ye|yèm)? trimès/i},d={any:[/1/i,/2/i,/3/i,/4/i]},u={narrow:/^[jfmasond]/i,abbreviated:/^(janv|fevr|mas|avr|me|jen|jiyè|out|sept|okt|nov|des)\.?/i,wide:/^(janvye|fevrye|mas|avril|me|jen|jiyè|out|septanm|oktòb|novanm|desanm)/i},l={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^o/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^av/i,/^me/i,/^je/i,/^ji/i,/^ou/i,/^s/i,/^ok/i,/^n/i,/^d/i]},i={narrow:/^[lmjvsd]/i,short:/^(di|le|ma|me|je|va|sa)/i,abbreviated:/^(dim|len|mad|mèk|jed|van|sam)\.?/i,wide:/^(dimanch|lendi|madi|mèkredi|jedi|vandredi|samdi)/i},n={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^le/i,/^ma/i,/^mè/i,/^je/i,/^va/i,/^sa/i]},s={narrow:/^(a|p|minwit|midi|mat\.?|ap\.?m\.?|swa)/i,any:/^([ap]\.?\s?m\.?|nan maten|nan aprèmidi|nan aswè)/i},o={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/sw/i,night:/nwit/i}},r={ordinalNumber:m({matchPattern:b,parsePattern:c,valueCallback:function H(B){return parseInt(B,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),day:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:s,defaultMatchWidth:"any",parsePatterns:o,defaultParseWidth:"any"})},e={code:"ht",formatDistance:x,formatLong:S,formatRelative:V,localize:F,match:r,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=W(W({},window.dateFns),{},{locale:W(W({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{ht:e})})})();

//# debugId=D403FDF42BCDD5A764756e2164756e21
