var K=function(E){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},K(E)},x=function(E,C){var G=Object.keys(E);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(E);C&&(J=J.filter(function(Y){return Object.getOwnPropertyDescriptor(E,Y).enumerable})),G.push.apply(G,J)}return G},W=function(E){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?x(Object(G),!0).forEach(function(J){XH(E,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(G)):x(Object(G)).forEach(function(J){Object.defineProperty(E,J,Object.getOwnPropertyDescriptor(G,J))})}return E},XH=function(E,C,G){if(C=ZH(C),C in E)Object.defineProperty(E,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else E[C]=G;return E},ZH=function(E){var C=IH(E,"string");return K(C)=="symbol"?C:String(C)},IH=function(E,C){if(K(E)!="object"||!E)return E;var G=E[Symbol.toPrimitive];if(G!==void 0){var J=G.call(E,C||"default");if(K(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(E)};(function(E){var C=Object.defineProperty,G=function T(H,U){for(var B in U)C(H,B,{get:U[B],enumerable:!0,configurable:!0,set:function Z(X){return U[B]=function(){return X}}})},J=function T(H){return H.replace(/sekuntia?/,"sekunnin")},Y=function T(H){return H.replace(/minuuttia?/,"minuutin")},L=function T(H){return H.replace(/tuntia?/,"tunnin")},V=function T(H){return H.replace(/päivää?/,"p\xE4iv\xE4n")},M=function T(H){return H.replace(/(viikko|viikkoa)/,"viikon")},j=function T(H){return H.replace(/(kuukausi|kuukautta)/,"kuukauden")},N=function T(H){return H.replace(/(vuosi|vuotta)/,"vuoden")},v={lessThanXSeconds:{one:"alle sekunti",other:"alle {{count}} sekuntia",futureTense:J},xSeconds:{one:"sekunti",other:"{{count}} sekuntia",futureTense:J},halfAMinute:{one:"puoli minuuttia",other:"puoli minuuttia",futureTense:function T(H){return"puolen minuutin"}},lessThanXMinutes:{one:"alle minuutti",other:"alle {{count}} minuuttia",futureTense:Y},xMinutes:{one:"minuutti",other:"{{count}} minuuttia",futureTense:Y},aboutXHours:{one:"noin tunti",other:"noin {{count}} tuntia",futureTense:L},xHours:{one:"tunti",other:"{{count}} tuntia",futureTense:L},xDays:{one:"p\xE4iv\xE4",other:"{{count}} p\xE4iv\xE4\xE4",futureTense:V},aboutXWeeks:{one:"noin viikko",other:"noin {{count}} viikkoa",futureTense:M},xWeeks:{one:"viikko",other:"{{count}} viikkoa",futureTense:M},aboutXMonths:{one:"noin kuukausi",other:"noin {{count}} kuukautta",futureTense:j},xMonths:{one:"kuukausi",other:"{{count}} kuukautta",futureTense:j},aboutXYears:{one:"noin vuosi",other:"noin {{count}} vuotta",futureTense:N},xYears:{one:"vuosi",other:"{{count}} vuotta",futureTense:N},overXYears:{one:"yli vuosi",other:"yli {{count}} vuotta",futureTense:N},almostXYears:{one:"l\xE4hes vuosi",other:"l\xE4hes {{count}} vuotta",futureTense:N}},w=function T(H,U,B){var Z=v[H],X=U===1?Z.one:Z.other.replace("{{count}}",String(U));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return Z.futureTense(X)+" kuluttua";else return X+" sitten";return X};function $(T){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=H.width?String(H.width):T.defaultWidth,B=T.formats[U]||T.formats[T.defaultWidth];return B}}var P={full:"eeee d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"d.M.y"},_={full:"HH.mm.ss zzzz",long:"HH.mm.ss z",medium:"HH.mm.ss",short:"HH.mm"},F={full:"{{date}} 'klo' {{time}}",long:"{{date}} 'klo' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},k={date:$({formats:P,defaultWidth:"full"}),time:$({formats:_,defaultWidth:"full"}),dateTime:$({formats:F,defaultWidth:"full"})},f={lastWeek:"'viime' eeee 'klo' p",yesterday:"'eilen klo' p",today:"'t\xE4n\xE4\xE4n klo' p",tomorrow:"'huomenna klo' p",nextWeek:"'ensi' eeee 'klo' p",other:"P"},b=function T(H,U,B,Z){return f[H]};function q(T){return function(H,U){var B=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",Z;if(B==="formatting"&&T.formattingValues){var X=T.defaultFormattingWidth||T.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):X;Z=T.formattingValues[I]||T.formattingValues[X]}else{var O=T.defaultWidth,S=U!==null&&U!==void 0&&U.width?String(U.width):T.defaultWidth;Z=T.values[S]||T.values[O]}var Q=T.argumentCallback?T.argumentCallback(H):H;return Z[Q]}}var h={narrow:["eaa.","jaa."],abbreviated:["eaa.","jaa."],wide:["ennen ajanlaskun alkua","j\xE4lkeen ajanlaskun alun"]},m={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartaali","2. kvartaali","3. kvartaali","4. kvartaali"]},D={narrow:["T","H","M","H","T","K","H","E","S","L","M","J"],abbreviated:["tammi","helmi","maalis","huhti","touko","kes\xE4","hein\xE4","elo","syys","loka","marras","joulu"],wide:["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kes\xE4kuu","hein\xE4kuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu"]},c={narrow:D.narrow,abbreviated:D.abbreviated,wide:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kes\xE4kuuta","hein\xE4kuuta","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"]},z={narrow:["S","M","T","K","T","P","L"],short:["su","ma","ti","ke","to","pe","la"],abbreviated:["sunn.","maan.","tiis.","kesk.","torst.","perj.","la"],wide:["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"]},y={narrow:z.narrow,short:z.short,abbreviated:z.abbreviated,wide:["sunnuntaina","maanantaina","tiistaina","keskiviikkona","torstaina","perjantaina","lauantaina"]},p={narrow:{am:"ap",pm:"ip",midnight:"keskiy\xF6",noon:"keskip\xE4iv\xE4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xF6ll\xE4"},abbreviated:{am:"ap",pm:"ip",midnight:"keskiy\xF6",noon:"keskip\xE4iv\xE4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xF6ll\xE4"},wide:{am:"ap",pm:"ip",midnight:"keskiy\xF6ll\xE4",noon:"keskip\xE4iv\xE4ll\xE4",morning:"aamup\xE4iv\xE4ll\xE4",afternoon:"iltap\xE4iv\xE4ll\xE4",evening:"illalla",night:"y\xF6ll\xE4"}},d=function T(H,U){var B=Number(H);return B+"."},g={ordinalNumber:d,era:q({values:h,defaultWidth:"wide"}),quarter:q({values:m,defaultWidth:"wide",argumentCallback:function T(H){return H-1}}),month:q({values:D,defaultWidth:"wide",formattingValues:c,defaultFormattingWidth:"wide"}),day:q({values:z,defaultWidth:"wide",formattingValues:y,defaultFormattingWidth:"wide"}),dayPeriod:q({values:p,defaultWidth:"wide"})};function A(T){return function(H){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.width,Z=B&&T.matchPatterns[B]||T.matchPatterns[T.defaultMatchWidth],X=H.match(Z);if(!X)return null;var I=X[0],O=B&&T.parsePatterns[B]||T.parsePatterns[T.defaultParseWidth],S=Array.isArray(O)?u(O,function(R){return R.test(I)}):l(O,function(R){return R.test(I)}),Q;Q=T.valueCallback?T.valueCallback(S):S,Q=U.valueCallback?U.valueCallback(Q):Q;var JH=H.slice(I.length);return{value:Q,rest:JH}}}var l=function T(H,U){for(var B in H)if(Object.prototype.hasOwnProperty.call(H,B)&&U(H[B]))return B;return},u=function T(H,U){for(var B=0;B<H.length;B++)if(U(H[B]))return B;return};function i(T){return function(H){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=H.match(T.matchPattern);if(!B)return null;var Z=B[0],X=H.match(T.parsePattern);if(!X)return null;var I=T.valueCallback?T.valueCallback(X[0]):X[0];I=U.valueCallback?U.valueCallback(I):I;var O=H.slice(Z.length);return{value:I,rest:O}}}var n=/^(\d+)(\.)/i,s=/\d+/i,o={narrow:/^(e|j)/i,abbreviated:/^(eaa.|jaa.)/i,wide:/^(ennen ajanlaskun alkua|jälkeen ajanlaskun alun)/i},r={any:[/^e/i,/^j/i]},a={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\.? kvartaali/i},e={any:[/1/i,/2/i,/3/i,/4/i]},t={narrow:/^[thmkeslj]/i,abbreviated:/^(tammi|helmi|maalis|huhti|touko|kesä|heinä|elo|syys|loka|marras|joulu)/i,wide:/^(tammikuu|helmikuu|maaliskuu|huhtikuu|toukokuu|kesäkuu|heinäkuu|elokuu|syyskuu|lokakuu|marraskuu|joulukuu)(ta)?/i},HH={narrow:[/^t/i,/^h/i,/^m/i,/^h/i,/^t/i,/^k/i,/^h/i,/^e/i,/^s/i,/^l/i,/^m/i,/^j/i],any:[/^ta/i,/^hel/i,/^maa/i,/^hu/i,/^to/i,/^k/i,/^hei/i,/^e/i,/^s/i,/^l/i,/^mar/i,/^j/i]},TH={narrow:/^[smtkpl]/i,short:/^(su|ma|ti|ke|to|pe|la)/i,abbreviated:/^(sunn.|maan.|tiis.|kesk.|torst.|perj.|la)/i,wide:/^(sunnuntai|maanantai|tiistai|keskiviikko|torstai|perjantai|lauantai)(na)?/i},UH={narrow:[/^s/i,/^m/i,/^t/i,/^k/i,/^t/i,/^p/i,/^l/i],any:[/^s/i,/^m/i,/^ti/i,/^k/i,/^to/i,/^p/i,/^l/i]},BH={narrow:/^(ap|ip|keskiyö|keskipäivä|aamupäivällä|iltapäivällä|illalla|yöllä)/i,any:/^(ap|ip|keskiyöllä|keskipäivällä|aamupäivällä|iltapäivällä|illalla|yöllä)/i},CH={any:{am:/^ap/i,pm:/^ip/i,midnight:/^keskiyö/i,noon:/^keskipäivä/i,morning:/aamupäivällä/i,afternoon:/iltapäivällä/i,evening:/illalla/i,night:/yöllä/i}},EH={ordinalNumber:i({matchPattern:n,parsePattern:s,valueCallback:function T(H){return parseInt(H,10)}}),era:A({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),quarter:A({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"any",valueCallback:function T(H){return H+1}}),month:A({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:HH,defaultParseWidth:"any"}),day:A({matchPatterns:TH,defaultMatchWidth:"wide",parsePatterns:UH,defaultParseWidth:"any"}),dayPeriod:A({matchPatterns:BH,defaultMatchWidth:"any",parsePatterns:CH,defaultParseWidth:"any"})},GH={code:"fi",formatDistance:w,formatLong:k,formatRelative:b,localize:g,match:EH,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=W(W({},window.dateFns),{},{locale:W(W({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{fi:GH})})})();

//# debugId=D62A789500DD20BC64756e2164756e21
