{"version": 3, "file": "TQueue.d.ts", "sourceRoot": "", "sources": ["../../src/TQueue.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AACpC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,OAAO,MAAgC,CAAA;AAEpE;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,OAAO,MAAgC,CAAA;AAEpE;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD;;;GAGG;AACH,MAAM,WAAW,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;CAAG;AAErE;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,UAAU;IAC5E;;OAEG;IACH,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAEjC;;;;;;;;;;;;;;OAcG;IACH,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;CAClD;AAED;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,UAAU;IAC7E;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAEzB;;;OAGG;IACH,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;IAE9C;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAEzB;;;OAGG;IACH,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEnC;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACzC;AAED;;;;;GAKG;AACH,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,QAAQ,IAAI,MAAM,CAAA;IAElB;;;;OAIG;IACH,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAE9B;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAEjC;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAElC;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAEhC;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAErC;;;;OAIG;IACH,QAAQ,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CACtC;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B;;;OAGG;IACH,UAAiB,gBAAgB,CAAC,EAAE,CAAC,CAAC;QACpC,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACzB,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;SACrC,CAAA;KACF;IAED;;;OAGG;IACH,UAAiB,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACrC,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACzB,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAClC,CAAA;KACF;CACF;AAED;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAqB,CAAA;AAE/E;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAuB,CAAA;AAErF;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAuB,CAAA;AAErF;;;;;;;GAOG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAA0B,CAAA;AAE1G;;;;;;;;;GASG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAoB,CAAA;AAE7F;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,MAA0B,CAAA;AAEzF;;;;;;;;GAQG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAqB,CAAA;AAE/F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAoB,CAAA;AAEjG;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAmB,CAAA;AAE/F;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAuB,CAAA;AAEvG;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACnD;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CAC/B,CAAA;AAElB;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IACnE;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;CAC5C,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAiB,CAAA;AAEvE;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAuB,CAAA;AAElG;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAiB,CAAA;AAEtF;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC/D;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CAC5C,CAAA;AAEjB;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAqB,CAAA;AAEhG;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAiB,CAAA;AAE1F;;;;;;;;GAQG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAoB,CAAA;AAE7F;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAiB,CAAA;AAEvE;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAoB,CAAA;AAEpF;;;;;;;GAOG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;;;;;OAOG;IACH,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACvE;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC7C,CAAA;AAExB;;;;;;;GAOG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACxD;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACpC,CAAA;AAElB;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;OAKG;IACH,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC1D;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACnC,CAAA;AAErB;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAsB,CAAA"}