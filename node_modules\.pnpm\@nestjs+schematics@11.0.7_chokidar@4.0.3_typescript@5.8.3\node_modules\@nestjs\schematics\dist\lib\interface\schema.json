{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestInterface", "title": "Nest Interface Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the interface.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the interface?"}, "path": {"type": "string", "format": "path", "description": "The path to create the interface."}, "sourceRoot": {"type": "string", "description": "Nest interface source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}}, "required": ["name"]}