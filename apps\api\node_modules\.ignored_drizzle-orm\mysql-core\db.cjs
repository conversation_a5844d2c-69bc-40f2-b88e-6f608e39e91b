"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var db_exports = {};
__export(db_exports, {
  MySqlDatabase: () => MySqlDatabase,
  withReplicas: () => withReplicas
});
module.exports = __toCommonJS(db_exports);
var import_entity = require("../entity.cjs");
var import_selection_proxy = require("../selection-proxy.cjs");
var import_subquery = require("../subquery.cjs");
var import_query_builders = require("./query-builders/index.cjs");
var import_query = require("./query-builders/query.cjs");
class MySqlDatabase {
  constructor(dialect, session, schema, mode) {
    this.dialect = dialect;
    this.session = session;
    this.mode = mode;
    this._ = schema ? { schema: schema.schema, tableNamesMap: schema.tableNamesMap } : { schema: void 0, tableNamesMap: {} };
    this.query = {};
    if (this._.schema) {
      for (const [tableName, columns] of Object.entries(this._.schema)) {
        this.query[tableName] = new import_query.RelationalQueryBuilder(
          schema.fullSchema,
          this._.schema,
          this._.tableNamesMap,
          schema.fullSchema[tableName],
          columns,
          dialect,
          session,
          this.mode
        );
      }
    }
  }
  static [import_entity.entityKind] = "MySqlDatabase";
  query;
  /**
   * Creates a subquery that defines a temporary named result set as a CTE.
   * 
   * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.
   * 
   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}
   * 
   * @param alias The alias for the subquery.
   * 
   * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.
   * 
   * @example
   * 
   * ```ts
   * // Create a subquery with alias 'sq' and use it in the select query
   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));
   * 
   * const result = await db.with(sq).select().from(sq);
   * ```
   * 
   * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:
   * 
   * ```ts
   * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query
   * const sq = db.$with('sq').as(db.select({
   *   name: sql<string>`upper(${users.name})`.as('name'),
   * })
   * .from(users));
   * 
   * const result = await db.with(sq).select({ name: sq.name }).from(sq);
   * ```
   */
  $with(alias) {
    return {
      as(qb) {
        if (typeof qb === "function") {
          qb = qb(new import_query_builders.QueryBuilder());
        }
        return new Proxy(
          new import_subquery.WithSubquery(qb.getSQL(), qb.getSelectedFields(), alias, true),
          new import_selection_proxy.SelectionProxyHandler({ alias, sqlAliasedBehavior: "alias", sqlBehavior: "error" })
        );
      }
    };
  }
  /**
   * Incorporates a previously defined CTE (using `$with`) into the main query.
   * 
   * This method allows the main query to reference a temporary named result set.
   * 
   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}
   * 
   * @param queries The CTEs to incorporate into the main query.
   * 
   * @example
   * 
   * ```ts
   * // Define a subquery 'sq' as a CTE using $with
   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));
   * 
   * // Incorporate the CTE 'sq' into the main query and select from it
   * const result = await db.with(sq).select().from(sq);
   * ```
   */
  with(...queries) {
    const self = this;
    function select(fields) {
      return new import_query_builders.MySqlSelectBuilder({
        fields: fields ?? void 0,
        session: self.session,
        dialect: self.dialect,
        withList: queries
      });
    }
    function selectDistinct(fields) {
      return new import_query_builders.MySqlSelectBuilder({
        fields: fields ?? void 0,
        session: self.session,
        dialect: self.dialect,
        withList: queries,
        distinct: true
      });
    }
    function update(table) {
      return new import_query_builders.MySqlUpdateBuilder(table, self.session, self.dialect, queries);
    }
    function delete_(table) {
      return new import_query_builders.MySqlDeleteBase(table, self.session, self.dialect, queries);
    }
    return { select, selectDistinct, update, delete: delete_ };
  }
  select(fields) {
    return new import_query_builders.MySqlSelectBuilder({ fields: fields ?? void 0, session: this.session, dialect: this.dialect });
  }
  selectDistinct(fields) {
    return new import_query_builders.MySqlSelectBuilder({
      fields: fields ?? void 0,
      session: this.session,
      dialect: this.dialect,
      distinct: true
    });
  }
  /**
   * Creates an update query.
   * 
   * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.
   * 
   * Use `.set()` method to specify which values to update.
   * 
   * See docs: {@link https://orm.drizzle.team/docs/update} 
   * 
   * @param table The table to update.
   * 
   * @example
   * 
   * ```ts
   * // Update all rows in the 'cars' table
   * await db.update(cars).set({ color: 'red' });
   * 
   * // Update rows with filters and conditions
   * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));
   * ```
   */
  update(table) {
    return new import_query_builders.MySqlUpdateBuilder(table, this.session, this.dialect);
  }
  /**
   * Creates an insert query.
   * 
   * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.
   * 
   * See docs: {@link https://orm.drizzle.team/docs/insert} 
   * 
   * @param table The table to insert into.
   * 
   * @example
   * 
   * ```ts
   * // Insert one row
   * await db.insert(cars).values({ brand: 'BMW' });
   * 
   * // Insert multiple rows
   * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);
   * ```
   */
  insert(table) {
    return new import_query_builders.MySqlInsertBuilder(table, this.session, this.dialect);
  }
  /**
   * Creates a delete query.
   * 
   * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted. 
   * 
   * See docs: {@link https://orm.drizzle.team/docs/delete}
   *  
   * @param table The table to delete from.
   * 
   * @example
   * 
   * ```ts
   * // Delete all rows in the 'cars' table
   * await db.delete(cars);
   * 
   * // Delete rows with filters and conditions
   * await db.delete(cars).where(eq(cars.color, 'green'));
   * ```
   */
  delete(table) {
    return new import_query_builders.MySqlDeleteBase(table, this.session, this.dialect);
  }
  execute(query) {
    return this.session.execute(query.getSQL());
  }
  transaction(transaction, config) {
    return this.session.transaction(transaction, config);
  }
}
const withReplicas = (primary, replicas, getReplica = () => replicas[Math.floor(Math.random() * replicas.length)]) => {
  const select = (...args) => getReplica(replicas).select(...args);
  const selectDistinct = (...args) => getReplica(replicas).selectDistinct(...args);
  const $with = (...args) => getReplica(replicas).with(...args);
  const update = (...args) => primary.update(...args);
  const insert = (...args) => primary.insert(...args);
  const $delete = (...args) => primary.delete(...args);
  const execute = (...args) => primary.execute(...args);
  const transaction = (...args) => primary.transaction(...args);
  return {
    ...primary,
    update,
    insert,
    delete: $delete,
    execute,
    transaction,
    $primary: primary,
    select,
    selectDistinct,
    with: $with,
    get query() {
      return getReplica(replicas).query;
    }
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  MySqlDatabase,
  withReplicas
});
//# sourceMappingURL=db.cjs.map