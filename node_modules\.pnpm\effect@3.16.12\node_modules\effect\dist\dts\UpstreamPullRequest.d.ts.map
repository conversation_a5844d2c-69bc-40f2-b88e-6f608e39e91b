{"version": 3, "file": "UpstreamPullRequest.d.ts", "sourceRoot": "", "sources": ["../../src/UpstreamPullRequest.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,yBAAyB,EAAE,OAAO,MAA2C,CAAA;AAE1F;;;GAGG;AACH,MAAM,MAAM,yBAAyB,GAAG,OAAO,yBAAyB,CAAA;AAExE;;;GAGG;AACH,MAAM,MAAM,mBAAmB,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAA;AAE3D;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,mBAAmB,CAAC;IAC3C;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;YACpC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACpE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;IACvB,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;CAClB;AAED;;;GAGG;AACH,MAAM,WAAW,UAAW,SAAQ,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC;IACrE,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAA;IAC3B,QAAQ,CAAC,qBAAqB,EAAE,MAAM,CAAA;CACvC;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,mBAAmB,CAAC,CAAC,CAAmB,CAAA;AAE9E;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,qBAAqB,EAAE,MAAM,KAAK,mBAAmB,CAAC,KAAK,CAAuB,CAAA;AAE5G;;;;;;GAMG;AACH,eAAO,MAAM,qBAAqB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,mBAAmB,CAAC,OAAO,CAAkC,CAAA;AAEtH;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAqB,CAAA;AAEjG;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,UAAkC,CAAA;AAE1G;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EACJ,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAA;QAClC,QAAQ,CAAC,YAAY,EAAE,CAAC,qBAAqB,EAAE,MAAM,KAAK,CAAC,CAAA;KAC5D,GACC,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACtC;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EACJ,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,EAC5B,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAA;QAClC,QAAQ,CAAC,YAAY,EAAE,CAAC,qBAAqB,EAAE,MAAM,KAAK,CAAC,CAAA;KAC5D,GACC,CAAC,CAAA;CACY,CAAA"}