{"version": 3, "file": "microservice.health.js", "sourceRoot": "", "sources": ["../../../lib/health-indicator/microservice/microservice.health.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAAmD;AAEnD,2BAAkE;AAClE,yCAA4C;AAC5C,8EAAyE;AACzE,uCAMqB;AA0BrB;;;;;;GAMG;AAEI,IAAM,2BAA2B,GAAjC,MAAM,2BAA4B,SAAQ,mBAAe;IAE9D;;OAEG;IACH;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,mBAAmB,GAAG,IAAA,qBAAa,EACtC,CAAC,uBAAuB,CAAC,EACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CACtB,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IAEa,gBAAgB,CAG5B,OAAsE;;YAEtE,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC3E,MAAM,eAAe,GAAG,GAAS,EAAE;gBACjC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;gBACvB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YACvB,CAAC,CAAA,CAAC;YACF,OAAO,MAAM,eAAe,EAAE,CAAC;QACjC,CAAC;KAAA;IAED;;;;;;;OAOG;IACK,aAAa,CAAC,GAAW,EAAE,KAAY,EAAE,OAAe;QAC9D,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,IAAI,KAAK,YAAY,oBAAmB,EAAE;YACxC,MAAM,IAAI,qBAAY,CACpB,OAAO,EACP,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;gBACzB,OAAO,EAAE,cAAc,OAAO,aAAa;aAC5C,CAAC,CACH,CAAC;SACH;QACD,MAAM,IAAI,qCAAgB,CACxB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;YACzB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACG,SAAS,CACb,GAAW,EACX,OAAsE;;YAEtE,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;YAExC,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE;gBAClE,OAAO,CAAC,OAAO;oBACb,gEAAgE;oBAChE,4EAA4E;oBAC5E,gBAAgB,EAAE,IAAI,IACnB,OAAO,CAAC,OAAO,CACnB,CAAC;aACH;YAED,IAAI;gBACF,MAAM,IAAA,sBAAc,EAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC9D,SAAS,GAAG,IAAI,CAAC;aAClB;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,IAAA,eAAO,EAAC,GAAG,CAAC,EAAE;oBAChB,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;iBACvC;gBAED,MAAM,QAAQ,GAAG,GAAG,GAAG,mBAAmB,CAAC;gBAE3C,MAAM,IAAI,qCAAgB,CACxB,QAAQ,EACR,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAClD,CAAC;aACH;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC;KAAA;CACF,CAAA;AA5GY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,EAAC,EAAE,KAAK,EAAE,cAAK,CAAC,SAAS,EAAE,CAAC;;GAC1B,2BAA2B,CA4GvC"}