{"version": 3, "file": "FiberRef.d.ts", "sourceRoot": "", "sources": ["../../src/FiberRef.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAC5C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAI5C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AACtC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,YAAY,MAAM,mBAAmB,CAAA;AACtD,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAClD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,OAAO,MAA4B,CAAA;AAEhE;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;IAavE,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,CAAA;IAClD,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,CAAA;CACpD;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IAClG,QAAQ,CAAC,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAoB,SAAQ,MAAM,CAAC,iBAAiB;IACnE,MAAM,CAAC,EAAE,IAAI,CAAA;CACd;AAED;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAChC,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACzB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;KAChC,CAAA;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EACnB,OAAO,EAAE,CAAC,EACV,OAAO,CAAC,EAAE;IACR,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,CAAA;IACzC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,CAAA;CACvD,KACE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAA6B,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CACnF,CAAA;AAE/B;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,EAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KACxB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAoC,CAAA;AAEvG;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAC7B,OAAO,EAAE,YAAY,CAAC,YAAY,KAC/B,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAyC,CAAA;AAEnH;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,KAAK,EAC7B,OAAO,EAAE,KAAK,EACd,OAAO,CAAC,EAAE;IACR,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,SAAS,CAAA;IACjD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,SAAS,CAAA;CACnE,KACE,QAAQ,CAAC,KAAK,CAA2B,CAAA;AAE9C;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAC/D,CAAA;AAEhC;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAC/D,CAAA;AAEhC;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CACpE,CAAA;AAE3C;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,EACzC,OAAO,EAAE,KAAK,EACd,OAAO,EAAE;IACP,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IAC5C,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAA;IACpB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG,SAAS,CAAA;CAClE,KACE,QAAQ,CAAC,KAAK,CAAgC,CAAA;AAEnD;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAoB,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACtD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CAC1B,CAAA;AAE1B;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC5D;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CAC7B,CAAA;AAE7B;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;OAGG;IACH,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC5E;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACzC,CAAA;AAEjC;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAChG;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACtE,CAAA;AAExB;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACzD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACnC,CAAA;AAEpB,QAAA,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAuB,CAAA;AAElF,OAAO;AACL;;;GAGG;AACH,OAAO,IAAI,MAAM,EAClB,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAsB,CAAA;AAEtF;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC7E;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACpD,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAC5B,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EACjB,GAAG,EAAE,CAAC,EACN,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KACxC,MAAM,CAAC,MAAM,CAAC,CAAC,CAA2B,CAAA;AAE/C;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/D;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACtC,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;OAGG;IACH,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/E;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CAClD,CAAA;AAE3B;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC5D;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CAC7B,CAAA;AAE7B;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;OAGG;IACH,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC5E;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACzC,CAAA;AAEjC;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,QAAQ,CAAC,MAAM,GAAG,WAAW,CAA2B,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,6BAA6B,EAAE,QAAQ,CAAC,OAAO,CAA+B,CAAA;AAE3F;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAA6B,CAAA;AAErF;;;GAGG;AACH,eAAO,MAAM,0BAA0B,EAAE,QAAQ,CAAC,OAAO,CAA6B,CAAA;AAEtF;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAuB,CAAA;AAEnF;;;GAGG;AACH,eAAO,MAAM,yBAAyB,EAAE,QAAQ,CAAC,MAAM,CAAkC,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,wBAAwB,EAAE,QAAQ,CAAC,MAAM,CAAiC,CAAA;AAEvF;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAsC,CAAA;AAEpH;;;GAGG;AACH,eAAO,MAAM,qBAAqB,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAA8B,CAAA;AAE3G;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAA+B,CAAA;AAEjH;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAwB,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAuC,CAAA;AAEtG;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAuB,CAAA;AAEvF;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAoC,CAAA;AAExG;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,QAAQ,CAAC,SAAS,CAAC,SAAS,CAA8B,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAkC,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,CAA4B,CAAA;AAE7G;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAA6B,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,0BAA0B,EAAE,QAAQ,CAAC,OAAO,CAAmC,CAAA;AAE5F;;;GAGG;AACH,eAAO,MAAM,4BAA4B,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CACjD,CAAA;AAEnC;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAA+B,CAAA;AAEzG;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAgC,CAAA"}