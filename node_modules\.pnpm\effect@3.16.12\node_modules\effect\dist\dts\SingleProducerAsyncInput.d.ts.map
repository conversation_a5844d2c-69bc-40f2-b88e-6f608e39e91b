{"version": 3, "file": "SingleProducerAsyncInput.d.ts", "sourceRoot": "", "sources": ["../../src/SingleProducerAsyncInput.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AAGtC;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,WAAW,wBAAwB,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAC5E,SAAQ,kBAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,kBAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;IAEhF,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACtC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;CACxE;AAED;;;;;GAKG;AACH,MAAM,WAAW,kBAAkB,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI;IAC1D,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACnC,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACzC,IAAI,CAAC,OAAO,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC3C,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACvD;AAED;;;;;GAKG;AACH,MAAM,WAAW,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;IAC7D,QAAQ,CAAC,CAAC,EACR,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EACvC,SAAS,EAAE,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,EAC/B,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,KAAK,CAAC,GACzB,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACpB;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAiB,CAAA"}