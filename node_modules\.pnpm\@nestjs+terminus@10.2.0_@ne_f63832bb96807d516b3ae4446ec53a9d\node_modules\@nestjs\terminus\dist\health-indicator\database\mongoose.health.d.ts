import { ModuleRef } from '@nestjs/core';
import { type HealthIndicatorResult } from '../..';
import { HealthIndicator } from '../health-indicator';
export interface MongoosePingCheckSettings {
    /**
     * The connection which the ping check should get executed
     */
    connection?: any;
    /**
     * The amount of time the check should require in ms
     */
    timeout?: number;
}
/**
 * The MongooseHealthIndicator contains health indicators
 * which are used for health checks related to Mongoose
 *
 * @publicApi
 * @module TerminusModule
 */
export declare class MongooseHealthIndicator extends HealthIndicator {
    private moduleRef;
    /**
     * Initializes the MongooseHealthIndicator
     *
     * @param {ModuleRef} moduleRef The NestJS module reference
     */
    constructor(moduleRef: ModuleRef);
    /**
     * Checks if the dependant packages are present
     */
    private checkDependantPackages;
    /**
     * Returns the connection of the current DI context
     */
    private getContextConnection;
    /**
     * Pings a mongoose connection
     * @param connection The connection which the ping should get executed
     * @param timeout The timeout how long the ping should maximum take
     *
     */
    private pingDb;
    /**
     * Checks if the MongoDB responds in (default) 1000ms and
     * returns a result object corresponding to the result
     *
     * @param key The key which will be used for the result object
     * @param options The options for the ping
     * @example
     * mongooseHealthIndicator.pingCheck('mongodb', { timeout: 1500 });
     */
    pingCheck(key: string, options?: MongoosePingCheckSettings): Promise<HealthIndicatorResult>;
}
