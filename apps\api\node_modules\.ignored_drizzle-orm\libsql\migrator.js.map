{"version": 3, "sources": ["../../src/libsql/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport type { LibSQLDatabase } from './driver.ts';\n\nexport function migrate<TSchema extends Record<string, unknown>>(\n\tdb: LibSQLDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\treturn db.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": "AACA,SAAS,0BAA0B;AAG5B,SAAS,QACf,IACA,QACC;AACD,QAAM,aAAa,mBAAmB,MAAM;AAC5C,SAAO,GAAG,QAAQ,QAAQ,YAAY,GAAG,SAAS,MAAM;AACzD;", "names": []}