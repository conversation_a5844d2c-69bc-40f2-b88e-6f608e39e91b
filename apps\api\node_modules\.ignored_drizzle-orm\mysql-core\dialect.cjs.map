{"version": 3, "sources": ["../../src/mysql-core/dialect.ts"], "sourcesContent": ["import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { Param, type QueryWithTypings, SQL, sql, type SQLChunk, View } from '~/sql/sql.ts';\nimport { Subquery, SubqueryConfig } from '~/subquery.ts';\nimport { getTableName, Table } from '~/table.ts';\nimport { orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { DrizzleError, type Name, ViewBaseConfig, and, eq } from '../index.ts';\nimport { MySqlColumn } from './columns/common.ts';\nimport type { MySqlDeleteConfig } from './query-builders/delete.ts';\nimport type { MySqlInsertConfig } from './query-builders/insert.ts';\nimport type { MySqlSelectConfig, MySqlSelectJoinConfig, SelectedFieldsOrdered } from './query-builders/select.types.ts';\nimport type { MySqlUpdateConfig } from './query-builders/update.ts';\nimport type { MySqlSession } from './session.ts';\nimport { MySqlTable } from './table.ts';\nimport { MySqlViewBase } from './view-base.ts';\n\nexport class MySqlDialect {\n\tstatic readonly [entityKind]: string = 'MySqlDialect';\n\t\n\tasync migrate(migrations: MigrationMeta[], session: MySqlSession, config: Omit<MigrationConfig, 'migrationsSchema'>): Promise<void> {\n\t\tconst migrationsTable = config.migrationsTable ?? '__drizzle_migrations';\n\t\tconst migrationTableCreate = sql`\n\t\t\tcreate table if not exists ${sql.identifier(migrationsTable)} (\n\t\t\t\tid serial primary key,\n\t\t\t\thash text not null,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n\t\tawait session.execute(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.all<{ id: number; hash: string; created_at: string }>(\n\t\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsTable)} order by created_at desc limit 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0];\n\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor (const migration of migrations) {\n\t\t\t\tif (\n\t\t\t\t\t!lastDbMigration\n\t\t\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t\t\t) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.execute(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.execute(\n\t\t\t\t\t\tsql`insert into ${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\\`hash\\`, \\`created_at\\`) values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\\`${name}\\``;\n\t}\n\n\tescapeParam(_num: number): string {\n\t\treturn `?`;\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w[SubqueryConfig].alias)} as (${w[SubqueryConfig].sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList }: MySqlDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${returningSql}`;\n\t}\n\n\tbuildUpdateSet(table: MySqlTable, set: UpdateSet): SQL {\n\t\tconst setEntries = Object.entries(set);\n\n\t\tconst setSize = setEntries.length;\n\t\treturn sql.join(\n\t\t\tsetEntries\n\t\t\t\t.flatMap(([colName, value], i): SQL[] => {\n\t\t\t\t\tconst col: MySqlColumn = table[Table.Symbol.Columns][colName]!;\n\t\t\t\t\tconst res = sql`${sql.identifier(col.name)} = ${value}`;\n\t\t\t\t\tif (i < setSize - 1) {\n\t\t\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t\t\t}\n\t\t\t\t\treturn [res];\n\t\t\t\t}),\n\t\t);\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList }: MySqlUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}update ${table} set ${setSql}${whereSql}${returningSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, MySqlColumn)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(c.name);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(sql.identifier(field.name));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tlockingClause,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t}: MySqlSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<MySqlColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table[SubqueryConfig].alias\n\t\t\t\t\t\t: is(table, MySqlViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst distinctSql = distinct ? sql` distinct` : undefined;\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = (() => {\n\t\t\tif (is(table, Table) && table[Table.Symbol.OriginalName] !== table[Table.Symbol.Name]) {\n\t\t\t\treturn sql`${sql.identifier(table[Table.Symbol.OriginalName])} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t\t}\n\n\t\t\treturn table;\n\t\t})();\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tif (joins) {\n\t\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\t\tif (index === 0) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t\tconst table = joinMeta.table;\n\t\t\t\tconst lateralSql = joinMeta.lateral ? sql` lateral` : undefined;\n\n\t\t\t\tif (is(table, MySqlTable)) {\n\t\t\t\t\tconst tableName = table[MySqlTable.Symbol.Name];\n\t\t\t\t\tconst tableSchema = table[MySqlTable.Symbol.Schema];\n\t\t\t\t\tconst origTableName = table[MySqlTable.Symbol.OriginalName];\n\t\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\ttableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t} else if (is(table, View)) {\n\t\t\t\t\tconst viewName = table[ViewBaseConfig].name;\n\t\t\t\t\tconst viewSchema = table[ViewBaseConfig].schema;\n\t\t\t\t\tconst origViewName = table[ViewBaseConfig].originalName;\n\t\t\t\t\tconst alias = viewName === origViewName ? undefined : joinMeta.alias;\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\t\tviewSchema ? sql`${sql.identifier(viewSchema)}.` : undefined\n\t\t\t\t\t\t}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t} else {\n\t\t\t\t\tjoinsArray.push(\n\t\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table} on ${joinMeta.on}`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tif (index < joins.length - 1) {\n\t\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst joinsSql = sql.join(joinsArray);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\torderBySql = sql` order by ${sql.join(orderBy, sql`, `)}`;\n\t\t}\n\n\t\tlet groupBySql;\n\t\tif (groupBy && groupBy.length > 0) {\n\t\t\tgroupBySql = sql` group by ${sql.join(groupBy, sql`, `)}`;\n\t\t}\n\n\t\tconst limitSql = limit ? sql` limit ${limit}` : undefined;\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tlet lockingClausesSql;\n\t\tif (lockingClause) {\n\t\t\tconst { config, strength } = lockingClause;\n\t\t\tlockingClausesSql = sql` for ${sql.raw(strength)}`;\n\t\t\tif (config.noWait) {\n\t\t\t\tlockingClausesSql.append(sql` no wait`);\n\t\t\t} else if (config.skipLocked) {\n\t\t\t\tlockingClausesSql.append(sql` skip locked`);\n\t\t\t}\n\t\t}\n\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClausesSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: MySqlSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: MySqlSelectConfig['setOperators'][number] }): SQL {\n\t\tconst leftChunk = sql`(${leftSelect.getSQL()}) `;\n\t\tconst rightChunk = sql`(${rightSelect.getSQL()})`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid MySql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const orderByUnit of orderBy) {\n\t\t\t\tif (is(orderByUnit, MySqlColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(orderByUnit.name));\n\t\t\t\t} else if (is(orderByUnit, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < orderByUnit.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = orderByUnit.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, MySqlColumn)) {\n\t\t\t\t\t\t\torderByUnit.queryChunks[i] = sql.identifier(chunk.name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${orderByUnit}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${orderByUnit}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n\t\t}\n\n\t\tconst limitSql = limit ? sql` limit ${limit}` : undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery({ table, values, ignore, onConflict }: MySqlInsertConfig): SQL {\n\t\t// const isSingleValue = values.length === 1;\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, MySqlColumn> = table[Table.Symbol.Columns];\n\t\tconst colEntries: [string, MySqlColumn][] = Object.entries(columns);\n\n\t\tconst insertOrder = colEntries.map(([, column]) => sql.identifier(column.name));\n\n\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\tif (col.defaultFn !== undefined) {\n\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\tconst defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalueList.push(sql`default`);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t}\n\t\t\t}\n\t\t\tvaluesSqlList.push(valueList);\n\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t}\n\t\t}\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst ignoreSql = ignore ? sql` ignore` : undefined;\n\n\t\tconst onConflictSql = onConflict ? sql` on duplicate key ${onConflict}` : undefined;\n\n\t\treturn sql`insert${ignoreSql} into ${table} ${insertOrder} values ${valuesSql}${onConflictSql}`;\n\t}\n\n\tsqlToQuery(sql: SQL): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t});\n\t}\n\n\tbuildRelationalQuery({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: MySqlTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<MySqlTable, MySqlColumn> {\n\t\tlet selection: BuildRelationalQueryResult<MySqlTable, MySqlColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: MySqlSelectConfig['orderBy'], where;\n\t\tconst joins: MySqlSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as MySqlColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: MySqlColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as MySqlColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as MySqlColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQuery({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as MySqlTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t\t\t\tjoins.push({\n\t\t\t\t\ton: sql`true`,\n\t\t\t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t\t\t\t\talias: relationTableAlias,\n\t\t\t\t\tjoinType: 'left',\n\t\t\t\t\tlateral: true,\n\t\t\t\t});\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field, tsKey, isJson }) =>\n\t\t\t\t\t\tisJson\n\t\t\t\t\t\t\t? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier('data')}`\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_arrayagg(${field}), json_array())`;\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || (orderBy?.length ?? 0) > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t\t},\n\t\t\t\t\t\t...(((orderBy?.length ?? 0) > 0)\n\t\t\t\t\t\t\t? [{\n\t\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\t\tfield: sql`row_number() over (order by ${sql.join(orderBy!, sql`, `)})`,\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t: []),\n\t\t\t\t\t],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = undefined;\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, MySqlTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n\n\tbuildRelationalQueryWithoutLateralSubqueries({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: MySqlTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<MySqlTable, MySqlColumn> {\n\t\tlet selection: BuildRelationalQueryResult<MySqlTable, MySqlColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: MySqlSelectConfig['orderBy'] = [], where;\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as MySqlColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: MySqlColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as MySqlColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as MySqlColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQueryWithoutLateralSubqueries({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as MySqlTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tlet fieldSql = sql`(${builtRelation.sql})`;\n\t\t\t\tif (is(relation, Many)) {\n\t\t\t\t\tfieldSql = sql`coalesce(${fieldSql}, json_array())`;\n\t\t\t\t}\n\t\t\t\tconst field = fieldSql.as(selectedRelationTsKey);\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({\n\t\t\t\tmessage:\n\t\t\t\t\t`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\"). You need to have at least one item in \"columns\", \"with\" or \"extras\". If you need to select all columns, omit the \"columns\" key or set it to undefined.`,\n\t\t\t});\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field }) =>\n\t\t\t\t\t\tis(field, MySqlColumn) ? sql.identifier(field.name) : is(field, SQL.Aliased) ? field.sql : field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`json_arrayagg(${field})`;\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield,\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t\t},\n\t\t\t\t\t\t...(orderBy.length > 0)\n\t\t\t\t\t\t\t? [{\n\t\t\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\t\t\tfield: sql`row_number() over (order by ${sql.join(orderBy, sql`, `)})`,\n\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t: [],\n\t\t\t\t\t],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = undefined;\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, MySqlTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwG;AACxG,oBAAuB;AACvB,oBAA+B;AAE/B,uBAWO;AACP,iBAA4E;AAC5E,sBAAyC;AACzC,mBAAoC;AACpC,mBAAoD;AACpD,eAAiE;AACjE,oBAA4B;AAM5B,IAAAA,gBAA2B;AAC3B,uBAA8B;AAEvB,MAAM,aAAa;AAAA,EACzB,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAM,QAAQ,YAA6B,SAAuB,QAAkE;AACnI,UAAM,kBAAkB,OAAO,mBAAmB;AAClD,UAAM,uBAAuB;AAAA,gCACC,eAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,UAAM,QAAQ,QAAQ,oBAAoB;AAE1C,UAAM,eAAe,MAAM,QAAQ;AAAA,MAClC,kDAAuC,eAAI,WAAW,eAAe,CAAC;AAAA,IACvE;AAEA,UAAM,kBAAkB,aAAa,CAAC;AAEtC,UAAM,QAAQ,YAAY,OAAO,OAAO;AACvC,iBAAW,aAAa,YAAY;AACnC,YACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,qBAAW,QAAQ,UAAU,KAAK;AACjC,kBAAM,GAAG,QAAQ,eAAI,IAAI,IAAI,CAAC;AAAA,UAC/B;AACA,gBAAM,GAAG;AAAA,YACR,6BACC,eAAI,WAAW,eAAe,CAC/B,sCAAsC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,UAChF;AAAA,QACD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,WAAW,MAAsB;AAChC,WAAO,KAAK,IAAI;AAAA,EACjB;AAAA,EAEA,YAAY,MAAsB;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,aAAa,KAAqB;AACjC,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AAAA,EACnC;AAAA,EAEQ,aAAa,SAAkD;AACtE,QAAI,CAAC,SAAS;AAAQ,aAAO;AAE7B,UAAM,gBAAgB,CAAC,qBAAU;AACjC,eAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACvC,oBAAc,KAAK,iBAAM,eAAI,WAAW,EAAE,8BAAc,EAAE,KAAK,CAAC,QAAQ,EAAE,8BAAc,EAAE,GAAG,GAAG;AAChG,UAAI,IAAI,QAAQ,SAAS,GAAG;AAC3B,sBAAc,KAAK,kBAAO;AAAA,MAC3B;AAAA,IACD;AACA,kBAAc,KAAK,iBAAM;AACzB,WAAO,eAAI,KAAK,aAAa;AAAA,EAC9B;AAAA,EAEA,iBAAiB,EAAE,OAAO,OAAO,WAAW,SAAS,GAA2B;AAC/E,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,eAAe,YAClB,4BAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,wBAAa,KAAK,KAAK;AAEhD,WAAO,iBAAM,OAAO,eAAe,KAAK,GAAG,QAAQ,GAAG,YAAY;AAAA,EACnE;AAAA,EAEA,eAAe,OAAmB,KAAqB;AACtD,UAAM,aAAa,OAAO,QAAQ,GAAG;AAErC,UAAM,UAAU,WAAW;AAC3B,WAAO,eAAI;AAAA,MACV,WACE,QAAQ,CAAC,CAAC,SAAS,KAAK,GAAG,MAAa;AACxC,cAAM,MAAmB,MAAM,mBAAM,OAAO,OAAO,EAAE,OAAO;AAC5D,cAAM,MAAM,iBAAM,eAAI,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK;AACrD,YAAI,IAAI,UAAU,GAAG;AACpB,iBAAO,CAAC,KAAK,eAAI,IAAI,IAAI,CAAC;AAAA,QAC3B;AACA,eAAO,CAAC,GAAG;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EAEA,iBAAiB,EAAE,OAAO,KAAK,OAAO,WAAW,SAAS,GAA2B;AACpF,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,SAAS,KAAK,eAAe,OAAO,GAAG;AAE7C,UAAM,eAAe,YAClB,4BAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,wBAAa,KAAK,KAAK;AAEhD,WAAO,iBAAM,OAAO,UAAU,KAAK,QAAQ,MAAM,GAAG,QAAQ,GAAG,YAAY;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaQ,eACP,QACA,EAAE,gBAAgB,MAAM,IAAiC,CAAC,GACpD;AACN,UAAM,aAAa,OAAO;AAE1B,UAAM,SAAS,OACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1B,YAAM,QAAoB,CAAC;AAE3B,cAAI,kBAAG,OAAO,eAAI,OAAO,KAAK,MAAM,kBAAkB;AACrD,cAAM,KAAK,eAAI,WAAW,MAAM,UAAU,CAAC;AAAA,MAC5C,eAAW,kBAAG,OAAO,eAAI,OAAO,SAAK,kBAAG,OAAO,cAAG,GAAG;AACpD,cAAM,YAAQ,kBAAG,OAAO,eAAI,OAAO,IAAI,MAAM,MAAM;AAEnD,YAAI,eAAe;AAClB,gBAAM;AAAA,YACL,IAAI;AAAA,cACH,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5B,wBAAI,kBAAG,GAAG,yBAAW,GAAG;AACvB,yBAAO,eAAI,WAAW,EAAE,IAAI;AAAA,gBAC7B;AACA,uBAAO;AAAA,cACR,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,OAAO;AACN,gBAAM,KAAK,KAAK;AAAA,QACjB;AAEA,gBAAI,kBAAG,OAAO,eAAI,OAAO,GAAG;AAC3B,gBAAM,KAAK,qBAAU,eAAI,WAAW,MAAM,UAAU,CAAC,EAAE;AAAA,QACxD;AAAA,MACD,eAAW,kBAAG,OAAO,oBAAM,GAAG;AAC7B,YAAI,eAAe;AAClB,gBAAM,KAAK,eAAI,WAAW,MAAM,IAAI,CAAC;AAAA,QACtC,OAAO;AACN,gBAAM,KAAK,KAAK;AAAA,QACjB;AAAA,MACD;AAEA,UAAI,IAAI,aAAa,GAAG;AACvB,cAAM,KAAK,kBAAO;AAAA,MACnB;AAEA,aAAO;AAAA,IACR,CAAC;AAEF,WAAO,eAAI,KAAK,MAAM;AAAA,EACvB;AAAA,EAEA,iBACC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GACM;AACN,UAAM,aAAa,kBAAc,kCAAiC,MAAM;AACxE,eAAW,KAAK,YAAY;AAC3B,cACC,kBAAG,EAAE,OAAO,oBAAM,SACf,2BAAa,EAAE,MAAM,KAAK,WACvB,kBAAG,OAAO,wBAAQ,IACpB,MAAM,8BAAc,EAAE,YACtB,kBAAG,OAAO,8BAAa,IACvB,MAAM,uBAAc,EAAE,WACtB,kBAAG,OAAO,cAAG,IACb,aACA,2BAAa,KAAK,MACnB,EAAE,CAACC,WACL,OAAO;AAAA,QAAK,CAAC,EAAE,MAAM,MACpB,WAAWA,OAAM,mBAAM,OAAO,OAAO,QAAI,2BAAaA,MAAK,IAAIA,OAAM,mBAAM,OAAO,QAAQ;AAAA,MAC3F,GAAG,EAAE,MAAM,KAAK,GAChB;AACD,cAAM,gBAAY,2BAAa,EAAE,MAAM,KAAK;AAC5C,cAAM,IAAI;AAAA,UACT,SACC,EAAE,KAAK,KAAK,IAAI,CACjB,gCAAgC,SAAS,MAAM,EAAE,MAAM,IAAI,qBAAqB,SAAS;AAAA,QAC1F;AAAA,MACD;AAAA,IACD;AAEA,UAAM,gBAAgB,CAAC,SAAS,MAAM,WAAW;AAEjD,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,cAAc,WAAW,4BAAiB;AAEhD,UAAM,YAAY,KAAK,eAAe,YAAY,EAAE,cAAc,CAAC;AAEnE,UAAM,YAAY,MAAM;AACvB,cAAI,kBAAG,OAAO,kBAAK,KAAK,MAAM,mBAAM,OAAO,YAAY,MAAM,MAAM,mBAAM,OAAO,IAAI,GAAG;AACtF,eAAO,iBAAM,eAAI,WAAW,MAAM,mBAAM,OAAO,YAAY,CAAC,CAAC,IAAI,eAAI,WAAW,MAAM,mBAAM,OAAO,IAAI,CAAC,CAAC;AAAA,MAC1G;AAEA,aAAO;AAAA,IACR,GAAG;AAEH,UAAM,aAAoB,CAAC;AAE3B,QAAI,OAAO;AACV,iBAAW,CAAC,OAAO,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAChD,YAAI,UAAU,GAAG;AAChB,qBAAW,KAAK,iBAAM;AAAA,QACvB;AACA,cAAMA,SAAQ,SAAS;AACvB,cAAM,aAAa,SAAS,UAAU,2BAAgB;AAEtD,gBAAI,kBAAGA,QAAO,wBAAU,GAAG;AAC1B,gBAAM,YAAYA,OAAM,yBAAW,OAAO,IAAI;AAC9C,gBAAM,cAAcA,OAAM,yBAAW,OAAO,MAAM;AAClD,gBAAM,gBAAgBA,OAAM,yBAAW,OAAO,YAAY;AAC1D,gBAAM,QAAQ,cAAc,gBAAgB,SAAY,SAAS;AACjE,qBAAW;AAAA,YACV,iBAAM,eAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,cAAc,iBAAM,eAAI,WAAW,WAAW,CAAC,MAAM,MACtD,GAAG,eAAI,WAAW,aAAa,CAAC,GAAG,SAAS,kBAAO,eAAI,WAAW,KAAK,CAAC,EAAE,OAAO,SAAS,EAAE;AAAA,UAC7F;AAAA,QACD,eAAW,kBAAGA,QAAO,eAAI,GAAG;AAC3B,gBAAM,WAAWA,OAAM,uBAAc,EAAE;AACvC,gBAAM,aAAaA,OAAM,uBAAc,EAAE;AACzC,gBAAM,eAAeA,OAAM,uBAAc,EAAE;AAC3C,gBAAM,QAAQ,aAAa,eAAe,SAAY,SAAS;AAC/D,qBAAW;AAAA,YACV,iBAAM,eAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,aAAa,iBAAM,eAAI,WAAW,UAAU,CAAC,MAAM,MACpD,GAAG,eAAI,WAAW,YAAY,CAAC,GAAG,SAAS,kBAAO,eAAI,WAAW,KAAK,CAAC,EAAE,OAAO,SAAS,EAAE;AAAA,UAC5F;AAAA,QACD,OAAO;AACN,qBAAW;AAAA,YACV,iBAAM,eAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IAAIA,MAAK,OAAO,SAAS,EAAE;AAAA,UAC9E;AAAA,QACD;AACA,YAAI,QAAQ,MAAM,SAAS,GAAG;AAC7B,qBAAW,KAAK,iBAAM;AAAA,QACvB;AAAA,MACD;AAAA,IACD;AAEA,UAAM,WAAW,eAAI,KAAK,UAAU;AAEpC,UAAM,WAAW,QAAQ,wBAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,yBAAc,MAAM,KAAK;AAEpD,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,2BAAgB,eAAI,KAAK,SAAS,kBAAO,CAAC;AAAA,IACxD;AAEA,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,2BAAgB,eAAI,KAAK,SAAS,kBAAO,CAAC;AAAA,IACxD;AAEA,UAAM,WAAW,QAAQ,wBAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,yBAAc,MAAM,KAAK;AAEpD,QAAI;AACJ,QAAI,eAAe;AAClB,YAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,0BAAoB,sBAAW,eAAI,IAAI,QAAQ,CAAC;AAChD,UAAI,OAAO,QAAQ;AAClB,0BAAkB,OAAO,wBAAa;AAAA,MACvC,WAAW,OAAO,YAAY;AAC7B,0BAAkB,OAAO,4BAAiB;AAAA,MAC3C;AAAA,IACD;AAEA,UAAM,aACL,iBAAM,OAAO,SAAS,WAAW,IAAI,SAAS,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,iBAAiB;AAEvK,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,KAAK,mBAAmB,YAAY,YAAY;AAAA,IACxD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,mBAAmB,YAAiB,cAAsD;AACzF,UAAM,CAAC,aAAa,GAAG,IAAI,IAAI;AAE/B,QAAI,CAAC,aAAa;AACjB,YAAM,IAAI,MAAM,kDAAkD;AAAA,IACnE;AAEA,QAAI,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,IAC/D;AAGA,WAAO,KAAK;AAAA,MACX,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,MACvD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,uBAAuB;AAAA,IACtB;AAAA,IACA,aAAa,EAAE,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO;AAAA,EACjE,GAAqF;AACpF,UAAM,YAAY,kBAAO,WAAW,OAAO,CAAC;AAC5C,UAAM,aAAa,kBAAO,YAAY,OAAO,CAAC;AAE9C,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,YAAM,gBAAyC,CAAC;AAIhD,iBAAW,eAAe,SAAS;AAClC,gBAAI,kBAAG,aAAa,yBAAW,GAAG;AACjC,wBAAc,KAAK,eAAI,WAAW,YAAY,IAAI,CAAC;AAAA,QACpD,eAAW,kBAAG,aAAa,cAAG,GAAG;AAChC,mBAAS,IAAI,GAAG,IAAI,YAAY,YAAY,QAAQ,KAAK;AACxD,kBAAM,QAAQ,YAAY,YAAY,CAAC;AAEvC,oBAAI,kBAAG,OAAO,yBAAW,GAAG;AAC3B,0BAAY,YAAY,CAAC,IAAI,eAAI,WAAW,MAAM,IAAI;AAAA,YACvD;AAAA,UACD;AAEA,wBAAc,KAAK,iBAAM,WAAW,EAAE;AAAA,QACvC,OAAO;AACN,wBAAc,KAAK,iBAAM,WAAW,EAAE;AAAA,QACvC;AAAA,MACD;AAEA,mBAAa,2BAAgB,eAAI,KAAK,eAAe,kBAAO,CAAC;AAAA,IAC9D;AAEA,UAAM,WAAW,QAAQ,wBAAa,KAAK,KAAK;AAEhD,UAAM,gBAAgB,eAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,EAAE,EAAE;AAE9D,UAAM,YAAY,SAAS,yBAAc,MAAM,KAAK;AAEpD,WAAO,iBAAM,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;AAAA,EACxF;AAAA,EAEA,iBAAiB,EAAE,OAAO,QAAQ,QAAQ,WAAW,GAA2B;AAE/E,UAAM,gBAA8C,CAAC;AACrD,UAAM,UAAuC,MAAM,mBAAM,OAAO,OAAO;AACvE,UAAM,aAAsC,OAAO,QAAQ,OAAO;AAElE,UAAM,cAAc,WAAW,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,eAAI,WAAW,OAAO,IAAI,CAAC;AAE9E,eAAW,CAAC,YAAY,KAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,YAAM,YAAgC,CAAC;AACvC,iBAAW,CAAC,WAAW,GAAG,KAAK,YAAY;AAC1C,cAAM,WAAW,MAAM,SAAS;AAChC,YAAI,aAAa,cAAc,kBAAG,UAAU,gBAAK,KAAK,SAAS,UAAU,QAAY;AAEpF,cAAI,IAAI,cAAc,QAAW;AAChC,kBAAM,kBAAkB,IAAI,UAAU;AACtC,kBAAM,mBAAe,kBAAG,iBAAiB,cAAG,IAAI,kBAAkB,eAAI,MAAM,iBAAiB,GAAG;AAChG,sBAAU,KAAK,YAAY;AAAA,UAC5B,OAAO;AACN,sBAAU,KAAK,uBAAY;AAAA,UAC5B;AAAA,QACD,OAAO;AACN,oBAAU,KAAK,QAAQ;AAAA,QACxB;AAAA,MACD;AACA,oBAAc,KAAK,SAAS;AAC5B,UAAI,aAAa,OAAO,SAAS,GAAG;AACnC,sBAAc,KAAK,kBAAO;AAAA,MAC3B;AAAA,IACD;AAEA,UAAM,YAAY,eAAI,KAAK,aAAa;AAExC,UAAM,YAAY,SAAS,0BAAe;AAE1C,UAAM,gBAAgB,aAAa,mCAAwB,UAAU,KAAK;AAE1E,WAAO,uBAAY,SAAS,SAAS,KAAK,IAAI,WAAW,WAAW,SAAS,GAAG,aAAa;AAAA,EAC9F;AAAA,EAEA,WAAWC,MAA4B;AACtC,WAAOA,KAAI,QAAQ;AAAA,MAClB,YAAY,KAAK;AAAA,MACjB,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,IACpB,CAAC;AAAA,EACF;AAAA,EAEA,qBAAqB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAUwD;AACvD,QAAI,YAA8E,CAAC;AACnF,QAAI,OAAO,QAAQ,SAAuC;AAC1D,UAAM,QAAiC,CAAC;AAExC,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;AAAA,QACL,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,QACP,WAAO,iCAAmB,OAAsB,UAAU;AAAA,QAC1D,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,CAAC;AAAA,MACb,EAAE;AAAA,IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;AAAA,QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,SAAK,iCAAmB,OAAO,UAAU,CAAC,CAAC;AAAA,MACvG;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,oBAAgB,+BAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,gBAAY,qCAAuB,UAAU,UAAU;AAAA,MAChE;AAEA,YAAM,kBAAyE,CAAC;AAChF,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;AAAA,UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;AAAA,YACjB;AACA,4BAAgB,KAAK,KAAK;AAAA,UAC3B;AAAA,QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,QACnF;AAAA,MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;AAAA,MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;AAAA,MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;AAAA,MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,oBAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;AAAA,YACpB;AAAA,YACA,WAAO,4CAA8B,OAAO,UAAU;AAAA,UACvD,CAAC;AAAA,QACF;AAAA,MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;AAAA,UACd,WAAO,kBAAG,OAAO,eAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;AAAA,UAC/E;AAAA,UACA,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,UACnE,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,CAAC;AAAA,QACb,CAAC;AAAA,MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,oBAAgB,sCAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;AAAA,MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,gBAAI,kBAAG,cAAc,oBAAM,GAAG;AAC7B,qBAAO,iCAAmB,cAAc,UAAU;AAAA,QACnD;AACA,mBAAO,qCAAuB,cAAc,UAAU;AAAA,MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb;AAAA,MACD,KAAK,mBACJ;AACD,cAAM,yBAAqB,oCAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,SAAS,gBAAgB,mBAAM,OAAO,IAAI;AACpE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMC,cAAS;AAAA,UACd,GAAG,mBAAmB,OAAO;AAAA,YAAI,CAACC,QAAO,UACxC;AAAA,kBACC,iCAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;AAAA,kBACxE,iCAAmBA,QAAO,UAAU;AAAA,YACrC;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,qBAAqB;AAAA,UAC/C;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,WAAW,mBAAmB;AAAA,UACrC,aAAa,OAAO,mBAAmB;AAAA,UACvC,iBAAa,kBAAG,UAAU,oBAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;AAAA,UACH,YAAY;AAAA,UACZ,QAAAD;AAAA,UACA,qBAAqB;AAAA,QACtB,CAAC;AACD,cAAM,QAAQ,iBAAM,eAAI,WAAW,kBAAkB,CAAC,IAAI,eAAI,WAAW,MAAM,CAAC,GAAG,GAAG,qBAAqB;AAC3G,cAAM,KAAK;AAAA,UACV,IAAI;AAAA,UACJ,OAAO,IAAI,yBAAS,cAAc,KAAY,CAAC,GAAG,kBAAkB;AAAA,UACpE,OAAO;AAAA,UACP,UAAU;AAAA,UACV,SAAS;AAAA,QACV,CAAC;AACD,kBAAU,KAAK;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,cAAc;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,sBAAa,EAAE,SAAS,iCAAiC,YAAY,MAAM,OAAO,UAAU,KAAK,CAAC;AAAA,IAC7G;AAEA,QAAI;AAEJ,gBAAQ,cAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,4BACX,eAAI;AAAA,QACH,UAAU;AAAA,UAAI,CAAC,EAAE,OAAAC,QAAO,OAAO,OAAO,MACrC,SACG,iBAAM,eAAI,WAAW,GAAG,UAAU,IAAI,KAAK,EAAE,CAAC,IAAI,eAAI,WAAW,MAAM,CAAC,SACxE,kBAAGA,QAAO,eAAI,OAAO,IACrBA,OAAM,MACNA;AAAA,QACJ;AAAA,QACA;AAAA,MACD,CACD;AACA,cAAI,kBAAG,qBAAqB,qBAAI,GAAG;AAClC,gBAAQ,wCAA6B,KAAK;AAAA,MAC3C;AACA,YAAM,kBAAkB,CAAC;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,MAAM,GAAG,MAAM;AAAA,QACtB,QAAQ;AAAA,QACR,oBAAoB,YAAY;AAAA,QAChC;AAAA,MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,WAAc,SAAS,UAAU,KAAK;AAE9F,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;AAAA,UAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,UACrC,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,YACX;AAAA,cACC,MAAM,CAAC;AAAA,cACP,OAAO,eAAI,IAAI,GAAG;AAAA,YACnB;AAAA,YACA,IAAM,SAAS,UAAU,KAAK,IAC3B,CAAC;AAAA,cACF,MAAM,CAAC;AAAA,cACP,OAAO,6CAAkC,eAAI,KAAK,SAAU,kBAAO,CAAC;AAAA,YACrE,CAAC,IACC,CAAC;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU;AAAA,MACX,OAAO;AACN,qBAAS,2BAAa,OAAO,UAAU;AAAA,MACxC;AAEA,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,kBAAG,QAAQ,wBAAU,IAAI,SAAS,IAAI,yBAAS,QAAQ,CAAC,GAAG,UAAU;AAAA,QAC5E,QAAQ,CAAC;AAAA,QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;AAAA,UAC/C,MAAM,CAAC;AAAA,UACP,WAAO,kBAAGA,QAAO,oBAAM,QAAI,iCAAmBA,QAAO,UAAU,IAAIA;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,QACrC,QAAQ,CAAC;AAAA,QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UACzC,MAAM,CAAC;AAAA,UACP,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN,YAAY,YAAY;AAAA,MACxB,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AAAA,EAEA,6CAA6C;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAUwD;AACvD,QAAI,YAA8E,CAAC;AACnF,QAAI,OAAO,QAAQ,UAAwC,CAAC,GAAG;AAE/D,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;AAAA,QACL,OAAO,MAAM;AAAA,QACb,OAAO;AAAA,QACP,WAAO,iCAAmB,OAAsB,UAAU;AAAA,QAC1D,oBAAoB;AAAA,QACpB,QAAQ;AAAA,QACR,WAAW,CAAC;AAAA,MACb,EAAE;AAAA,IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;AAAA,QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,SAAK,iCAAmB,OAAO,UAAU,CAAC,CAAC;AAAA,MACvG;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,oBAAgB,+BAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,gBAAY,qCAAuB,UAAU,UAAU;AAAA,MAChE;AAEA,YAAM,kBAAyE,CAAC;AAChF,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;AAAA,UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;AAAA,YACjB;AACA,4BAAgB,KAAK,KAAK;AAAA,UAC3B;AAAA,QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;AAAA,QACnF;AAAA,MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;AAAA,MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;AAAA,MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;AAAA,MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,oBAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;AAAA,YACpB;AAAA,YACA,WAAO,4CAA8B,OAAO,UAAU;AAAA,UACvD,CAAC;AAAA,QACF;AAAA,MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;AAAA,UACd,WAAO,kBAAG,OAAO,eAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;AAAA,UAC/E;AAAA,UACA,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,UACnE,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,CAAC;AAAA,QACb,CAAC;AAAA,MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,oBAAgB,sCAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;AAAA,MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,gBAAI,kBAAG,cAAc,oBAAM,GAAG;AAC7B,qBAAO,iCAAmB,cAAc,UAAU;AAAA,QACnD;AACA,mBAAO,qCAAuB,cAAc,UAAU;AAAA,MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;AAAA,QACL,OAAO;AAAA,QACP,aAAa;AAAA,QACb;AAAA,MACD,KAAK,mBACJ;AACD,cAAM,yBAAqB,oCAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,SAAS,gBAAgB,mBAAM,OAAO,IAAI;AACpE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMD,cAAS;AAAA,UACd,GAAG,mBAAmB,OAAO;AAAA,YAAI,CAACC,QAAO,UACxC;AAAA,kBACC,iCAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;AAAA,kBACxE,iCAAmBA,QAAO,UAAU;AAAA,YACrC;AAAA,UACD;AAAA,QACD;AACA,cAAM,gBAAgB,KAAK,6CAA6C;AAAA,UACvE;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,WAAW,mBAAmB;AAAA,UACrC,aAAa,OAAO,mBAAmB;AAAA,UACvC,iBAAa,kBAAG,UAAU,oBAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;AAAA,UACH,YAAY;AAAA,UACZ,QAAAD;AAAA,UACA,qBAAqB;AAAA,QACtB,CAAC;AACD,YAAI,WAAW,kBAAO,cAAc,GAAG;AACvC,gBAAI,kBAAG,UAAU,qBAAI,GAAG;AACvB,qBAAW,0BAAe,QAAQ;AAAA,QACnC;AACA,cAAM,QAAQ,SAAS,GAAG,qBAAqB;AAC/C,kBAAU,KAAK;AAAA,UACd,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA,oBAAoB;AAAA,UACpB,QAAQ;AAAA,UACR,WAAW,cAAc;AAAA,QAC1B,CAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,sBAAa;AAAA,QACtB,SACC,iCAAiC,YAAY,MAAM,OAAO,UAAU;AAAA,MACtE,CAAC;AAAA,IACF;AAEA,QAAI;AAEJ,gBAAQ,cAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,4BACX,eAAI;AAAA,QACH,UAAU;AAAA,UAAI,CAAC,EAAE,OAAAC,OAAM,UACtB,kBAAGA,QAAO,yBAAW,IAAI,eAAI,WAAWA,OAAM,IAAI,QAAI,kBAAGA,QAAO,eAAI,OAAO,IAAIA,OAAM,MAAMA;AAAA,QAC5F;AAAA,QACA;AAAA,MACD,CACD;AACA,cAAI,kBAAG,qBAAqB,qBAAI,GAAG;AAClC,gBAAQ,+BAAoB,KAAK;AAAA,MAClC;AACA,YAAM,kBAAkB,CAAC;AAAA,QACxB,OAAO;AAAA,QACP,OAAO;AAAA,QACP;AAAA,QACA,QAAQ;AAAA,QACR,oBAAoB,YAAY;AAAA,QAChC;AAAA,MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,UAAa,QAAQ,SAAS;AAEtF,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;AAAA,UAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,UACrC,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,YACX;AAAA,cACC,MAAM,CAAC;AAAA,cACP,OAAO,eAAI,IAAI,GAAG;AAAA,YACnB;AAAA,YACA,GAAI,QAAQ,SAAS,IAClB,CAAC;AAAA,cACF,MAAM,CAAC;AAAA,cACP,OAAO,6CAAkC,eAAI,KAAK,SAAS,kBAAO,CAAC;AAAA,YACpE,CAAC,IACC,CAAC;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU;AAAA,MACX,OAAO;AACN,qBAAS,2BAAa,OAAO,UAAU;AAAA,MACxC;AAEA,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,kBAAG,QAAQ,wBAAU,IAAI,SAAS,IAAI,yBAAS,QAAQ,CAAC,GAAG,UAAU;AAAA,QAC5E,QAAQ,CAAC;AAAA,QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;AAAA,UAC/C,MAAM,CAAC;AAAA,UACP,WAAO,kBAAGA,QAAO,oBAAM,QAAI,iCAAmBA,QAAO,UAAU,IAAIA;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;AAAA,QAC9B,WAAO,2BAAa,OAAO,UAAU;AAAA,QACrC,QAAQ,CAAC;AAAA,QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;AAAA,UACzC,MAAM,CAAC;AAAA,UACP,WAAO,kBAAG,OAAO,oBAAM,QAAI,iCAAmB,OAAO,UAAU,IAAI;AAAA,QACpE,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,CAAC;AAAA,MAChB,CAAC;AAAA,IACF;AAEA,WAAO;AAAA,MACN,YAAY,YAAY;AAAA,MACxB,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;", "names": ["import_table", "table", "sql", "joinOn", "field"]}