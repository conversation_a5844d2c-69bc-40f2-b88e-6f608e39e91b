{"version": 3, "file": "Duration.d.ts", "sourceRoot": "", "sources": ["../../src/Duration.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAGpD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAEnD,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAI7C,QAAA,MAAM,MAAM,EAAE,OAAO,MAAsC,CAAA;AAS3D;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,QAAS,SAAQ,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW;IAClE,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAA;CAC9B;AACD;;;GAGG;AACH,MAAM,MAAM,aAAa,GACrB;IACA,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;CACxB,GACC;IACA,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;CACvB,GACC;IACA,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAA;CAC1B,CAAA;AAEH;;;GAGG;AACH,MAAM,MAAM,IAAI,GACZ,MAAM,GACN,OAAO,GACP,OAAO,GACP,QAAQ,GACR,OAAO,GACP,QAAQ,GACR,QAAQ,GACR,SAAS,GACT,QAAQ,GACR,SAAS,GACT,MAAM,GACN,OAAO,GACP,KAAK,GACL,MAAM,GACN,MAAM,GACN,OAAO,CAAA;AAEX;;;GAGG;AACH,MAAM,MAAM,aAAa,GACrB,QAAQ,GACR,MAAM,GACN,MAAM,GACN,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GACzC,GAAG,MAAM,IAAI,IAAI,EAAE,CAAA;AAIvB;;GAEG;AACH,eAAO,MAAM,MAAM,GAAI,OAAO,aAAa,KAAG,QAmD7C,CAAA;AAED;;GAEG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAuC,CAAA;AAsDzG;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,GAAG,OAAO,KAAG,CAAC,IAAI,QAAkC,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,QAAQ,GAAI,MAAM,QAAQ,KAAG,OAAyC,CAAA;AAEnF;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,MAAM,QAAQ,KAAG,OAYvC,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,QAAkB,CAAA;AAErC;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,QAAyB,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,OAAO,MAAM,KAAG,QAAuB,CAAA;AAE7D;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,QAAQ,MAAM,KAAG,QAAoC,CAAA;AAE5E;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,QAAQ,MAAM,KAAG,QAAwB,CAAA;AAEhE;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,SAAS,MAAM,KAAG,QAAgC,CAAA;AAE1E;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,SAAS,MAAM,KAAG,QAAkC,CAAA;AAE5E;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,OAAO,MAAM,KAAG,QAAmC,CAAA;AAEzE;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,MAAM,MAAM,KAAG,QAAmC,CAAA;AAEvE;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,OAAO,MAAM,KAAG,QAAqC,CAAA;AAE3E;;;GAGG;AACH,eAAO,MAAM,QAAQ,GAAI,MAAM,aAAa,KAAG,MAI3C,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,SAAS,GAAI,MAAM,aAAa,KAAG,MAI5C,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,SAAS,GAAI,MAAM,aAAa,KAAG,MAI5C,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,MAAM,aAAa,KAAG,MAI1C,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,MAAM,aAAa,KAAG,MAIzC,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,MAAM,aAAa,KAAG,MAI1C,CAAA;AAEJ;;;;;;;GAOG;AACH,eAAO,MAAM,OAAO,GAAI,MAAM,aAAa,KAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAUjE,CAAA;AAED;;;;;;;GAOG;AACH,eAAO,MAAM,aAAa,GAAI,MAAM,aAAa,KAAG,MAUnD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,GAAI,MAAM,aAAa,KAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAgB7E,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,CAAA;QACxC,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,CAAA;KACvC,GACA,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA;IACjC;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,IAAI,EAAE,aAAa,EACnB,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,CAAA;QACxC,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,CAAA;KACvC,GACA,CAAC,GAAG,CAAC,CAAA;CAiBR,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,IAAI,EAAE,aAAa,EACnB,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,CAAA;QACpD,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,CAAA;KACpD,GACA,CAAC,IAAI,EAAE,aAAa,KAAK,CAAC,GAAG,CAAC,CAAA;IACjC;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,IAAI,EAAE,aAAa,EACnB,IAAI,EAAE,aAAa,EACnB,OAAO,EAAE;QACP,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,CAAA;QACpD,QAAQ,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,CAAA;KACpD,GACA,CAAC,GAAG,CAAC,CAAA;CA8BR,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAKvC,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CACE,OAAO,EAAE;QACP,OAAO,EAAE,aAAa,CAAA;QACtB,OAAO,EAAE,aAAa,CAAA;KACvB,GACA,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACnC;;;;;OAKG;IACH,CACE,IAAI,EAAE,aAAa,EACnB,OAAO,EAAE;QACP,OAAO,EAAE,aAAa,CAAA;QACtB,OAAO,EAAE,aAAa,CAAA;KACvB,GACA,OAAO,CAAA;CACoC,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,QAAQ,CAItD,CAAA;AAIJ;;GAEG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;OAEG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IACxD;;OAEG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,QAAQ,CAAA;CAC+C,CAAA;AAIrG;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IACxD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,QAAQ,CAAA;CAC+C,CAAA;AAIrG;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;OAGG;IACH,CACE,OAAO,EAAE;QACP,OAAO,EAAE,aAAa,CAAA;QACtB,OAAO,EAAE,aAAa,CAAA;KACvB,GACA,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IACpC;;;OAGG;IACH,CACE,IAAI,EAAE,aAAa,EACnB,OAAO,EAAE;QACP,OAAO,EAAE,aAAa,CAAA;QACtB,OAAO,EAAE,aAAa,CAAA;KACvB,GACA,QAAQ,CAAA;CAWZ,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,EAAE,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAC9D;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;CAsB3D,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,EAAE,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IAC/C;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,MAAM,GAAG,QAAQ,CAAA;CAe5C,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;OAGG;IACH,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IAClD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,GAAG,QAAQ,CAAA;CAQ/C,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IACxD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,QAAQ,CAAA;CAQrD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,QAAQ,CAAA;IACxD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,QAAQ,CAAA;CAQrD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACvD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAA;CAQpD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE;IAC9B;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACvD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAA;CAQpD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACvD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAA;CAQpD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE;IACjC;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACvD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAA;CAQpD,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,aAAa,KAAK,OAAO,CAAA;IACvD;;;OAGG;IACH,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,GAAG,OAAO,CAAA;CACsD,CAAA;AAE3G;;;;;GAKG;AACH,eAAO,MAAM,KAAK,GAAI,MAAM,aAAa,KAAG;IAC1C,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;IACf,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;CA6Bd,CAAA;AAED;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,MAAM,GAAI,MAAM,aAAa,KAAG,MAoC5C,CAAA;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,eAAO,MAAM,eAAe,GAAI,MAAM,aAAa,KAAG,MA0DrD,CAAA;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,eAAO,MAAM,SAAS,GAAI,MAAM,aAAa,KAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAGnE,CAAA;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,OAAO,GAAI,KAAK,MAAM,KAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAgB3D,CAAA"}