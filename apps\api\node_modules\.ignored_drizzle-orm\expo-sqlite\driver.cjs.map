{"version": 3, "sources": ["../../src/expo-sqlite/driver.ts"], "sourcesContent": ["import type { SQLiteDatabase, SQLiteRunResult } from 'expo-sqlite/next';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n    createTableRelationsHelpers,\n    extractTablesRelationalConfig,\n    type RelationalSchemaConfig,\n    type TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { ExpoSQLiteSession } from './session.ts';\n\nexport type ExpoSQLiteDatabase<\n    TSchema extends Record<string, unknown> = Record<string, never>,\n> = BaseSQLiteDatabase<'sync', SQLiteRunResult, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n    client: SQLiteDatabase,\n    config: DrizzleConfig<TSchema> = {},\n): ExpoSQLiteDatabase<TSchema> {\n    const dialect = new SQLiteSyncDialect();\n    let logger;\n    if (config.logger === true) {\n        logger = new DefaultLogger();\n    } else if (config.logger !== false) {\n        logger = config.logger;\n    }\n\n    let schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n    if (config.schema) {\n        const tablesConfig = extractTablesRelationalConfig(\n            config.schema,\n            createTableRelationsHelpers,\n        );\n        schema = {\n            fullSchema: config.schema,\n            schema: tablesConfig.tables,\n            tableNamesMap: tablesConfig.tableNamesMap,\n        };\n    }\n\n    const session = new ExpoSQLiteSession(client, dialect, schema, { logger });\n    return new BaseSQLiteDatabase('sync', dialect, session, schema) as ExpoSQLiteDatabase<TSchema>;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA8B;AAC9B,uBAKO;AACP,gBAAmC;AACnC,qBAAkC;AAElC,qBAAkC;AAM3B,SAAS,QACZ,QACA,SAAiC,CAAC,GACP;AAC3B,QAAM,UAAU,IAAI,iCAAkB;AACtC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AACxB,aAAS,IAAI,4BAAc;AAAA,EAC/B,WAAW,OAAO,WAAW,OAAO;AAChC,aAAS,OAAO;AAAA,EACpB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AACf,UAAM,mBAAe;AAAA,MACjB,OAAO;AAAA,MACP;AAAA,IACJ;AACA,aAAS;AAAA,MACL,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAChC;AAAA,EACJ;AAEA,QAAM,UAAU,IAAI,iCAAkB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACzE,SAAO,IAAI,6BAAmB,QAAQ,SAAS,SAAS,MAAM;AAClE;", "names": []}