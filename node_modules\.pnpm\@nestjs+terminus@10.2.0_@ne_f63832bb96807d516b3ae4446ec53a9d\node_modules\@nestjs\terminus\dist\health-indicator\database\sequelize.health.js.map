{"version": 3, "file": "sequelize.health.js", "sourceRoot": "", "sources": ["../../../lib/health-indicator/database/sequelize.health.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAAmD;AACnD,uCAAyC;AAEzC,6BAIe;AACf,8EAAyE;AACzE,uCAIqB;AACrB,0DAAsD;AAatD;;;;;;GAMG;AAEI,IAAM,wBAAwB,GAA9B,MAAM,wBAAyB,SAAQ,kCAAe;IAC3D;;;;OAIG;IACH,YAAoB,SAAoB;QACtC,KAAK,EAAE,CAAC;QADU,cAAS,GAAT,SAAS,CAAW;QAEtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAA,qBAAa,EAAC,CAAC,mBAAmB,EAAE,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,EAAE,kBAAkB,EAAE;QAC1B,8DAA8D;QAC9D,OAAO,CAAC,+CAA+C,CAA2B,CAAC;QAErF,IAAI;YACF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAY,EAAE;gBACxD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;SACJ;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED;;;;;OAKG;IACW,MAAM,CAAC,UAAe,EAAE,OAAe;;YACnD,MAAM,KAAK,GAAiB,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACzD,OAAO,MAAM,IAAA,sBAAc,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;KAAA;IAED;;;;;;;;OAQG;IACU,SAAS,CACpB,GAAW,EACX,UAAsC,EAAE;;YAExC,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACrE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;YAExC,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,IAAI,2BAAuB,CAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;oBAC7B,OAAO,EAAE,sDAAsD;iBAChE,CAAC,CACH,CAAC;aACH;YAED,IAAI;gBACF,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACvC,SAAS,GAAG,IAAI,CAAC;aAClB;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,GAAG,YAAY,oBAAmB,EAAE;oBACtC,MAAM,IAAI,gBAAY,CACpB,OAAO,EACP,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;wBAC7B,OAAO,EAAE,cAAc,OAAO,aAAa;qBAC5C,CAAC,CACH,CAAC;iBACH;aACF;YAED,IAAI,SAAS,EAAE;gBACb,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;aACvC;iBAAM;gBACL,MAAM,IAAI,qCAAgB,CACxB,GAAG,GAAG,mBAAmB,EACzB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAC/B,CAAC;aACH;QACH,CAAC;KAAA;CACF,CAAA;AAhGY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,EAAC,EAAE,KAAK,EAAE,cAAK,CAAC,SAAS,EAAE,CAAC;qCAON,gBAAS;GAN7B,wBAAwB,CAgGpC"}