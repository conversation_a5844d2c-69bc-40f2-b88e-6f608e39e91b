{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestMiddleware", "title": "Nest Middleware Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the middleware.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the middleware?"}, "path": {"type": "string", "format": "path", "description": "The path to create the middleware."}, "language": {"type": "string", "description": "Nest middleware language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest middleware source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}