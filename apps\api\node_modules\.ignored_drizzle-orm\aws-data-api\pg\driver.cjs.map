{"version": 3, "sources": ["../../../src/aws-data-api/pg/driver.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { AwsDataApiClient, AwsDataApiPgQueryResultHKT } from './session.ts';\nimport { AwsDataApiSession } from './session.ts';\n\nexport interface PgDriverOptions {\n\tlogger?: Logger;\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport interface DrizzleAwsDataApiPgConfig<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends DrizzleConfig<TSchema> {\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport type AwsDataApiPgDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = PgDatabase<AwsDataApiPgQueryResultHKT, TSchema>;\n\nexport class AwsPgDialect extends PgDialect {\n\tstatic readonly [entityKind]: string = 'AwsPgDialect';\n\n\toverride escapeParam(num: number): string {\n\t\treturn `:${num + 1}`;\n\t}\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: AwsDataApiClient,\n\tconfig: DrizzleAwsDataApiPgConfig<TSchema>,\n): AwsDataApiPgDatabase<TSchema> {\n\tconst dialect = new AwsPgDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new AwsDataApiSession(client, dialect, schema, { ...config, logger }, undefined);\n\treturn new PgDatabase(dialect, session, schema) as AwsDataApiPgDatabase<TSchema>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAC1B,uBAKO;AAGP,qBAAkC;AAqB3B,MAAM,qBAAqB,yBAAU;AAAA,EAC3C,QAAiB,wBAAU,IAAY;AAAA,EAE9B,YAAY,KAAqB;AACzC,WAAO,IAAI,MAAM,CAAC;AAAA,EACnB;AACD;AAEO,SAAS,QACf,QACA,QACgC;AAChC,QAAM,UAAU,IAAI,aAAa;AACjC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,iCAAkB,QAAQ,SAAS,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG,MAAS;AAC/F,SAAO,IAAI,qBAAW,SAAS,SAAS,MAAM;AAC/C;", "names": []}