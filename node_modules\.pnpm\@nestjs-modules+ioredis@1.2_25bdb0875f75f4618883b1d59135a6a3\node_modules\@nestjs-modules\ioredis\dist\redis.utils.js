"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRedisConnection = exports.getRedisConnectionToken = exports.getRedisOptionsToken = void 0;
const ioredis_1 = require("ioredis");
const redis_constants_1 = require("./redis.constants");
function getRedisOptionsToken(connection) {
    return `${connection || redis_constants_1.REDIS_MODULE_CONNECTION}_${redis_constants_1.REDIS_MODULE_OPTIONS_TOKEN}`;
}
exports.getRedisOptionsToken = getRedisOptionsToken;
function getRedisConnectionToken(connection) {
    return `${connection || redis_constants_1.REDIS_MODULE_CONNECTION}_${redis_constants_1.REDIS_MODULE_CONNECTION_TOKEN}`;
}
exports.getRedisConnectionToken = getRedisConnectionToken;
function createRedisConnection(options) {
    const { config } = options;
    if (config.url) {
        return new ioredis_1.default(config.url, config);
    }
    else {
        return new ioredis_1.default(config);
    }
}
exports.createRedisConnection = createRedisConnection;
