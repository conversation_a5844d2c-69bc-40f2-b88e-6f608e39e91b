{"version": 3, "sources": ["../../src/mysql2/session.ts"], "sourcesContent": ["import type { Connection as CallbackConnection } from 'mysql2';\nimport type {\n\tConnection,\n\tFieldPacket,\n\tOkPacket,\n\tPool,\n\tPoolConnection,\n\tQueryOptions,\n\tResultSetHeader,\n\tRowDataPacket,\n} from 'mysql2/promise';\nimport { once } from 'node:events';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type { SelectedFieldsOrdered } from '~/mysql-core/query-builders/select.types.ts';\nimport {\n\ttype Mode,\n\tMySqlSession,\n\tMySqlTransaction,\n\ttype MySqlTransactionConfig,\n\tPreparedQuery,\n\ttype PreparedQueryConfig,\n\ttype PreparedQueryHKT,\n\ttype PreparedQueryKind,\n\ttype QueryResultHKT,\n} from '~/mysql-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport type MySql2Client = Pool | Connection;\n\nexport type MySqlRawQueryResult = [ResultSetHeader, FieldPacket[]];\nexport type MySqlQueryResultType = RowDataPacket[][] | RowDataPacket[] | OkPacket | OkPacket[] | ResultSetHeader;\nexport type MySqlQueryResult<\n\tT = any,\n> = [T extends ResultSetHeader ? T : T[], FieldPacket[]];\n\nexport class MySql2PreparedQuery<T extends PreparedQueryConfig> extends PreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'MySql2PreparedQuery';\n\n\tprivate rawQuery: QueryOptions;\n\tprivate query: QueryOptions;\n\n\tconstructor(\n\t\tprivate client: MySql2Client,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper();\n\t\tthis.rawQuery = {\n\t\t\tsql: queryString,\n\t\t\t// rowsAsArray: true,\n\t\t\ttypeCast: function(field: any, next: any) {\n\t\t\t\tif (field.type === 'TIMESTAMP' || field.type === 'DATETIME' || field.type === 'DATE') {\n\t\t\t\t\treturn field.string();\n\t\t\t\t}\n\t\t\t\treturn next();\n\t\t\t},\n\t\t};\n\t\tthis.query = {\n\t\t\tsql: queryString,\n\t\t\trowsAsArray: true,\n\t\t\ttypeCast: function(field: any, next: any) {\n\t\t\t\tif (field.type === 'TIMESTAMP' || field.type === 'DATETIME' || field.type === 'DATE') {\n\t\t\t\t\treturn field.string();\n\t\t\t\t}\n\t\t\t\treturn next();\n\t\t\t},\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.rawQuery.sql, params);\n\n\t\tconst { fields, client, rawQuery, query, joinsNotNullableMap, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client.query(rawQuery, params);\n\t\t}\n\n\t\tconst result = await client.query<any[]>(query, params);\n\t\tconst rows = result[0];\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tasync *iterator(\n\t\tplaceholderValues: Record<string, unknown> = {},\n\t): AsyncGenerator<T['execute'] extends any[] ? T['execute'][number] : T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tconst conn = ((isPool(this.client) ? await this.client.getConnection() : this.client) as {} as {\n\t\t\tconnection: CallbackConnection;\n\t\t}).connection;\n\n\t\tconst { fields, query, rawQuery, joinsNotNullableMap, client, customResultMapper } = this;\n\t\tconst hasRowsMapper = Boolean(fields || customResultMapper);\n\t\tconst driverQuery = hasRowsMapper ? conn.query(query, params) : conn.query(rawQuery, params);\n\n\t\tconst stream = driverQuery.stream();\n\n\t\tfunction dataListener() {\n\t\t\tstream.pause();\n\t\t}\n\n\t\tstream.on('data', dataListener);\n\n\t\ttry {\n\t\t\tconst onEnd = once(stream, 'end');\n\t\t\tconst onError = once(stream, 'error');\n\n\t\t\twhile (true) {\n\t\t\t\tstream.resume();\n\t\t\t\tconst row = await Promise.race([onEnd, onError, new Promise((resolve) => stream.once('data', resolve))]);\n\t\t\t\tif (row === undefined || (Array.isArray(row) && row.length === 0)) {\n\t\t\t\t\tbreak;\n\t\t\t\t} else if (row instanceof Error) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t\tthrow row;\n\t\t\t\t} else {\n\t\t\t\t\tif (hasRowsMapper) {\n\t\t\t\t\t\tif (customResultMapper) {\n\t\t\t\t\t\t\tconst mappedRow = customResultMapper([row as unknown[]]);\n\t\t\t\t\t\t\tyield (Array.isArray(mappedRow) ? mappedRow[0] : mappedRow);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tyield mapResultRow(fields!, row as unknown[], joinsNotNullableMap);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tyield row as T['execute'];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t} finally {\n\t\t\tstream.off('data', dataListener);\n\t\t\tif (isPool(client)) {\n\t\t\t\tconn.end();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport interface MySql2SessionOptions {\n\tlogger?: Logger;\n\tmode: Mode;\n}\n\nexport class MySql2Session<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlSession<MySql2QueryResultHKT, MySql2PreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'MySql2Session';\n\n\tprivate logger: Logger;\n\tprivate mode: Mode;\n\n\tconstructor(\n\t\tprivate client: MySql2Client,\n\t\tdialect: MySqlDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: MySql2SessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.mode = options.mode;\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PreparedQueryKind<MySql2PreparedQueryHKT, T> {\n\t\treturn new MySql2PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t) as PreparedQueryKind<MySql2PreparedQueryHKT, T>;\n\t}\n\n\t/**\n\t * @internal\n\t * What is its purpose?\n\t */\n\tasync query(query: string, params: unknown[]): Promise<MySqlQueryResult> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.query({\n\t\t\tsql: query,\n\t\t\tvalues: params,\n\t\t\trowsAsArray: true,\n\t\t\ttypeCast: function(field: any, next: any) {\n\t\t\t\tif (field.type === 'TIMESTAMP' || field.type === 'DATETIME' || field.type === 'DATE') {\n\t\t\t\t\treturn field.string();\n\t\t\t\t}\n\t\t\t\treturn next();\n\t\t\t},\n\t\t});\n\t\treturn result;\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\t\treturn this.client.execute(querySql.sql, querySql.params).then((result) => result[0]) as Promise<T[]>;\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: MySql2Transaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: MySqlTransactionConfig,\n\t): Promise<T> {\n\t\tconst session = isPool(this.client)\n\t\t\t? new MySql2Session(await this.client.getConnection(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new MySql2Transaction(\n\t\t\tthis.dialect,\n\t\t\tsession as MySqlSession<any, any, any, any>,\n\t\t\tthis.schema,\n\t\t\t0,\n\t\t\tthis.mode,\n\t\t);\n\t\tif (config) {\n\t\t\tconst setTransactionConfigSql = this.getSetTransactionSQL(config);\n\t\t\tif (setTransactionConfigSql) {\n\t\t\t\tawait tx.execute(setTransactionConfigSql);\n\t\t\t}\n\t\t\tconst startTransactionSql = this.getStartTransactionSQL(config);\n\t\t\tawait (startTransactionSql ? tx.execute(startTransactionSql) : tx.execute(sql`begin`));\n\t\t} else {\n\t\t\tawait tx.execute(sql`begin`);\n\t\t}\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow err;\n\t\t} finally {\n\t\t\tif (isPool(this.client)) {\n\t\t\t\t(session.client as PoolConnection).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class MySql2Transaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlTransaction<MySql2QueryResultHKT, MySql2PreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'MySql2Transaction';\n\n\toverride async transaction<T>(transaction: (tx: MySql2Transaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new MySql2Transaction(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t\tthis.mode,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nfunction isPool(client: MySql2Client): client is Pool {\n\treturn 'getConnection' in client;\n}\n\nexport interface MySql2QueryResultHKT extends QueryResultHKT {\n\ttype: MySqlRawQueryResult;\n}\n\nexport interface MySql2PreparedQueryHKT extends PreparedQueryHKT {\n\ttype: MySql2PreparedQuery<Assume<this['config'], PreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,yBAAqB;AACrB,oBAA2B;AAE3B,oBAA2B;AAG3B,qBAUO;AAEP,iBAA4D;AAC5D,mBAA0C;AAUnC,MAAM,4BAA2D,6BAAiB;AAAA,EAMxF,YACS,QACR,aACQ,QACA,QACA,QACA,oBACP;AACD,UAAM;AAPE;AAEA;AACA;AACA;AACA;AAGR,SAAK,WAAW;AAAA,MACf,KAAK;AAAA;AAAA,MAEL,UAAU,SAAS,OAAY,MAAW;AACzC,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,cAAc,MAAM,SAAS,QAAQ;AACrF,iBAAO,MAAM,OAAO;AAAA,QACrB;AACA,eAAO,KAAK;AAAA,MACb;AAAA,IACD;AACA,SAAK,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL,aAAa;AAAA,MACb,UAAU,SAAS,OAAY,MAAW;AACzC,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,cAAc,MAAM,SAAS,QAAQ;AACrF,iBAAO,MAAM,OAAO;AAAA,QACrB;AACA,eAAO,KAAK;AAAA,MACb;AAAA,IACD;AAAA,EACD;AAAA,EAlCA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAiCR,MAAM,QAAQ,oBAA6C,CAAC,GAA0B;AACrF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,SAAS,KAAK,MAAM;AAE9C,UAAM,EAAE,QAAQ,QAAQ,UAAU,OAAO,qBAAqB,mBAAmB,IAAI;AACrF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAM,UAAU,MAAM;AAAA,IACrC;AAEA,UAAM,SAAS,MAAM,OAAO,MAAa,OAAO,MAAM;AACtD,UAAM,OAAO,OAAO,CAAC;AAErB,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAEA,OAAO,SACN,oBAA6C,CAAC,GACqC;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,UAAM,QAAS,OAAO,KAAK,MAAM,IAAI,MAAM,KAAK,OAAO,cAAc,IAAI,KAAK,QAE3E;AAEH,UAAM,EAAE,QAAQ,OAAO,UAAU,qBAAqB,QAAQ,mBAAmB,IAAI;AACrF,UAAM,gBAAgB,QAAQ,UAAU,kBAAkB;AAC1D,UAAM,cAAc,gBAAgB,KAAK,MAAM,OAAO,MAAM,IAAI,KAAK,MAAM,UAAU,MAAM;AAE3F,UAAM,SAAS,YAAY,OAAO;AAElC,aAAS,eAAe;AACvB,aAAO,MAAM;AAAA,IACd;AAEA,WAAO,GAAG,QAAQ,YAAY;AAE9B,QAAI;AACH,YAAM,YAAQ,yBAAK,QAAQ,KAAK;AAChC,YAAM,cAAU,yBAAK,QAAQ,OAAO;AAEpC,aAAO,MAAM;AACZ,eAAO,OAAO;AACd,cAAM,MAAM,MAAM,QAAQ,KAAK,CAAC,OAAO,SAAS,IAAI,QAAQ,CAAC,YAAY,OAAO,KAAK,QAAQ,OAAO,CAAC,CAAC,CAAC;AACvG,YAAI,QAAQ,UAAc,MAAM,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAI;AAClE;AAAA,QACD,WAAW,eAAe,OAAO;AAChC,gBAAM;AAAA,QACP,OAAO;AACN,cAAI,eAAe;AAClB,gBAAI,oBAAoB;AACvB,oBAAM,YAAY,mBAAmB,CAAC,GAAgB,CAAC;AACvD,oBAAO,MAAM,QAAQ,SAAS,IAAI,UAAU,CAAC,IAAI;AAAA,YAClD,OAAO;AACN,wBAAM,2BAAa,QAAS,KAAkB,mBAAmB;AAAA,YAClE;AAAA,UACD,OAAO;AACN,kBAAM;AAAA,UACP;AAAA,QACD;AAAA,MACD;AAAA,IACD,UAAE;AACD,aAAO,IAAI,QAAQ,YAAY;AAC/B,UAAI,OAAO,MAAM,GAAG;AACnB,aAAK,IAAI;AAAA,MACV;AAAA,IACD;AAAA,EACD;AACD;AAOO,MAAM,sBAGH,4BAAiF;AAAA,EAM1F,YACS,QACR,SACQ,QACA,SACP;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA,EAdA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,oBAC+C;AAC/C,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,OAAe,QAA8C;AACxE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,MAAM;AAAA,MACtC,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,UAAU,SAAS,OAAY,MAAW;AACzC,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,cAAc,MAAM,SAAS,QAAQ;AACrF,iBAAO,MAAM,OAAO;AAAA,QACrB;AACA,eAAO,KAAK;AAAA,MACb;AAAA,IACD,CAAC;AACD,WAAO;AAAA,EACR;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAClD,WAAO,KAAK,OAAO,QAAQ,SAAS,KAAK,SAAS,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,CAAC,CAAC;AAAA,EACrF;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,UAAU,OAAO,KAAK,MAAM,IAC/B,IAAI,cAAc,MAAM,KAAK,OAAO,cAAc,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IAC5F;AACH,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN;AACA,QAAI,QAAQ;AACX,YAAM,0BAA0B,KAAK,qBAAqB,MAAM;AAChE,UAAI,yBAAyB;AAC5B,cAAM,GAAG,QAAQ,uBAAuB;AAAA,MACzC;AACA,YAAM,sBAAsB,KAAK,uBAAuB,MAAM;AAC9D,aAAO,sBAAsB,GAAG,QAAQ,mBAAmB,IAAI,GAAG,QAAQ,qBAAU;AAAA,IACrF,OAAO;AACN,YAAM,GAAG,QAAQ,qBAAU;AAAA,IAC5B;AACA,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,sBAAW;AAC5B,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,wBAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,OAAO,KAAK,MAAM,GAAG;AACxB,QAAC,QAAQ,OAA0B,QAAQ;AAAA,MAC5C;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,gCAAqF;AAAA,EAC9F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAe,YAAe,aAAsF;AACnH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,MACnB,KAAK;AAAA,IACN;AACA,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEA,SAAS,OAAO,QAAsC;AACrD,SAAO,mBAAmB;AAC3B;", "names": []}