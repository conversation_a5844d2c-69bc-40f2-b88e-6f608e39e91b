{"version": 3, "file": "RuntimeFlags.d.ts", "sourceRoot": "", "sources": ["../../src/RuntimeFlags.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAG1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,iBAAiB,MAAM,wBAAwB,CAAA;AAEhE;;;;;;;GAOG;AACH,MAAM,MAAM,YAAY,GAAG,MAAM,GAAG;IAClC,QAAQ,CAAC,YAAY,EAAE,OAAO,MAAM,CAAA;CACrC,CAAA;AAED;;;;;;GAMG;AACH,MAAM,MAAM,WAAW,GAAG,MAAM,GAAG;IACjC,QAAQ,CAAC,WAAW,EAAE,OAAO,MAAM,CAAA;CACpC,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,WAA2B,CAAA;AAE9C;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,WAAmC,CAAA;AAE9D;;;;;;;;GAQG;AACH,eAAO,MAAM,aAAa,EAAE,WAAoC,CAAA;AAEhE;;;;;;;;;GASG;AACH,eAAO,MAAM,cAAc,EAAE,WAAqC,CAAA;AAElE;;;;;;;;GAQG;AACH,eAAO,MAAM,QAAQ,EAAE,WAA+B,CAAA;AAEtD;;;;;;GAMG;AACH,eAAO,MAAM,mBAAmB,EAAE,WAA0C,CAAA;AAE5E;;;;;;GAMG;AACH,eAAO,MAAM,mBAAmB,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAsC,CAAA;AAEhG;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,iBAAiB,CAAC,iBAAiB,CAAA;IACjF;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAA;CAC9D,CAAA;AAEjB;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,iBAAiB,CAAC,iBAAiB,CAAmB,CAAA;AAEvG;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,YAAY,CAAA;IACzD;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,GAAG,YAAY,CAAA;CACnC,CAAA;AAEpB;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;;;OAKG;IACH,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,YAAY,CAAA;IAC3D;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,GAAG,YAAY,CAAA;CAClC,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,0BAA0B,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAuC,CAAA;AAEjG;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAgC,CAAA;AAEnF;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAiC,CAAA;AAErF;;;GAGG;AACH,eAAO,MAAM,qBAAqB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAkC,CAAA;AAEvF;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAA4B,CAAA;AAE3E;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,YAAY,CAAA;IACzD;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,GAAG,YAAY,CAAA;CACpC,CAAA;AAEnB;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,KAAK,EAAE,YAAY,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,YAAY,CAAA;IAC3D;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,GAAG,YAAY,CAAA;CACnC,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,yBAAyB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAsC,CAAA;AAE/F;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAA+B,CAAA;AAEjF;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAgC,CAAA;AAEnF;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAiC,CAAA;AAErF;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAA2B,CAAA;AAEzE;;;;;;;;;;GAUG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAgC,CAAA;AAEpF;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAA+B,CAAA;AAElF;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IACpD;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,GAAG,OAAO,CAAA;CAC5B,CAAA;AAEtB;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,OAAO,CAAA;IACpD;;;;;OAKG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,GAAG,OAAO,CAAA;CAC3B,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,KAAK,YAA4B,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,YAA4B,CAAA;AAE/C;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAgC,CAAA;AAEpF;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;OAMG;IACH,CAAC,KAAK,EAAE,iBAAiB,CAAC,iBAAiB,GAAG,CAAC,IAAI,EAAE,YAAY,KAAK,YAAY,CAAA;IAClF;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,iBAAiB,CAAC,iBAAiB,GAAG,YAAY,CAAA;CAC9D,CAAA;AAElB;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,MAAwB,CAAA;AAErE;;;;;;GAMG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAAiC,CAAA;AAEtF;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,WAAW,CAAC,WAAW,CAAkB,CAAA;AAErF;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,KAAK,OAA2B,CAAA"}