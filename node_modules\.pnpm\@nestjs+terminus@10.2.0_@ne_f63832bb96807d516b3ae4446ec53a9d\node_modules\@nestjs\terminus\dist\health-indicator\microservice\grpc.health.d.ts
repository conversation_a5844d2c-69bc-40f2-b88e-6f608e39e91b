import { type HealthIndicatorResult } from '../..';
import { type PropType } from '../../utils';
import { HealthIndicator } from '../health-indicator';
/**
 * The function to check whether the service is up or down
 */
export type HealthServiceCheck = (healthService: any, service: string) => Promise<any>;
interface GrpcClientOptionsLike {
    transport?: number;
    options?: any;
}
type GrpcOptionsLike<GrpcClientOptions extends GrpcClientOptionsLike = GrpcClientOptionsLike> = PropType<GrpcClientOptions, 'options'>;
/**
 * The options for the `grpc.checkService` health indicator function
 */
export type CheckGRPCServiceOptions<GrpcOptions extends GrpcClientOptionsLike = GrpcClientOptionsLike> = Partial<GrpcOptionsLike<GrpcOptions>> & {
    timeout?: number;
    healthServiceName?: string;
    healthServiceCheck?: HealthServiceCheck;
};
/**
 * The `GRPCHealthIndicator` is used for health checks
 * related to GRPC
 *
 * @publicApi
 * @module TerminusModule
 */
export declare class GRPCHealthIndicator extends HealthIndicator {
    private nestJsMicroservices;
    /**
     * Initializes the health indicator
     */
    constructor();
    /**
     * A cache of open channels for the health indicator
     * This is used to prevent opening new channels for every health check
     */
    private readonly openChannels;
    /**
     * Checks if the dependant packages are present
     */
    private checkDependantPackages;
    /**
     * Creates a GRPC client from the given options
     * @private
     */
    private createClient;
    /**
     * Checks if the given service is up using the standard health check
     * specification of GRPC.
     *
     * https://github.com/grpc/grpc/blob/master/doc/health-checking.md
     *
     * @param {string} key The key which will be used for the result object
     * @param {string} service The service which should be checked
     * @param {CheckGRPCOptions} [options] Configuration for the request
     *
     * @example
     * grpc.checkService<GrpcOptions>('hero_service', 'hero.health.v1')
     *
     * @example
     * // Change the timeout
     * grpc.checkService<GrpcOptions>('hero_service', 'hero.health.v1', { timeout: 300 })
     *
     * @example
     * // You can customize the health check
     * // by giving these options. Nonetheless it is still seen
     * // as best practice to implement the recommended GRPC specs
     * grpc.checkService<GrpcOptions>('hero_service', 'hero.health.v1', {
     *   timeout: 500,
     *   package: 'grpc.health.v2',
     *   protoPath: join(__dirname, './protos/my-custom-health.v1'),
     *   // The name of the service you need for the health check
     *   healthServiceName: 'Health',
     *   // Your custom function which checks the service
     *   healthServiceCheck: (healthService: any, service: string) =>
     *     healthService.check({ service }).toPromise(),
     * })
     *
     * @throws {HealthCheckError} Gets thrown in case a health check failed
     * @throws {TimeoutError} Gets thrown in case a health check exceeded the given timeout
     * @throws {UnhealthyResponseCodeError} Gets thrown in case the received response is unhealthy
     */
    checkService<GrpcOptions extends GrpcClientOptionsLike = GrpcClientOptionsLike>(key: string, service: string, options?: CheckGRPCServiceOptions<GrpcOptions>): Promise<HealthIndicatorResult>;
}
export {};
