/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/sites": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/listSites | `listSites`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
     */
    get: operations["listSites"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/createSite | `createSite`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [createEnvVars](#tag/environmentVariables/operation/createEnvVars) to create environment variables for a site.
     */
    post: operations["createSite"];
  };
  "/sites/{site_id}": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/getSite | `getSite`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
     */
    get: operations["getSite"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/site/operation/deleteSite | `deleteSite`} */
    delete: operations["deleteSite"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/updateSite | `updateSite`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [updateEnvVar](#tag/environmentVariables/operation/updateEnvVar) to update a site's environment variables.
     */
    patch: operations["updateSite"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/ssl": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/showSiteTLSCertificate | `showSiteTLSCertificate`} */
    get: operations["showSiteTLSCertificate"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/provisionSiteTLSCertificate | `provisionSiteTLSCertificate`} */
    post: operations["provisionSiteTLSCertificate"];
  };
  "/sites/{site_id}/ssl/certificates": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/getAllCertificates | `getAllCertificates`} */
    get: operations["getAllCertificates"];
  };
  "/accounts/{account_id}/env": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getEnvVars | `getEnvVars`}
     *
     * Returns all environment variables for an account or site. An account corresponds to a team in the Netlify UI.
     */
    get: operations["getEnvVars"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/createEnvVars | `createEnvVars`}
     *
     * Creates new environment variables. Granular scopes are available on Pro plans and above.
     */
    post: operations["createEnvVars"];
  };
  "/api/v1/sites/{site_id}/env": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getSiteEnvVars | `getSiteEnvVars`}
     *
     * Returns all environment variables for a site. This convenience method behaves the same as `getEnvVars` but doesn't require an `account_id` as input.
     */
    get: operations["getSiteEnvVars"];
  };
  "/accounts/{account_id}/env/{key}": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getEnvVar | `getEnvVar`}
     *
     * Returns an individual environment variable.
     */
    get: operations["getEnvVar"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/updateEnvVar | `updateEnvVar`}
     *
     * Updates an existing environment variable and all of its values. Existing values will be replaced by values provided.
     */
    put: operations["updateEnvVar"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/deleteEnvVar | `deleteEnvVar`}
     *
     * Deletes an environment variable
     */
    delete: operations["deleteEnvVar"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/setEnvVarValue | `setEnvVarValue`}
     *
     * Updates or creates a new value for an existing environment variable.
     */
    patch: operations["setEnvVarValue"];
  };
  "/accounts/{account_id}/env/{key}/value/{id}": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/deleteEnvVarValue | `deleteEnvVarValue`}
     *
     * Deletes a specific environment variable value.
     */
    delete: operations["deleteEnvVarValue"];
  };
  "/sites/{site_id}/functions": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/function/operation/searchSiteFunctions | `searchSiteFunctions`} */
    get: operations["searchSiteFunctions"];
  };
  "/sites/{site_id}/forms": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/form/operation/listSiteForms | `listSiteForms`} */
    get: operations["listSiteForms"];
  };
  "/sites/{site_id}/forms/{form_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/form/operation/deleteSiteForm | `deleteSiteForm`} */
    delete: operations["deleteSiteForm"];
  };
  "/sites/{site_id}/submissions": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listSiteSubmissions | `listSiteSubmissions`} */
    get: operations["listSiteSubmissions"];
  };
  "/sites/{site_id}/files": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/listSiteFiles | `listSiteFiles`} */
    get: operations["listSiteFiles"];
  };
  "/sites/{site_id}/assets": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/listSiteAssets | `listSiteAssets`} */
    get: operations["listSiteAssets"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/createSiteAsset | `createSiteAsset`} */
    post: operations["createSiteAsset"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/assets/{asset_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/getSiteAssetInfo | `getSiteAssetInfo`} */
    get: operations["getSiteAssetInfo"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/updateSiteAsset | `updateSiteAsset`} */
    put: operations["updateSiteAsset"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/deleteSiteAsset | `deleteSiteAsset`} */
    delete: operations["deleteSiteAsset"];
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
    };
  };
  "/sites/{site_id}/assets/{asset_id}/public_signature": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/assetPublicSignature/operation/getSiteAssetPublicSignature | `getSiteAssetPublicSignature`} */
    get: operations["getSiteAssetPublicSignature"];
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
    };
  };
  "/sites/{site_id}/files/{file_path}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/getSiteFileByPathName | `getSiteFileByPathName`} */
    get: operations["getSiteFileByPathName"];
  };
  "/purge": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/purge/operation/purgeCache | `purgeCache`}
     *
     * Purges cached content from Netlify's CDN. Supports purging by Cache-Tag.
     */
    post: operations["purgeCache"];
  };
  "/sites/{site_id}/snippets": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/listSiteSnippets | `listSiteSnippets`} */
    get: operations["listSiteSnippets"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/createSiteSnippet | `createSiteSnippet`} */
    post: operations["createSiteSnippet"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/snippets/{snippet_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/getSiteSnippet | `getSiteSnippet`} */
    get: operations["getSiteSnippet"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/updateSiteSnippet | `updateSiteSnippet`} */
    put: operations["updateSiteSnippet"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/deleteSiteSnippet | `deleteSiteSnippet`} */
    delete: operations["deleteSiteSnippet"];
    parameters: {
      path: {
        site_id: string;
        snippet_id: string;
      };
    };
  };
  "/sites/{site_id}/metadata": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/metadata/operation/getSiteMetadata | `getSiteMetadata`} */
    get: operations["getSiteMetadata"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/metadata/operation/updateSiteMetadata | `updateSiteMetadata`} */
    put: operations["updateSiteMetadata"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/build_hooks": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/listSiteBuildHooks | `listSiteBuildHooks`} */
    get: operations["listSiteBuildHooks"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/createSiteBuildHook | `createSiteBuildHook`} */
    post: operations["createSiteBuildHook"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/build_hooks/{id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/getSiteBuildHook | `getSiteBuildHook`} */
    get: operations["getSiteBuildHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/updateSiteBuildHook | `updateSiteBuildHook`} */
    put: operations["updateSiteBuildHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/deleteSiteBuildHook | `deleteSiteBuildHook`} */
    delete: operations["deleteSiteBuildHook"];
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
  };
  "/sites/{site_id}/deploys": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/listSiteDeploys | `listSiteDeploys`} */
    get: operations["listSiteDeploys"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/createSiteDeploy | `createSiteDeploy`} */
    post: operations["createSiteDeploy"];
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        "deploy-previews"?: boolean;
        production?: boolean;
        state?:
          | "new"
          | "pending_review"
          | "accepted"
          | "rejected"
          | "enqueued"
          | "building"
          | "uploading"
          | "uploaded"
          | "preparing"
          | "prepared"
          | "processing"
          | "processed"
          | "ready"
          | "error"
          | "retrying";
        branch?: string;
        "latest-published"?: boolean;
      };
    };
  };
  "/sites/{site_id}/deploys/{deploy_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/getSiteDeploy | `getSiteDeploy`} */
    get: operations["getSiteDeploy"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/updateSiteDeploy | `updateSiteDeploy`} */
    put: operations["updateSiteDeploy"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/deleteSiteDeploy | `deleteSiteDeploy`} */
    delete: operations["deleteSiteDeploy"];
  };
  "/deploys/{deploy_id}/cancel": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/cancelSiteDeploy | `cancelSiteDeploy`} */
    post: operations["cancelSiteDeploy"];
  };
  "/sites/{site_id}/deploys/{deploy_id}/restore": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/restoreSiteDeploy | `restoreSiteDeploy`} */
    post: operations["restoreSiteDeploy"];
  };
  "/sites/{site_id}/builds": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/listSiteBuilds | `listSiteBuilds`} */
    get: operations["listSiteBuilds"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/createSiteBuild | `createSiteBuild`} */
    post: operations["createSiteBuild"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/deployed-branches": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deployedBranch/operation/listSiteDeployedBranches | `listSiteDeployedBranches`} */
    get: operations["listSiteDeployedBranches"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/unlink_repo": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/unlinkSiteRepo | `unlinkSiteRepo`}
     *
     * [Beta] Unlinks the repo from the site.
     *
     * This action will also:
     * - Delete associated deploy keys
     * - Delete outgoing webhooks for the repo
     * - Delete the site's build hooks
     */
    put: operations["unlinkSiteRepo"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/builds/{build_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/getSiteBuild | `getSiteBuild`} */
    get: operations["getSiteBuild"];
    parameters: {
      path: {
        build_id: string;
      };
    };
  };
  "/builds/{build_id}/log": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/buildLogMsg/operation/updateSiteBuildLog | `updateSiteBuildLog`} */
    post: operations["updateSiteBuildLog"];
    parameters: {
      path: {
        build_id: string;
      };
    };
  };
  "/builds/{build_id}/start": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/notifyBuildStart | `notifyBuildStart`} */
    post: operations["notifyBuildStart"];
    parameters: {
      path: {
        build_id: string;
      };
      query: {
        buildbot_version?: string;
        build_version?: string;
      };
    };
  };
  "/{account_id}/builds/status": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/getAccountBuildStatus | `getAccountBuildStatus`} */
    get: operations["getAccountBuildStatus"];
    parameters: {
      path: {
        account_id: string;
      };
    };
  };
  "/sites/{site_id}/dns": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDNSForSite | `getDNSForSite`} */
    get: operations["getDNSForSite"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/configureDNSForSite | `configureDNSForSite`} */
    put: operations["configureDNSForSite"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/rollback": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/rollbackSiteDeploy | `rollbackSiteDeploy`} */
    put: operations["rollbackSiteDeploy"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/deploys/{deploy_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/getDeploy | `getDeploy`} */
    get: operations["getDeploy"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/deleteDeploy | `deleteDeploy`} */
    delete: operations["deleteDeploy"];
  };
  "/deploys/{deploy_id}/lock": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/lockDeploy | `lockDeploy`} */
    post: operations["lockDeploy"];
  };
  "/deploys/{deploy_id}/unlock": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/unlockDeploy | `unlockDeploy`} */
    post: operations["unlockDeploy"];
  };
  "/deploys/{deploy_id}/files/{path}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/uploadDeployFile | `uploadDeployFile`} */
    put: operations["uploadDeployFile"];
  };
  "/deploys/{deploy_id}/functions/{name}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/function/operation/uploadDeployFunction | `uploadDeployFunction`} */
    put: operations["uploadDeployFunction"];
  };
  "/forms/{form_id}/submissions": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listFormSubmissions | `listFormSubmissions`} */
    get: operations["listFormSubmissions"];
  };
  "/hooks": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/listHooksBySiteId | `listHooksBySiteId`} */
    get: operations["listHooksBySiteId"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/createHookBySiteId | `createHookBySiteId`} */
    post: operations["createHookBySiteId"];
  };
  "/hooks/{hook_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/getHook | `getHook`} */
    get: operations["getHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/updateHook | `updateHook`} */
    put: operations["updateHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/deleteHook | `deleteHook`} */
    delete: operations["deleteHook"];
    parameters: {
      path: {
        hook_id: string;
      };
    };
  };
  "/hooks/{hook_id}/enable": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/enableHook | `enableHook`} */
    post: operations["enableHook"];
    parameters: {
      path: {
        hook_id: string;
      };
    };
  };
  "/hooks/types": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/hookType/operation/listHookTypes | `listHookTypes`} */
    get: operations["listHookTypes"];
  };
  "/oauth/tickets": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/ticket/operation/createTicket | `createTicket`} */
    post: operations["createTicket"];
  };
  "/oauth/tickets/{ticket_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/ticket/operation/showTicket | `showTicket`} */
    get: operations["showTicket"];
  };
  "/oauth/tickets/{ticket_id}/exchange": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/accessToken/operation/exchangeTicket | `exchangeTicket`} */
    post: operations["exchangeTicket"];
  };
  "/deploy_keys": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/listDeployKeys | `listDeployKeys`} */
    get: operations["listDeployKeys"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/createDeployKey | `createDeployKey`} */
    post: operations["createDeployKey"];
  };
  "/deploy_keys/{key_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/getDeployKey | `getDeployKey`} */
    get: operations["getDeployKey"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/deleteDeployKey | `deleteDeployKey`} */
    delete: operations["deleteDeployKey"];
    parameters: {
      path: {
        key_id: string;
      };
    };
  };
  "/{account_slug}/sites": {
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/listSitesForAccount | `listSitesForAccount`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
     */
    get: operations["listSitesForAccount"];
    /**
     * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/createSiteInTeam | `createSiteInTeam`}
     *
     * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [createEnvVars](#tag/environmentVariables/operation/createEnvVars) to create environment variables for a site.
     */
    post: operations["createSiteInTeam"];
  };
  "/{account_slug}/members": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/listMembersForAccount | `listMembersForAccount`} */
    get: operations["listMembersForAccount"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/addMemberToAccount | `addMemberToAccount`} */
    post: operations["addMemberToAccount"];
    parameters: {
      path: {
        account_slug: string;
      };
    };
  };
  "/{account_slug}/members/{member_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/getAccountMember | `getAccountMember`} */
    get: operations["getAccountMember"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/updateAccountMember | `updateAccountMember`} */
    put: operations["updateAccountMember"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/removeAccountMember | `removeAccountMember`} */
    delete: operations["removeAccountMember"];
    parameters: {
      path: {
        account_slug: string;
        member_id: string;
      };
    };
  };
  "/billing/payment_methods": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/paymentMethod/operation/listPaymentMethodsForUser | `listPaymentMethodsForUser`} */
    get: operations["listPaymentMethodsForUser"];
  };
  "/accounts/types": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountType/operation/listAccountTypesForUser | `listAccountTypesForUser`} */
    get: operations["listAccountTypesForUser"];
  };
  "/accounts": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/listAccountsForUser | `listAccountsForUser`} */
    get: operations["listAccountsForUser"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/createAccount | `createAccount`} */
    post: operations["createAccount"];
  };
  "/accounts/{account_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/getAccount | `getAccount`} */
    get: operations["getAccount"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/updateAccount | `updateAccount`} */
    put: operations["updateAccount"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/cancelAccount | `cancelAccount`} */
    delete: operations["cancelAccount"];
    parameters: {
      path: {
        account_id: string;
      };
    };
  };
  "/accounts/{account_id}/audit": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/auditLog/operation/listAccountAuditEvents | `listAccountAuditEvents`} */
    get: operations["listAccountAuditEvents"];
    parameters: {
      path: {
        account_id: string;
      };
    };
  };
  "/submissions/{submission_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listFormSubmission | `listFormSubmission`} */
    get: operations["listFormSubmission"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/deleteSubmission | `deleteSubmission`} */
    delete: operations["deleteSubmission"];
    parameters: {
      path: {
        submission_id: string;
      };
    };
  };
  "/sites/{site_id}/service-instances": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/listServiceInstancesForSite | `listServiceInstancesForSite`} */
    get: operations["listServiceInstancesForSite"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/services/{addon}/instances": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/createServiceInstance | `createServiceInstance`} */
    post: operations["createServiceInstance"];
    parameters: {
      path: {
        site_id: string;
        addon: string;
      };
    };
  };
  "/sites/{site_id}/services/{addon}/instances/{instance_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/showServiceInstance | `showServiceInstance`} */
    get: operations["showServiceInstance"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/updateServiceInstance | `updateServiceInstance`} */
    put: operations["updateServiceInstance"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/deleteServiceInstance | `deleteServiceInstance`} */
    delete: operations["deleteServiceInstance"];
    parameters: {
      path: {
        site_id: string;
        addon: string;
        instance_id: string;
      };
    };
  };
  "/services/": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/getServices | `getServices`} */
    get: operations["getServices"];
    parameters: {
      query: {
        search?: string;
      };
    };
  };
  "/services/{addonName}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/showService | `showService`} */
    get: operations["showService"];
    parameters: {
      path: {
        addonName: string;
      };
    };
  };
  "/services/{addonName}/manifest": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/showServiceManifest | `showServiceManifest`} */
    get: operations["showServiceManifest"];
    parameters: {
      path: {
        addonName: string;
      };
    };
  };
  "/user": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/user/operation/getCurrentUser | `getCurrentUser`} */
    get: operations["getCurrentUser"];
  };
  "/sites/{site_id}/traffic_splits": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/getSplitTests | `getSplitTests`} */
    get: operations["getSplitTests"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/createSplitTest | `createSplitTest`} */
    post: operations["createSplitTest"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/traffic_splits/{split_test_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/getSplitTest | `getSplitTest`} */
    get: operations["getSplitTest"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/updateSplitTest | `updateSplitTest`} */
    put: operations["updateSplitTest"];
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
  };
  "/sites/{site_id}/traffic_splits/{split_test_id}/publish": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/enableSplitTest | `enableSplitTest`} */
    post: operations["enableSplitTest"];
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
  };
  "/sites/{site_id}/traffic_splits/{split_test_id}/unpublish": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/disableSplitTest | `disableSplitTest`} */
    post: operations["disableSplitTest"];
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
  };
  "/dns_zones": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsZones | `getDnsZones`} */
    get: operations["getDnsZones"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/createDnsZone | `createDnsZone`} */
    post: operations["createDnsZone"];
  };
  "/dns_zones/{zone_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsZone | `getDnsZone`} */
    get: operations["getDnsZone"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/deleteDnsZone | `deleteDnsZone`} */
    delete: operations["deleteDnsZone"];
    parameters: {
      path: {
        zone_id: string;
      };
    };
  };
  "/dns_zones/{zone_id}/transfer": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/transferDnsZone | `transferDnsZone`} */
    put: operations["transferDnsZone"];
    parameters: {
      path: {
        zone_id: string;
      };
      query: {
        /** the account of the dns zone */
        account_id: string;
        /** the account you want to transfer the dns zone to */
        transfer_account_id: string;
        /** the user you want to transfer the dns zone to */
        transfer_user_id: string;
      };
    };
  };
  "/dns_zones/{zone_id}/dns_records": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsRecords | `getDnsRecords`} */
    get: operations["getDnsRecords"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/createDnsRecord | `createDnsRecord`} */
    post: operations["createDnsRecord"];
    parameters: {
      path: {
        zone_id: string;
      };
    };
  };
  "/dns_zones/{zone_id}/dns_records/{dns_record_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getIndividualDnsRecord | `getIndividualDnsRecord`} */
    get: operations["getIndividualDnsRecord"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/deleteDnsRecord | `deleteDnsRecord`} */
    delete: operations["deleteDnsRecord"];
    parameters: {
      path: {
        zone_id: string;
        dns_record_id: string;
      };
    };
  };
  "/sites/{site_id}/dev_servers": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/listSiteDevServers | `listSiteDevServers`} */
    get: operations["listSiteDevServers"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/createSiteDevServer | `createSiteDevServer`} */
    post: operations["createSiteDevServer"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/deleteSiteDevServers | `deleteSiteDevServers`} */
    delete: operations["deleteSiteDevServers"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/dev_servers/{dev_server_id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/getSiteDevServer | `getSiteDevServer`} */
    get: operations["getSiteDevServer"];
    parameters: {
      path: {
        site_id: string;
        dev_server_id: string;
      };
    };
  };
  "/sites/{site_id}/dev_servers/{dev_server_id}/activity": {
    parameters: {
      path: {
        site_id: string;
        dev_server_id: string;
      };
    };
  };
  "/sites/{site_id}/dev_server_hooks": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/listSiteDevServerHooks | `listSiteDevServerHooks`} */
    get: operations["listSiteDevServerHooks"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/createSiteDevServerHook | `createSiteDevServerHook`} */
    post: operations["createSiteDevServerHook"];
    parameters: {
      path: {
        site_id: string;
      };
    };
  };
  "/sites/{site_id}/dev_server_hooks/{id}": {
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/getSiteDevServerHook | `getSiteDevServerHook`} */
    get: operations["getSiteDevServerHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/updateSiteDevServerHook | `updateSiteDevServerHook`} */
    put: operations["updateSiteDevServerHook"];
    /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/deleteSiteDevServerHook | `deleteSiteDevServerHook`} */
    delete: operations["deleteSiteDevServerHook"];
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
  };
}

export interface components {
  schemas: {
    DeployValidationsReport: {
      /** @description The id of the deploy validations report */
      id?: string;
      /** @description The id of the deploy */
      deploy_id?: string;
      secret_scan_result?: components["schemas"]["DeployValidationsReport_SecretScanResult"];
    };
    DeployValidationsReport_SecretScanResult: {
      /** @description The number of files scanned */
      scannedFilesCount?: number;
      /** @description The list of secrets scan matches */
      secretsScanMatches?: string[];
    };
    splitTestSetup: {
      branch_tests?: { [key: string]: unknown };
    };
    splitTests: components["schemas"]["splitTest"][];
    splitTest: {
      id?: string;
      site_id?: string;
      name?: string;
      path?: string;
      branches?: { [key: string]: unknown }[];
      active?: boolean;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      /** Format: dateTime */
      unpublished_at?: string;
    };
    serviceInstance: {
      id?: string;
      url?: string;
      config?: { [key: string]: unknown };
      external_attributes?: { [key: string]: unknown };
      service_slug?: string;
      service_path?: string;
      service_name?: string;
      env?: { [key: string]: unknown };
      snippets?: { [key: string]: unknown }[];
      auth_url?: string;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
    };
    service: {
      id?: string;
      name?: string;
      slug?: string;
      service_path?: string;
      long_description?: string;
      description?: string;
      events?: { [key: string]: unknown }[];
      tags?: string[];
      icon?: string;
      manifest_url?: string;
      environments?: string[];
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
    };
    site: {
      id?: string;
      state?: string;
      plan?: string;
      name?: string;
      custom_domain?: string;
      domain_aliases?: string[];
      branch_deploy_custom_domain?: string;
      deploy_preview_custom_domain?: string;
      password?: string;
      notification_email?: string;
      url?: string;
      ssl_url?: string;
      admin_url?: string;
      screenshot_url?: string;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      user_id?: string;
      session_id?: string;
      ssl?: boolean;
      force_ssl?: boolean;
      managed_dns?: boolean;
      deploy_url?: string;
      published_deploy?: components["schemas"]["deploy"];
      account_id?: string;
      account_name?: string;
      account_slug?: string;
      git_provider?: string;
      deploy_hook?: string;
      capabilities?: { [key: string]: { [key: string]: unknown } };
      processing_settings?: {
        html?: {
          pretty_urls?: boolean;
        };
      };
      build_settings?: components["schemas"]["repoInfo"];
      id_domain?: string;
      default_hooks_data?: {
        access_token?: string;
      };
      build_image?: string;
      prerender?: string;
      functions_region?: string;
    };
    siteSetup: components["schemas"]["site"] & {
      repo?: components["schemas"]["repoInfo"];
    };
    repoInfo: {
      id?: number;
      provider?: string;
      deploy_key_id?: string;
      repo_path?: string;
      repo_branch?: string;
      dir?: string;
      functions_dir?: string;
      cmd?: string;
      allowed_branches?: string[];
      public_repo?: boolean;
      private_logs?: boolean;
      repo_url?: string;
      env?: { [key: string]: string };
      installation_id?: number;
      stop_builds?: boolean;
    };
    submission: {
      id?: string;
      /** Format: int32 */
      number?: number;
      email?: string;
      name?: string;
      first_name?: string;
      last_name?: string;
      company?: string;
      summary?: string;
      body?: string;
      data?: { [key: string]: unknown };
      /** Format: dateTime */
      created_at?: string;
      site_url?: string;
    };
    /** @description Environment variable model definition */
    envVar: {
      /** @description The environment variable key, like ALGOLIA_ID (case-sensitive) */
      key?: string;
      /** @description The scopes that this environment variable is set to */
      scopes?: ("builds" | "functions" | "runtime" | "post-processing")[];
      /** @description An array of Value objects containing values and metadata */
      values?: components["schemas"]["envVarValue"][];
      /** @description Secret values are only readable by code running on Netlify's systems. With secrets, only the local development context values are readable from the UI, API, and CLI. By default, environment variable values are not secret. */
      is_secret?: boolean;
      /**
       * Format: date-time
       * @description The timestamp of when the value was last updated
       */
      updated_at?: string;
      updated_by?: components["schemas"]["envVarUser"];
    };
    /** @description Environment variable value model definition */
    envVarValue: {
      /** @description The environment variable value's universally unique ID */
      id?: string;
      /** @description The environment variable's unencrypted value */
      value?: string;
      /**
       * @description The deploy context in which this value will be used. `dev` refers to local development when running `netlify dev`.
       * @enum {string}
       */
      context?:
        | "all"
        | "dev"
        | "branch-deploy"
        | "deploy-preview"
        | "production"
        | "branch";
      /** @description An additional parameter for custom branches. Currently, this is used for specifying a branch name when `context=branch`. */
      context_parameter?: string;
    };
    envVarUser: {
      /** @description The user's unique identifier */
      id?: string;
      /** @description The user's full name (first and last) */
      full_name?: string;
      /** @description The user's email address */
      email?: string;
      /** @description A URL pointing to the user's avatar */
      avatar_url?: string;
    };
    form: {
      id?: string;
      site_id?: string;
      name?: string;
      paths?: string[];
      /** Format: int32 */
      submission_count?: number;
      fields?: { [key: string]: unknown }[];
      /** Format: dateTime */
      created_at?: string;
    };
    hookType: {
      name?: string;
      events?: string[];
      fields?: { [key: string]: unknown }[];
    };
    hook: {
      id?: string;
      site_id?: string;
      type?: string;
      event?: string;
      data?: { [key: string]: unknown };
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      disabled?: boolean;
    };
    file: {
      id?: string;
      path?: string;
      sha?: string;
      mime_type?: string;
      /** Format: int64 */
      size?: number;
    };
    function: {
      id?: string;
      name?: string;
      sha?: string;
    };
    snippet: {
      /** Format: int32 */
      id?: number;
      site_id?: string;
      title?: string;
      general?: string;
      general_position?: string;
      goal?: string;
      goal_position?: string;
    };
    purge: {
      site_id?: string;
      site_slug?: string;
      cache_tags?: string[];
    };
    deploy: {
      id?: string;
      site_id?: string;
      user_id?: string;
      build_id?: string;
      state?: string;
      name?: string;
      url?: string;
      ssl_url?: string;
      admin_url?: string;
      deploy_url?: string;
      deploy_ssl_url?: string;
      screenshot_url?: string;
      review_id?: number;
      draft?: boolean;
      required?: string[];
      required_functions?: string[];
      error_message?: string;
      branch?: string;
      commit_ref?: string;
      commit_url?: string;
      skipped?: boolean;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      /** Format: dateTime */
      published_at?: string;
      title?: string;
      context?: string;
      locked?: boolean;
      review_url?: string;
      framework?: string;
      function_schedules?: components["schemas"]["functionSchedule"][];
    };
    deployFiles: {
      files?: { [key: string]: unknown };
      draft?: boolean;
      async?: boolean;
      functions?: { [key: string]: unknown };
      function_schedules?: components["schemas"]["functionSchedule"][];
      functions_config?: {
        [key: string]: components["schemas"]["functionConfig"];
      };
      branch?: string;
      framework?: string;
      framework_version?: string;
    };
    pluginParams: {
      pinned_version?: string;
    };
    plugin: {
      package?: string;
      pinned_version?: string;
    };
    buildStatus: {
      active?: number;
      pending_concurrency?: number;
      enqueued?: number;
      build_count?: number;
      minutes?: {
        current?: number;
        current_average_sec?: number;
        previous?: number;
        /** Format: dateTime */
        period_start_date?: string;
        /** Format: dateTime */
        period_end_date?: string;
        /** Format: dateTime */
        last_updated_at?: string;
        included_minutes?: string;
        included_minutes_with_packs?: string;
      };
    };
    build: {
      id?: string;
      deploy_id?: string;
      sha?: string;
      done?: boolean;
      error?: string;
      /** Format: dateTime */
      created_at?: string;
    };
    buildLogMsg: {
      message?: string;
      error?: boolean;
      /** @enum {string} */
      section?:
        | "initializing"
        | "building"
        | "deploying"
        | "cleanup"
        | "postprocessing";
    };
    pluginRunData: {
      package?: string;
      version?: string;
      state?: string;
      reporting_event?: string;
      title?: string;
      summary?: string;
      text?: string;
    };
    pluginRun: components["schemas"]["pluginRunData"] & {
      deploy_id?: string;
    };
    metadata: { [key: string]: unknown };
    dnsZoneSetup: {
      account_slug?: string;
      site_id?: string;
      name?: string;
    };
    dnsZones: components["schemas"]["dnsZone"][];
    dnsZone: {
      id?: string;
      name?: string;
      errors?: string[];
      supported_record_types?: string[];
      user_id?: string;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      records?: components["schemas"]["dnsRecord"][];
      dns_servers?: string[];
      account_id?: string;
      site_id?: string;
      account_slug?: string;
      account_name?: string;
      domain?: string;
      ipv6_enabled?: boolean;
      dedicated?: boolean;
    };
    dnsRecordCreate: {
      type?: string;
      hostname?: string;
      value?: string;
      /** Format: int64 */
      ttl?: number;
      /** Format: int64 */
      priority?: number;
      /** Format: int64 */
      weight?: number;
      /** Format: int64 */
      port?: number;
      /** Format: int64 */
      flag?: number;
      tag?: string;
    };
    dnsRecords: components["schemas"]["dnsRecord"][];
    dnsRecord: {
      id?: string;
      hostname?: string;
      type?: string;
      value?: string;
      /** Format: int64 */
      ttl?: number;
      /** Format: int64 */
      priority?: number;
      dns_zone_id?: string;
      site_id?: string;
      flag?: number;
      tag?: string;
      managed?: boolean;
    };
    sniCertificate: {
      state?: string;
      domains?: string[];
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      /** Format: dateTime */
      expires_at?: string;
    };
    ticket: {
      id?: string;
      client_id?: string;
      authorized?: boolean;
      /** Format: dateTime */
      created_at?: string;
    };
    accessToken: {
      id?: string;
      access_token?: string;
      user_id?: string;
      user_email?: string;
      /** Format: dateTime */
      created_at?: string;
    };
    asset: {
      id?: string;
      site_id?: string;
      creator_id?: string;
      name?: string;
      state?: string;
      content_type?: string;
      url?: string;
      key?: string;
      visibility?: string;
      /** Format: int64 */
      size?: number;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
    };
    assetForm: {
      url?: string;
      fields?: { [key: string]: string };
    };
    assetSignature: {
      form?: components["schemas"]["assetForm"];
      asset?: components["schemas"]["asset"];
    };
    assetPublicSignature: {
      url?: string;
    };
    deployKey: {
      id?: string;
      public_key?: string;
      /** Format: dateTime */
      created_at?: string;
    };
    member: {
      id?: string;
      full_name?: string;
      email?: string;
      avatar?: string;
      role?: string;
    };
    paymentMethod: {
      id?: string;
      method_name?: string;
      type?: string;
      state?: string;
      data?: {
        card_type?: string;
        last4?: string;
        email?: string;
      };
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
    };
    accountType: {
      id?: string;
      name?: string;
      description?: string;
      capabilities?: { [key: string]: unknown };
      monthly_dollar_price?: number;
      yearly_dollar_price?: number;
      monthly_seats_addon_dollar_price?: number;
      yearly_seats_addon_dollar_price?: number;
    };
    accountSetup: {
      name: string;
      type_id: string;
      payment_method_id?: string;
      /** @enum {string} */
      period?: "monthly" | "yearly";
      extra_seats_block?: number;
    };
    accountUpdateSetup: {
      name?: string;
      slug?: string;
      type_id?: string;
      extra_seats_block?: number;
      billing_name?: string;
      billing_email?: string;
      billing_details?: string;
    };
    accountAddMemberSetup: {
      /** @enum {string} */
      role?: "Owner" | "Developer" | "Billing Admin" | "Reviewer";
      email?: string;
    };
    accountUpdateMemberSetup: {
      /** @enum {string} */
      role?: "Owner" | "Developer" | "Billing Admin" | "Reviewer";
      /** @enum {string} */
      site_access?: "all" | "none" | "selected";
      site_ids?: string[];
    };
    accountMembership: {
      id?: string;
      name?: string;
      slug?: string;
      type?: string;
      capabilities?: {
        sites?: components["schemas"]["accountUsageCapability"];
        collaborators?: components["schemas"]["accountUsageCapability"];
      };
      billing_name?: string;
      billing_email?: string;
      billing_details?: string;
      billing_period?: string;
      payment_method_id?: string;
      type_name?: string;
      type_id?: string;
      owner_ids?: string[];
      roles_allowed?: string[];
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
    };
    auditLog: {
      id?: string;
      account_id?: string;
      payload?: {
        actor_id?: string;
        actor_name?: string;
        actor_email?: string;
        action?: string;
        /** Format: dateTime */
        timestamp?: string;
        log_type?: string;
      } & { [key: string]: { [key: string]: unknown } };
    };
    accountUsageCapability: {
      included?: number;
      used?: number;
    };
    buildSetup: {
      image?: string;
      clear_cache?: boolean;
    };
    buildHookSetup: {
      title?: string;
      branch?: string;
    };
    buildHook: {
      id?: string;
      title?: string;
      branch?: string;
      url?: string;
      site_id?: string;
      /** Format: dateTime */
      created_at?: string;
    };
    deployedBranch: {
      id?: string;
      deploy_id?: string;
      name?: string;
      slug?: string;
      url?: string;
      ssl_url?: string;
    };
    user: {
      id?: string;
      uid?: string;
      full_name?: string;
      avatar_url?: string;
      email?: string;
      affiliate_id?: string;
      /** Format: int64 */
      site_count?: number;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      last_login?: string;
      login_providers?: string[];
      onboarding_progress?: {
        slides?: string;
      };
    };
    error: {
      /** Format: int64 */
      code?: number;
      message: string;
    };
    functionSchedule: {
      name?: string;
      cron?: string;
    };
    functionConfig: {
      display_name?: string;
      generator?: string;
      build_data?: { [key: string]: unknown };
      routes?: components["schemas"]["functionRoute"][];
      excluded_routes?: components["schemas"]["excludedFunctionRoute"][];
      priority?: number;
      traffic_rules?: components["schemas"]["trafficRulesConfig"];
    };
    functionRoute: {
      pattern?: string;
      literal?: string;
      expression?: string;
      methods?: ("GET" | "POST" | "PUT" | "PATCH" | "DELETE" | "OPTIONS")[];
      prefer_static?: boolean;
    };
    excludedFunctionRoute: {
      pattern?: string;
      literal?: string;
      expression?: string;
    };
    trafficRulesConfig: {
      action?: {
        type?: string;
        config?: {
          to?: string;
          rate_limit_config?: components["schemas"]["trafficRulesRateLimitConfig"];
          aggregate?: components["schemas"]["trafficRulesAggregateConfig"];
        };
      };
    };
    trafficRulesRateLimitConfig: {
      /** @enum {string} */
      algorithm?: "sliding_window";
      window_size?: number;
      window_limit?: number;
    };
    trafficRulesAggregateConfig: {
      keys?: {
        /** @enum {string} */
        type?: "ip" | "domain";
      }[];
    };
    siteFunction: {
      branch?: string;
      /** Format: dateTime */
      created_at?: string;
      functions?: { [key: string]: unknown }[];
      id?: string;
      log_type?: string;
      provider?: string;
    };
    devServer: {
      id?: string;
      site_id?: string;
      branch?: string;
      url?: string;
      state?: string;
      /** Format: dateTime */
      created_at?: string;
      /** Format: dateTime */
      updated_at?: string;
      /** Format: dateTime */
      starting_at?: string;
      /** Format: dateTime */
      error_at?: string;
      /** Format: dateTime */
      live_at?: string;
      /** Format: dateTime */
      done_at?: string;
      title?: string;
    };
    devServerHook: {
      id?: string;
      title?: string;
      branch?: string;
      url?: string;
      site_id?: string;
      /** Format: dateTime */
      created_at?: string;
      /** @enum {string} */
      type?: "new_dev_server" | "content_refresh";
    };
    devServerHookSetup: {
      title?: string;
      branch?: string;
      /** @enum {string} */
      type?: "new_dev_server" | "content_refresh";
    };
  };
  responses: {
    /** error */
    error: {
      content: {
        "application/json": components["schemas"]["error"];
      };
    };
  };
  parameters: {
    page: number;
    perPage: number;
    retryCount: number;
  };
  requestBodies: {
    devServerHookSetup: {
      content: {
        "application/json": components["schemas"]["devServerHookSetup"];
      };
    };
    createServiceInstanceConfig: {
      content: {
        "application/json": { [key: string]: unknown };
      };
    };
    snippet: {
      content: {
        "application/json": components["schemas"]["snippet"];
      };
    };
    deployFiles: {
      content: {
        "application/json": components["schemas"]["deployFiles"];
      };
    };
    uploadDeployFileFileBody: {
      content: {
        "application/octet-stream": string;
      };
    };
    siteSetup: {
      content: {
        "application/json": components["schemas"]["siteSetup"];
      };
    };
    buildHookSetup: {
      content: {
        "application/json": components["schemas"]["buildHookSetup"];
      };
    };
    hook: {
      content: {
        "application/json": components["schemas"]["hook"];
      };
    };
    splitTestSetup: {
      content: {
        "application/json": components["schemas"]["splitTestSetup"];
      };
    };
  };
}

export interface operations {
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/listSites | `listSites`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
   */
  listSites: {
    parameters: {
      query: {
        name?: string;
        filter?: "all" | "owner" | "guest";
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["site"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/createSite | `createSite`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [createEnvVars](#tag/environmentVariables/operation/createEnvVars) to create environment variables for a site.
   */
  createSite: {
    parameters: {
      query: {
        configure_dns?: boolean;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["site"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["siteSetup"];
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/getSite | `getSite`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
   */
  getSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["site"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/site/operation/deleteSite | `deleteSite`} */
  deleteSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** Deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/updateSite | `updateSite`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [updateEnvVar](#tag/environmentVariables/operation/updateEnvVar) to update a site's environment variables.
   */
  updateSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["site"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["siteSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/showSiteTLSCertificate | `showSiteTLSCertificate`} */
  showSiteTLSCertificate: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["sniCertificate"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/provisionSiteTLSCertificate | `provisionSiteTLSCertificate`} */
  provisionSiteTLSCertificate: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        certificate?: string;
        key?: string;
        ca_certificates?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["sniCertificate"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/sniCertificate/operation/getAllCertificates | `getAllCertificates`} */
  getAllCertificates: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        domain: string;
      };
    };
    responses: {
      /** Array of SNI Certificates */
      200: {
        content: {
          "application/json": components["schemas"]["sniCertificate"][];
        };
      };
      /** Not Found */
      404: unknown;
      /** Unprocessable Entity */
      422: unknown;
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getEnvVars | `getEnvVars`}
   *
   * Returns all environment variables for an account or site. An account corresponds to a team in the Netlify UI.
   */
  getEnvVars: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
      };
      query: {
        /** Filter by deploy context */
        context_name?:
          | "all"
          | "dev"
          | "branch-deploy"
          | "deploy-preview"
          | "production";
        /** Filter by scope */
        scope?: "builds" | "functions" | "runtime" | "post-processing";
        /** If specified, only return environment variables set on this site */
        site_id?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["envVar"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/createEnvVars | `createEnvVars`}
   *
   * Creates new environment variables. Granular scopes are available on Pro plans and above.
   */
  createEnvVars: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
      };
      query: {
        /** If provided, create an environment variable on the site level, not the account level */
        site_id?: string;
      };
    };
    responses: {
      /** OK */
      201: {
        content: {
          "application/json": components["schemas"]["envVar"][];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": {
          /** @description The existing or new name of the key, if you wish to rename it (case-sensitive) */
          key?: string;
          /** @description The scopes that this environment variable is set to (Pro plans and above) */
          scopes?: ("builds" | "functions" | "runtime" | "post-processing")[];
          values?: components["schemas"]["envVarValue"][];
          /** @description Secret values are only readable by code running on Netlify's systems. With secrets, only the local development context values are readable from the UI, API, and CLI. By default, environment variable values are not secret. */
          is_secret?: boolean;
        }[];
      };
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getSiteEnvVars | `getSiteEnvVars`}
   *
   * Returns all environment variables for a site. This convenience method behaves the same as `getEnvVars` but doesn't require an `account_id` as input.
   */
  getSiteEnvVars: {
    parameters: {
      query: {
        /** Filter by deploy context */
        context_name?:
          | "all"
          | "dev"
          | "branch-deploy"
          | "deploy-preview"
          | "production";
        /** Filter by scope */
        scope?: "builds" | "functions" | "runtime" | "post_processing";
      };
      path: {
        /** Scope response to site_id */
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["envVar"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/getEnvVar | `getEnvVar`}
   *
   * Returns an individual environment variable.
   */
  getEnvVar: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
        /** The environment variable key (case-sensitive) */
        key: string;
      };
      query: {
        /** If provided, return the environment variable for a specific site (no merging is performed) */
        site_id?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["envVar"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/updateEnvVar | `updateEnvVar`}
   *
   * Updates an existing environment variable and all of its values. Existing values will be replaced by values provided.
   */
  updateEnvVar: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
        /** The existing environment variable key name (case-sensitive) */
        key: string;
      };
      query: {
        /** If provided, update an environment variable set on this site */
        site_id?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["envVar"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": {
          /** @description The existing or new name of the key, if you wish to rename it (case-sensitive) */
          key?: string;
          /** @description The scopes that this environment variable is set to (Pro plans and above) */
          scopes?: ("builds" | "functions" | "runtime" | "post-processing")[];
          values?: components["schemas"]["envVarValue"][];
          /** @description Secret values are only readable by code running on Netlify's systems. With secrets, only the local development context values are readable from the UI, API, and CLI. By default, environment variable values are not secret. */
          is_secret?: boolean;
        };
      };
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/deleteEnvVar | `deleteEnvVar`}
   *
   * Deletes an environment variable
   */
  deleteEnvVar: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
        /** The environment variable key (case-sensitive) */
        key: string;
      };
      query: {
        /** If provided, delete the environment variable from this site */
        site_id?: string;
      };
    };
    responses: {
      /** No Content (success) */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/setEnvVarValue | `setEnvVarValue`}
   *
   * Updates or creates a new value for an existing environment variable.
   */
  setEnvVarValue: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
        /** The existing environment variable key name (case-sensitive) */
        key: string;
      };
      query: {
        /** If provided, update an environment variable set on this site */
        site_id?: string;
      };
    };
    responses: {
      /** Created (success) */
      201: {
        content: {
          "application/json": components["schemas"]["envVar"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description The deploy context in which this value will be used. `dev` refers to local development when running `netlify dev`. `branch` must be provided with a value in `context_parameter`.
           * @enum {string}
           */
          context?:
            | "all"
            | "dev"
            | "branch-deploy"
            | "deploy-preview"
            | "production"
            | "branch";
          /** @description An additional parameter for custom branches. Currently, this is used for providing a branch name when `context=branch`. */
          context_parameter?: string;
          /** @description The environment variable's unencrypted value */
          value?: string;
        };
      };
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/environmentVariables/operation/deleteEnvVarValue | `deleteEnvVarValue`}
   *
   * Deletes a specific environment variable value.
   */
  deleteEnvVarValue: {
    parameters: {
      path: {
        /** Scope response to account_id */
        account_id: string;
        /** The environment variable value's ID */
        id: string;
        /** The environment variable key name (case-sensitive) */
        key: string;
      };
      query: {
        /** If provided, delete the value from an environment variable on this site */
        site_id?: string;
      };
    };
    responses: {
      /** No Content (success) */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/function/operation/searchSiteFunctions | `searchSiteFunctions`} */
  searchSiteFunctions: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        filter?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["siteFunction"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/form/operation/listSiteForms | `listSiteForms`} */
  listSiteForms: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["form"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/form/operation/deleteSiteForm | `deleteSiteForm`} */
  deleteSiteForm: {
    parameters: {
      path: {
        site_id: string;
        form_id: string;
      };
    };
    responses: {
      /** Deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listSiteSubmissions | `listSiteSubmissions`} */
  listSiteSubmissions: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["submission"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/listSiteFiles | `listSiteFiles`} */
  listSiteFiles: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["file"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/listSiteAssets | `listSiteAssets`} */
  listSiteAssets: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["asset"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/createSiteAsset | `createSiteAsset`} */
  createSiteAsset: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        name: string;
        size: number;
        content_type: string;
        visibility?: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["assetSignature"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/getSiteAssetInfo | `getSiteAssetInfo`} */
  getSiteAssetInfo: {
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["asset"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/updateSiteAsset | `updateSiteAsset`} */
  updateSiteAsset: {
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
      query: {
        state: string;
      };
    };
    responses: {
      /** Updated */
      200: {
        content: {
          "application/json": components["schemas"]["asset"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/asset/operation/deleteSiteAsset | `deleteSiteAsset`} */
  deleteSiteAsset: {
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
    };
    responses: {
      /** Deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/assetPublicSignature/operation/getSiteAssetPublicSignature | `getSiteAssetPublicSignature`} */
  getSiteAssetPublicSignature: {
    parameters: {
      path: {
        site_id: string;
        asset_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["assetPublicSignature"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/getSiteFileByPathName | `getSiteFileByPathName`} */
  getSiteFileByPathName: {
    parameters: {
      path: {
        site_id: string;
        file_path: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["file"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/purge/operation/purgeCache | `purgeCache`}
   *
   * Purges cached content from Netlify's CDN. Supports purging by Cache-Tag.
   */
  purgeCache: {
    responses: {
      /** OK */
      202: unknown;
      /** Invalid request parameters */
      400: unknown;
      /** Site not found */
      404: unknown;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["purge"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/listSiteSnippets | `listSiteSnippets`} */
  listSiteSnippets: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["snippet"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/createSiteSnippet | `createSiteSnippet`} */
  createSiteSnippet: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      201: {
        content: {
          "application/json": components["schemas"]["snippet"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["snippet"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/getSiteSnippet | `getSiteSnippet`} */
  getSiteSnippet: {
    parameters: {
      path: {
        site_id: string;
        snippet_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["snippet"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/updateSiteSnippet | `updateSiteSnippet`} */
  updateSiteSnippet: {
    parameters: {
      path: {
        site_id: string;
        snippet_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["snippet"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/snippet/operation/deleteSiteSnippet | `deleteSiteSnippet`} */
  deleteSiteSnippet: {
    parameters: {
      path: {
        site_id: string;
        snippet_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/metadata/operation/getSiteMetadata | `getSiteMetadata`} */
  getSiteMetadata: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["metadata"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/metadata/operation/updateSiteMetadata | `updateSiteMetadata`} */
  updateSiteMetadata: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["metadata"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/listSiteBuildHooks | `listSiteBuildHooks`} */
  listSiteBuildHooks: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["buildHook"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/createSiteBuildHook | `createSiteBuildHook`} */
  createSiteBuildHook: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["buildHook"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["buildHookSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/getSiteBuildHook | `getSiteBuildHook`} */
  getSiteBuildHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["buildHook"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/updateSiteBuildHook | `updateSiteBuildHook`} */
  updateSiteBuildHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["buildHookSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildHook/operation/deleteSiteBuildHook | `deleteSiteBuildHook`} */
  deleteSiteBuildHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/listSiteDeploys | `listSiteDeploys`} */
  listSiteDeploys: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        "deploy-previews"?: boolean;
        production?: boolean;
        state?:
          | "new"
          | "pending_review"
          | "accepted"
          | "rejected"
          | "enqueued"
          | "building"
          | "uploading"
          | "uploaded"
          | "preparing"
          | "prepared"
          | "processing"
          | "processed"
          | "ready"
          | "error"
          | "retrying";
        branch?: string;
        "latest-published"?: boolean;
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/createSiteDeploy | `createSiteDeploy`} */
  createSiteDeploy: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        "deploy-previews"?: boolean;
        production?: boolean;
        state?:
          | "new"
          | "pending_review"
          | "accepted"
          | "rejected"
          | "enqueued"
          | "building"
          | "uploading"
          | "uploaded"
          | "preparing"
          | "prepared"
          | "processing"
          | "processed"
          | "ready"
          | "error"
          | "retrying";
        branch?: string;
        "latest-published"?: boolean;
        title?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["deployFiles"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/getSiteDeploy | `getSiteDeploy`} */
  getSiteDeploy: {
    parameters: {
      path: {
        site_id: string;
        deploy_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/updateSiteDeploy | `updateSiteDeploy`} */
  updateSiteDeploy: {
    parameters: {
      path: {
        site_id: string;
        deploy_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["deployFiles"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/deleteSiteDeploy | `deleteSiteDeploy`} */
  deleteSiteDeploy: {
    parameters: {
      path: {
        deploy_id: string;
        site_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/cancelSiteDeploy | `cancelSiteDeploy`} */
  cancelSiteDeploy: {
    parameters: {
      path: {
        deploy_id: string;
      };
    };
    responses: {
      /** Cancelled */
      201: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/restoreSiteDeploy | `restoreSiteDeploy`} */
  restoreSiteDeploy: {
    parameters: {
      path: {
        site_id: string;
        deploy_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/listSiteBuilds | `listSiteBuilds`} */
  listSiteBuilds: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["build"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/createSiteBuild | `createSiteBuild`} */
  createSiteBuild: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["build"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["buildSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deployedBranch/operation/listSiteDeployedBranches | `listSiteDeployedBranches`} */
  listSiteDeployedBranches: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deployedBranch"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/unlinkSiteRepo | `unlinkSiteRepo`}
   *
   * [Beta] Unlinks the repo from the site.
   *
   * This action will also:
   * - Delete associated deploy keys
   * - Delete outgoing webhooks for the repo
   * - Delete the site's build hooks
   */
  unlinkSiteRepo: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["site"];
        };
      };
      /** Site not found */
      404: unknown;
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/getSiteBuild | `getSiteBuild`} */
  getSiteBuild: {
    parameters: {
      path: {
        build_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["build"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/buildLogMsg/operation/updateSiteBuildLog | `updateSiteBuildLog`} */
  updateSiteBuildLog: {
    parameters: {
      path: {
        build_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/notifyBuildStart | `notifyBuildStart`} */
  notifyBuildStart: {
    parameters: {
      path: {
        build_id: string;
      };
      query: {
        buildbot_version?: string;
        build_version?: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/build/operation/getAccountBuildStatus | `getAccountBuildStatus`} */
  getAccountBuildStatus: {
    parameters: {
      path: {
        account_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["buildStatus"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDNSForSite | `getDNSForSite`} */
  getDNSForSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["dnsZone"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/configureDNSForSite | `configureDNSForSite`} */
  configureDNSForSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["dnsZone"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/rollbackSiteDeploy | `rollbackSiteDeploy`} */
  rollbackSiteDeploy: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/getDeploy | `getDeploy`} */
  getDeploy: {
    parameters: {
      path: {
        deploy_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/deleteDeploy | `deleteDeploy`} */
  deleteDeploy: {
    parameters: {
      path: {
        deploy_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/lockDeploy | `lockDeploy`} */
  lockDeploy: {
    parameters: {
      path: {
        deploy_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deploy/operation/unlockDeploy | `unlockDeploy`} */
  unlockDeploy: {
    parameters: {
      path: {
        deploy_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deploy"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/file/operation/uploadDeployFile | `uploadDeployFile`} */
  uploadDeployFile: {
    parameters: {
      path: {
        deploy_id: string;
        path: string;
      };
      query: {
        size?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["file"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["uploadDeployFileFileBody"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/function/operation/uploadDeployFunction | `uploadDeployFunction`} */
  uploadDeployFunction: {
    parameters: {
      path: {
        deploy_id: string;
        name: string;
      };
      query: {
        runtime?: string;
        invocation_mode?: string;
        timeout?: number;
        size?: number;
      };
      header: {
        "X-Nf-Retry-Count"?: components["parameters"]["retryCount"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["function"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["uploadDeployFileFileBody"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listFormSubmissions | `listFormSubmissions`} */
  listFormSubmissions: {
    parameters: {
      path: {
        form_id: string;
      };
      query: {
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["submission"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/listHooksBySiteId | `listHooksBySiteId`} */
  listHooksBySiteId: {
    parameters: {
      query: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["hook"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/createHookBySiteId | `createHookBySiteId`} */
  createHookBySiteId: {
    parameters: {
      query: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      201: {
        content: {
          "application/json": components["schemas"]["hook"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["hook"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/getHook | `getHook`} */
  getHook: {
    parameters: {
      path: {
        hook_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["hook"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/updateHook | `updateHook`} */
  updateHook: {
    parameters: {
      path: {
        hook_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["hook"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["hook"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/deleteHook | `deleteHook`} */
  deleteHook: {
    parameters: {
      path: {
        hook_id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hook/operation/enableHook | `enableHook`} */
  enableHook: {
    parameters: {
      path: {
        hook_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["hook"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/hookType/operation/listHookTypes | `listHookTypes`} */
  listHookTypes: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["hookType"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/ticket/operation/createTicket | `createTicket`} */
  createTicket: {
    parameters: {
      query: {
        client_id: string;
      };
    };
    responses: {
      /** ok */
      201: {
        content: {
          "application/json": components["schemas"]["ticket"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/ticket/operation/showTicket | `showTicket`} */
  showTicket: {
    parameters: {
      path: {
        ticket_id: string;
      };
    };
    responses: {
      /** ok */
      200: {
        content: {
          "application/json": components["schemas"]["ticket"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accessToken/operation/exchangeTicket | `exchangeTicket`} */
  exchangeTicket: {
    parameters: {
      path: {
        ticket_id: string;
      };
    };
    responses: {
      /** ok */
      201: {
        content: {
          "application/json": components["schemas"]["accessToken"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/listDeployKeys | `listDeployKeys`} */
  listDeployKeys: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deployKey"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/createDeployKey | `createDeployKey`} */
  createDeployKey: {
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["deployKey"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/getDeployKey | `getDeployKey`} */
  getDeployKey: {
    parameters: {
      path: {
        key_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["deployKey"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/deployKey/operation/deleteDeployKey | `deleteDeployKey`} */
  deleteDeployKey: {
    parameters: {
      path: {
        key_id: string;
      };
    };
    responses: {
      /** Not Content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/listSitesForAccount | `listSitesForAccount`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [getEnvVars](#tag/environmentVariables/operation/getEnvVars) to retrieve site environment variables.
   */
  listSitesForAccount: {
    parameters: {
      query: {
        name?: string;
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
      path: {
        account_slug: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["site"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /**
   * API Reference: {@link https://open-api.netlify.com/#tag/site/operation/createSiteInTeam | `createSiteInTeam`}
   *
   * **Note:** Environment variable keys and values have moved from `build_settings.env` and `repo.env` to a new endpoint. Please use [createEnvVars](#tag/environmentVariables/operation/createEnvVars) to create environment variables for a site.
   */
  createSiteInTeam: {
    parameters: {
      query: {
        configure_dns?: boolean;
      };
      path: {
        account_slug: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["site"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["siteSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/listMembersForAccount | `listMembersForAccount`} */
  listMembersForAccount: {
    parameters: {
      path: {
        account_slug: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["member"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/addMemberToAccount | `addMemberToAccount`} */
  addMemberToAccount: {
    parameters: {
      path: {
        account_slug: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["member"][];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["accountAddMemberSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/getAccountMember | `getAccountMember`} */
  getAccountMember: {
    parameters: {
      path: {
        account_slug: string;
        member_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["member"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/updateAccountMember | `updateAccountMember`} */
  updateAccountMember: {
    parameters: {
      path: {
        account_slug: string;
        member_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["member"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["accountUpdateMemberSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/member/operation/removeAccountMember | `removeAccountMember`} */
  removeAccountMember: {
    parameters: {
      path: {
        account_slug: string;
        member_id: string;
      };
    };
    responses: {
      /** Not Content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/paymentMethod/operation/listPaymentMethodsForUser | `listPaymentMethodsForUser`} */
  listPaymentMethodsForUser: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["paymentMethod"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountType/operation/listAccountTypesForUser | `listAccountTypesForUser`} */
  listAccountTypesForUser: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["accountType"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/listAccountsForUser | `listAccountsForUser`} */
  listAccountsForUser: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["accountMembership"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/createAccount | `createAccount`} */
  createAccount: {
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["accountMembership"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["accountSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/getAccount | `getAccount`} */
  getAccount: {
    parameters: {
      path: {
        account_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["accountMembership"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/updateAccount | `updateAccount`} */
  updateAccount: {
    parameters: {
      path: {
        account_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["accountMembership"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["accountUpdateSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/accountMembership/operation/cancelAccount | `cancelAccount`} */
  cancelAccount: {
    parameters: {
      path: {
        account_id: string;
      };
    };
    responses: {
      /** Not Content */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/auditLog/operation/listAccountAuditEvents | `listAccountAuditEvents`} */
  listAccountAuditEvents: {
    parameters: {
      path: {
        account_id: string;
      };
      query: {
        query?: string;
        log_type?: string;
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["auditLog"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/listFormSubmission | `listFormSubmission`} */
  listFormSubmission: {
    parameters: {
      path: {
        submission_id: string;
      };
      query: {
        query?: string;
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["submission"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/submission/operation/deleteSubmission | `deleteSubmission`} */
  deleteSubmission: {
    parameters: {
      path: {
        submission_id: string;
      };
    };
    responses: {
      /** Deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/listServiceInstancesForSite | `listServiceInstancesForSite`} */
  listServiceInstancesForSite: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["serviceInstance"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/createServiceInstance | `createServiceInstance`} */
  createServiceInstance: {
    parameters: {
      path: {
        site_id: string;
        addon: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["serviceInstance"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["createServiceInstanceConfig"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/showServiceInstance | `showServiceInstance`} */
  showServiceInstance: {
    parameters: {
      path: {
        site_id: string;
        addon: string;
        instance_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["serviceInstance"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/updateServiceInstance | `updateServiceInstance`} */
  updateServiceInstance: {
    parameters: {
      path: {
        site_id: string;
        addon: string;
        instance_id: string;
      };
    };
    responses: {
      /** No Content */
      204: never;
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["createServiceInstanceConfig"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/serviceInstance/operation/deleteServiceInstance | `deleteServiceInstance`} */
  deleteServiceInstance: {
    parameters: {
      path: {
        site_id: string;
        addon: string;
        instance_id: string;
      };
    };
    responses: {
      /** Deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/getServices | `getServices`} */
  getServices: {
    parameters: {
      query: {
        search?: string;
      };
    };
    responses: {
      /** services */
      200: {
        content: {
          "application/json": components["schemas"]["service"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/showService | `showService`} */
  showService: {
    parameters: {
      path: {
        addonName: string;
      };
    };
    responses: {
      /** services */
      200: {
        content: {
          "application/json": components["schemas"]["service"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/service/operation/showServiceManifest | `showServiceManifest`} */
  showServiceManifest: {
    parameters: {
      path: {
        addonName: string;
      };
    };
    responses: {
      /** retrieving from provider */
      201: {
        content: {
          "application/json": { [key: string]: unknown };
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/user/operation/getCurrentUser | `getCurrentUser`} */
  getCurrentUser: {
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["user"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/getSplitTests | `getSplitTests`} */
  getSplitTests: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** split_tests */
      200: {
        content: {
          "application/json": components["schemas"]["splitTests"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/createSplitTest | `createSplitTest`} */
  createSplitTest: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["splitTest"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["splitTestSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/getSplitTest | `getSplitTest`} */
  getSplitTest: {
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
    responses: {
      /** split_test */
      200: {
        content: {
          "application/json": components["schemas"]["splitTest"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/updateSplitTest | `updateSplitTest`} */
  updateSplitTest: {
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["splitTest"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["splitTestSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/enableSplitTest | `enableSplitTest`} */
  enableSplitTest: {
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
    responses: {
      /** enable */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/splitTest/operation/disableSplitTest | `disableSplitTest`} */
  disableSplitTest: {
    parameters: {
      path: {
        site_id: string;
        split_test_id: string;
      };
    };
    responses: {
      /** disabled */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsZones | `getDnsZones`} */
  getDnsZones: {
    parameters: {
      query: {
        account_slug?: string;
      };
    };
    responses: {
      /** get all DNS zones the user has access to */
      200: {
        content: {
          "application/json": components["schemas"]["dnsZones"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/createDnsZone | `createDnsZone`} */
  createDnsZone: {
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["dnsZone"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["dnsZoneSetup"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsZone | `getDnsZone`} */
  getDnsZone: {
    parameters: {
      path: {
        zone_id: string;
      };
    };
    responses: {
      /** get a single DNS zone */
      200: {
        content: {
          "application/json": components["schemas"]["dnsZone"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/deleteDnsZone | `deleteDnsZone`} */
  deleteDnsZone: {
    parameters: {
      path: {
        zone_id: string;
      };
    };
    responses: {
      /** delete a single DNS zone */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/transferDnsZone | `transferDnsZone`} */
  transferDnsZone: {
    parameters: {
      path: {
        zone_id: string;
      };
      query: {
        /** the account of the dns zone */
        account_id: string;
        /** the account you want to transfer the dns zone to */
        transfer_account_id: string;
        /** the user you want to transfer the dns zone to */
        transfer_user_id: string;
      };
    };
    responses: {
      /** transfer a DNS zone to another account */
      200: {
        content: {
          "application/json": components["schemas"]["dnsZone"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getDnsRecords | `getDnsRecords`} */
  getDnsRecords: {
    parameters: {
      path: {
        zone_id: string;
      };
    };
    responses: {
      /** get all DNS records for a single DNS zone */
      200: {
        content: {
          "application/json": components["schemas"]["dnsRecords"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/createDnsRecord | `createDnsRecord`} */
  createDnsRecord: {
    parameters: {
      path: {
        zone_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["dnsRecord"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["dnsRecordCreate"];
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/getIndividualDnsRecord | `getIndividualDnsRecord`} */
  getIndividualDnsRecord: {
    parameters: {
      path: {
        zone_id: string;
        dns_record_id: string;
      };
    };
    responses: {
      /** get a single DNS record */
      200: {
        content: {
          "application/json": components["schemas"]["dnsRecord"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/dnsZone/operation/deleteDnsRecord | `deleteDnsRecord`} */
  deleteDnsRecord: {
    parameters: {
      path: {
        zone_id: string;
        dns_record_id: string;
      };
    };
    responses: {
      /** record deleted */
      204: never;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/listSiteDevServers | `listSiteDevServers`} */
  listSiteDevServers: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        page?: components["parameters"]["page"];
        per_page?: components["parameters"]["perPage"];
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["devServer"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/createSiteDevServer | `createSiteDevServer`} */
  createSiteDevServer: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        branch?: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["devServer"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/deleteSiteDevServers | `deleteSiteDevServers`} */
  deleteSiteDevServers: {
    parameters: {
      path: {
        site_id: string;
      };
      query: {
        branch?: string;
      };
    };
    responses: {
      /** OK */
      202: unknown;
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServer/operation/getSiteDevServer | `getSiteDevServer`} */
  getSiteDevServer: {
    parameters: {
      path: {
        site_id: string;
        dev_server_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["devServer"];
        };
      };
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/listSiteDevServerHooks | `listSiteDevServerHooks`} */
  listSiteDevServerHooks: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["devServerHook"][];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/createSiteDevServerHook | `createSiteDevServerHook`} */
  createSiteDevServerHook: {
    parameters: {
      path: {
        site_id: string;
      };
    };
    responses: {
      /** Created */
      201: {
        content: {
          "application/json": components["schemas"]["devServerHook"];
        };
      };
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["devServerHookSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/getSiteDevServerHook | `getSiteDevServerHook`} */
  getSiteDevServerHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        content: {
          "application/json": components["schemas"]["devServerHook"];
        };
      };
      default: components["responses"]["error"];
    };
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/updateSiteDevServerHook | `updateSiteDevServerHook`} */
  updateSiteDevServerHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
    requestBody: components["requestBodies"]["devServerHookSetup"];
  };
  /** API Reference: {@link https://open-api.netlify.com/#tag/devServerHook/operation/deleteSiteDevServerHook | `deleteSiteDevServerHook`} */
  deleteSiteDevServerHook: {
    parameters: {
      path: {
        site_id: string;
        id: string;
      };
    };
    responses: {
      /** No content */
      204: never;
      default: components["responses"]["error"];
    };
  };
}

export interface external {}
