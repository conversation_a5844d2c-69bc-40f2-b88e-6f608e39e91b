{"name": "<%= name %>", "version": "<%= version %>", "description": "<%= description %>", "author": "<%= author %>", "private": true, "license": "UNLICENSED", "scripts": {"format": "prettier --write \"**/*.js\"", "start": "babel-node index.js", "start:dev": "nodemon", "test": "jest", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.2.0"}, "devDependencies": {"@nestjs/testing": "^11.0.1", "@babel/core": "7.28.0", "@babel/node": "7.28.0", "@babel/plugin-proposal-decorators": "7.28.0", "@babel/plugin-transform-runtime": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/register": "7.27.1", "@babel/runtime": "7.28.2", "jest": "30.0.5", "nodemon": "3.1.10", "prettier": "3.6.2", "supertest": "7.1.4"}, "jest": {"moduleFileExtensions": ["js", "json"], "rootDir": "src", "testRegex": ".spec.js$", "coverageDirectory": "../coverage"}}