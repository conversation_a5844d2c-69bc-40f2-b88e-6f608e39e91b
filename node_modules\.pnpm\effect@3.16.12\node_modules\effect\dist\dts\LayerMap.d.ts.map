{"version": 3, "file": "LayerMap.d.ts", "sourceRoot": "", "sources": ["../../src/LayerMap.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAIrC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAGnC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAsC,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;;GAIG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK;IACrD,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IAEzB;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7B,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACjC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;KAC1E,EAAE,CAAC,CAAC,CAAA;IAEL;;OAEG;IACH,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAE9B;;OAEG;IACH,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;IAElE;;OAEG;IACH,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDG;AACH,eAAO,MAAM,IAAI,EAAE,CACjB,CAAC,EACD,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAEpC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EACrB,OAAO,CAAC,EAAE;IACR,QAAQ,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,aAAa,GAAG,SAAS,CAAA;CAC7D,GAAG,SAAS,KACV,MAAM,CAAC,MAAM,CAChB,QAAQ,CACN,CAAC,EACD,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,EAChE,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CACjE,EACD,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAuDhF,CAAA;AAEF;;;;GAIG;AACH,eAAO,MAAM,UAAU,GACrB,KAAK,CAAC,MAAM,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAE/D,QAAQ,MAAM,EACd,UAAU;IACR,QAAQ,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,aAAa,GAAG,SAAS,CAAA;CAC7D,GAAG,SAAS,KACZ,MAAM,CAAC,MAAM,CACd,QAAQ,CACN,MAAM,MAAM,EACZ,MAAM,CAAC,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,EACnF,MAAM,CAAC,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,IAAE,EAAE,MAAM,IAAE,CAAC,GAAG,IAAE,GAAG,KAAK,CACpF,EACD,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,IAAE,EAAE,MAAM,IAAE,CAAC,GAAG,IAAE,GAAG,KAAK,CAAC,CAC/C,CAAA;AAEtD;;;GAGG;AACH,MAAM,WAAW,QAAQ,CACvB,EAAE,CAAC,GAAG,CAAC,IAAI,EACX,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,MAAM,EACxB,EAAE,CAAC,GAAG,CAAC,CAAC,EACR,EAAE,CAAC,GAAG,CAAC,CAAC,EACR,EAAE,CAAC,GAAG,CAAC,CAAC,EACR,EAAE,CAAC,GAAG,CAAC,CAAC,EACR,EAAE,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAC9C,SAAQ,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAC3B,IAAI,EACJ,CAAC,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,EACnE,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,GACjF,CAAC,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CACxE,CAAA;IAED;;OAEG;IACH,QAAQ,CAAC,0BAA0B,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IAEhE;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;IAEjD;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAA;IAEtF;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;CAClE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDG;AACH,eAAO,MAAM,OAAO,GAAI,IAAI,QAE1B,KAAK,CAAC,EAAE,SAAS,MAAM,EACvB,MAAM,SAAS;IACb,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;CAC1D,GAAG;IACF,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;CAC5D,EACD,KAAK,CAAC,IAAI,SAAS,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAEjE,IAAI,EAAE,EACN,SAAS,MAAM,GAAG;IAChB,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,GAAG,SAAS,CAAA;IACxC,QAAQ,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,aAAa,GAAG,SAAS,CAAA;CAC7D,KACA,QAAQ,CACT,IAAI,EACJ,EAAE,EACF,MAAM,SAAS;IAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,GAAG,CAAA;CAAE,GAAG,CAAC,GACzD,MAAM,SAAS;IAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAA;CAAE,GAAG,MAAM,MAAM,GAC/D,KAAK,EACT,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EACvB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EACrB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EACvB,IAAI,CAAC,MAAM,CAAC,CAiCb,CAAA;AAED;;;;GAIG;AACH,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B;;;;OAIG;IACH,KAAY,GAAG,CAAC,OAAO,IAAI,OAAO,SAAS;QAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,GAAG,CAAA;KAAE,GAAG,CAAC,GACrF,OAAO,SAAS;QAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAA;KAAE,GAAG,MAAM,MAAM,GAChE,KAAK,CAAA;IAET;;;;OAIG;IACH,KAAY,MAAM,CAAC,OAAO,IAAI,OAAO,SAAS;QAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM,MAAM,CAAA;KAAE,GAAG,MAAM,GACvG,OAAO,SAAS;QAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAA;KAAE,GAAG,MAAM,CAAC,MAAM,MAAM,CAAC,GACxE,KAAK,CAAA;IAET;;;;OAIG;IACH,KAAY,OAAO,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;IAE7G;;;;OAIG;IACH,KAAY,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;IAE3G;;;;OAIG;IACH,KAAY,OAAO,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;CAC9G"}