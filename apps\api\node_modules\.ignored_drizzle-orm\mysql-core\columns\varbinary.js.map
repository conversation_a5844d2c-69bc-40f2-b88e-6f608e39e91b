{"version": 3, "sources": ["../../../src/mysql-core/columns/varbinary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlVarBinaryBuilderInitial<TName extends string> = MySqlVarBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlVarBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlVarBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlVarBinary'>>\n\textends MySqlColumnBuilder<T, MySqlVarbinaryOptions>\n{\n\tstatic readonly [entityKind]: string = 'MySqlVarBinaryBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: MySqlVarbinaryOptions) {\n\t\tsuper(name, 'string', 'MySqlVarBinary');\n\t\tthis.config.length = config?.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlVarBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlVarBinary<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlVarBinary<\n\tT extends ColumnBaseConfig<'string', 'MySqlVarBinary'>,\n> extends MySqlColumn<T, MySqlVarbinaryOptions> {\n\tstatic readonly [entityKind]: string = 'MySqlVarBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varbinary` : `varbinary(${this.length})`;\n\t}\n}\n\nexport interface MySqlVarbinaryOptions {\n\tlength: number;\n}\n\nexport function varbinary<TName extends string>(\n\tname: TName,\n\toptions: MySqlVarbinaryOptions,\n): MySqlVarBinaryBuilderInitial<TName> {\n\treturn new MySqlVarBinaryBuilder(name, options);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,aAAa,0BAA0B;AAWzC,MAAM,8BACJ,mBACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,SAAS,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBAEH,YAAsC;AAAA,EAC/C,QAAiB,UAAU,IAAY;AAAA,EAEvC,SAA6B,KAAK,OAAO;AAAA,EAEzC,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,cAAc,aAAa,KAAK,MAAM;AAAA,EAC1E;AACD;AAMO,SAAS,UACf,MACA,SACsC;AACtC,SAAO,IAAI,sBAAsB,MAAM,OAAO;AAC/C;", "names": []}