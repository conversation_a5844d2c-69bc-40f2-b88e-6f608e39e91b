"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MikroOrmHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const __1 = require("..");
const errors_1 = require("../../errors");
const health_check_1 = require("../../health-check");
const utils_1 = require("../../utils");
/**
 * The MikroOrmHealthIndicator contains health indicators
 * which are used for health checks related to MikroOrm
 *
 * @publicApi
 * @module TerminusModule
 */
let MikroOrmHealthIndicator = class MikroOrmHealthIndicator extends __1.HealthIndicator {
    /**
     * Initializes the MikroOrmHealthIndicator
     *
     * @param {ModuleRef} moduleRef The NestJS module reference
     */
    constructor(moduleRef) {
        super();
        this.moduleRef = moduleRef;
        this.checkDependantPackages();
    }
    /**
     * Checks if responds in (default) 1000ms and
     * returns a result object corresponding to the result
     * @param key The key which will be used for the result object
     * @param options The options for the ping
     *
     * @example
     * MikroOrmHealthIndicator.pingCheck('database', { timeout: 1500 });
     */
    pingCheck(key, options = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            this.checkDependantPackages();
            const connection = options.connection || this.getContextConnection();
            const timeout = options.timeout || 1000;
            if (!connection) {
                return this.getStatus(key, false);
            }
            try {
                yield this.pingDb(connection, timeout);
            }
            catch (error) {
                // Check if the error is a timeout error
                if (error instanceof utils_1.TimeoutError) {
                    throw new errors_1.TimeoutError(timeout, this.getStatus(key, false, {
                        message: `timeout of ${timeout}ms exceeded`,
                    }));
                }
                // Check if the error is a connection not found error
                if (error instanceof Error) {
                    throw new health_check_1.HealthCheckError(error.message, this.getStatus(key, false, {
                        message: error.message,
                    }));
                }
            }
            return this.getStatus(key, true);
        });
    }
    checkDependantPackages() {
        (0, utils_1.checkPackages)(['@mikro-orm/nestjs', '@mikro-orm/core'], this.constructor.name);
    }
    /**
     * Returns the connection of the current DI context
     */
    getContextConnection() {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const { MikroORM } = require('@mikro-orm/core');
        const mikro = this.moduleRef.get(MikroORM, { strict: false });
        const connection = mikro.em.getConnection();
        if (!connection) {
            return null;
        }
        return connection;
    }
    /**
     * Pings a mikro-orm connection
     *
     * @param connection The connection which the ping should get executed
     * @param timeout The timeout how long the ping should maximum take
     *
     */
    pingDb(connection, timeout) {
        return __awaiter(this, void 0, void 0, function* () {
            const isConnected = connection.isConnected();
            return yield (0, utils_1.promiseTimeout)(timeout, isConnected);
        });
    }
};
exports.MikroOrmHealthIndicator = MikroOrmHealthIndicator;
exports.MikroOrmHealthIndicator = MikroOrmHealthIndicator = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.TRANSIENT }),
    __metadata("design:paramtypes", [core_1.ModuleRef])
], MikroOrmHealthIndicator);
//# sourceMappingURL=mikro-orm.health.js.map