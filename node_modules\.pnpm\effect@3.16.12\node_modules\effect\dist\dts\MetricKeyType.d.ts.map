{"version": 3, "file": "MetricKeyType.d.ts", "sourceRoot": "", "sources": ["../../src/MetricKeyType.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,KAAK,gBAAgB,MAAM,uBAAuB,CAAA;AAC9D,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,OAAO,MAAqC,CAAA;AAE9E;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,OAAO,mBAAmB,CAAA;AAE5D;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,OAAO,MAAwC,CAAA;AAEpF;;;GAGG;AACH,MAAM,MAAM,sBAAsB,GAAG,OAAO,sBAAsB,CAAA;AAElE;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,OAAO,MAAoC,CAAA;AAE5E;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,kBAAkB,CAAA;AAE1D;;;GAGG;AACH,eAAO,MAAM,sBAAsB,EAAE,OAAO,MAAwC,CAAA;AAEpF;;;GAGG;AACH,MAAM,MAAM,sBAAsB,GAAG,OAAO,sBAAsB,CAAA;AAElE;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;GAGG;AACH,MAAM,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAE,SAAQ,aAAa,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ;CAAG;AAEhH;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC;;;OAGG;IACH,KAAY,OAAO,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAE7C;;;OAGG;IACH,KAAY,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG;QACxG,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;QACrD,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAA;QAC7B,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAA;KACzB,CAAA;IAED;;;OAGG;IACH,KAAY,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG;QACjF,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE,sBAAsB,CAAA;QACzD,QAAQ,CAAC,kBAAkB,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;KACnD,CAAA;IAED;;;OAGG;IACH,KAAY,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;QACpG,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE,kBAAkB,CAAA;QACjD,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAA;KACzB,CAAA;IAED;;;OAGG;IACH,KAAY,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG;QACjF,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE,sBAAsB,CAAA;QACzD,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,gBAAgB,CAAA;KACvD,CAAA;IAED;;;OAGG;IACH,KAAY,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG;QAChG,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;QACrD,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAA;QAClC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;QACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;KAC1C,CAAA;IAED;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG;QACtC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;YAC9B,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YACrC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;SACpC,CAAA;KACF;IAED;;;OAGG;IACH,KAAY,MAAM,CAAC,IAAI,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;QACxE;YACE,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;gBAC9B,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,IAAI,CAAA;aACpC,CAAA;SACF;KACF,GAAG,EAAE,GACF,KAAK,CAAA;IAET;;;OAGG;IACH,KAAY,OAAO,CAAC,IAAI,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;QACzE;YACE,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;gBAC9B,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,MAAM,GAAG,CAAA;aACvC,CAAA;SACF;KACF,GAAG,GAAG,GACH,KAAK,CAAA;CACV;AAED;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,OAAO,aAAa,CAAC,OAAO,CAAC,CAAC,CAAoB,CAAA;AAElG;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CACtB,OAAO,CAAC,EAAE;IACR,QAAQ,CAAC,kBAAkB,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;CAChE,GAAG,SAAS,KACV,aAAa,CAAC,SAA8B,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC,CAAkB,CAAA;AAE5F;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC,gBAAgB,KAAK,aAAa,CAAC,SAA8B,CAAA;AAEvH;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CACpB,OAAO,EAAE;IACP,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAA;IACvC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;CAC1C,KACE,aAAa,CAAC,OAA0B,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,OAAO,CAA4B,CAAA;AAE7G;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAyB,CAAA;AAE9G;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,SAAmC,CAAA;AAEnG;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAuB,CAAA;AAExG;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,SAAmC,CAAA;AAEnG;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,aAAa,CAAC,OAA+B,CAAA"}