{"version": 3, "file": "Random.d.ts", "sourceRoot": "", "sources": ["../../src/Random.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAG1C,OAAO,KAAK,KAAK,gBAAgB,MAAM,uBAAuB,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,OAAO,MAA8B,CAAA;AAEhE;;;GAGG;AACH,MAAM,MAAM,YAAY,GAAG,OAAO,YAAY,CAAA;AAE9C;;;GAGG;AACH,MAAM,WAAW,MAAM;IACrB,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,YAAY,CAAA;IACrC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACpC;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC5C;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACvC;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAC1D;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAC/D;;OAEG;IACH,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACjE;AAED;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAwB,CAAA;AAE/D;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAA2B,CAAA;AAErE;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAA+B,CAAA;AAE9E;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAA6B,CAAA;AAEvG;;;;;;GAMG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAkC,CAAA;AAEjH;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAA2B,CAAA;AAE3G;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,SAAS,QAAQ,CAAC,OAAO,CAAC,EAClD,QAAQ,EAAE,IAAI,KACX,IAAI,SAAS,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAC3E,IAAI,SAAS,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GACpE,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,sBAAsB,CAAC,GAC/E,KAA8B,CAAA;AAElC;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAC9E,CAAA;AAE5B;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAsB,CAAA;AAErE;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,MAAsB,CAAA"}