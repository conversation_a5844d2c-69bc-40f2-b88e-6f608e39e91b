{"version": 3, "file": "LogLevel.d.ts", "sourceRoot": "", "sources": ["../../src/LogLevel.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAI1C,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAE7C;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;AAElF;;;GAGG;AACH,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAA;AAEtC;;;GAGG;AACH,MAAM,WAAW,GAAI,SAAQ,QAAQ;IACnC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAA;IACpB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAA;IACrB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,OAAQ,SAAQ,QAAQ;IACvC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,IAAK,SAAQ,QAAQ;IACpC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,IAAK,SAAQ,QAAQ;IACpC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAA;IACrB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;IAClB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,QAA2B,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,QAA6B,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,QAA6B,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,QAA+B,CAAA;AAErD;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,QAA4B,CAAA;AAE/C;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,QAA6B,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,QAA6B,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,QAA4B,CAAA;AAE/C;;;GAGG;AACH,eAAO,MAAM,SAAS,qBAAoB,CAAA;AAE1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAClF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAK/E,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAGvC,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC7C;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAA;CAClB,CAAA;AAEzB;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC7C;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAA;CACT,CAAA;AAElC;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC7C;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAA;CACf,CAAA;AAE5B;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAA;IAC7C;;;OAGG;IACH,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAA;CACN,CAAA;AAErC;;;GAGG;AACH,eAAO,MAAM,WAAW,GAAI,SAAS,OAAO,KAAG,QAmB9C,CAAA"}