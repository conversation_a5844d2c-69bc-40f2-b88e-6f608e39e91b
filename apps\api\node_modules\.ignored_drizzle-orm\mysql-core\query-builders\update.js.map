{"version": 3, "sources": ["../../../src/mysql-core/query-builders/update.ts"], "sourcesContent": ["import type { GetColumnData } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type {\n\tAnyQueryResultHKT,\n\tMySqlSession,\n\tPreparedQueryConfig,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n\tQueryResultHKT,\n\tQueryResultKind,\n} from '~/mysql-core/session.ts';\nimport type { MySqlTable } from '~/mysql-core/table.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { mapUpdateSet, type UpdateSet } from '~/utils.ts';\nimport type { SelectedFieldsOrdered } from './select.types.ts';\nimport type { Subquery } from '~/subquery.ts';\n\nexport interface MySqlUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: MySqlTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type MySqlUpdateSetSource<TTable extends MySqlTable> =\n\t& {\n\t\t[Key in keyof TTable['_']['columns']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key], 'query'>\n\t\t\t| SQL;\n\t}\n\t& {};\n\nexport class MySqlUpdateBuilder<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n> {\n\tstatic readonly [entityKind]: string = 'MySqlUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: MySqlSession,\n\t\tprivate dialect: MySqlDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tset(values: MySqlUpdateSetSource<TTable>): MySqlUpdateBase<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\treturn new MySqlUpdateBase(this.table, mapUpdateSet(this.table, values), this.session, this.dialect, this.withList);\n\t}\n}\n\nexport type MySqlUpdateWithout<\n\tT extends AnyMySqlUpdateBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tMySqlUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['preparedQueryHKT'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type MySqlUpdatePrepare<T extends AnyMySqlUpdateBase> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tPreparedQueryConfig & {\n\t\texecute: QueryResultKind<T['_']['queryResult'], never>;\n\t\titerator: never;\n\t},\n\ttrue\n>;\n\nexport type MySqlUpdateDynamic<T extends AnyMySqlUpdateBase> = MySqlUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT']\n>;\n\nexport type MySqlUpdate<\n\tTTable extends MySqlTable = MySqlTable,\n\tTQueryResult extends QueryResultHKT = AnyQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n> = MySqlUpdateBase<TTable, TQueryResult, TPreparedQueryHKT, true, never>;\n\nexport type AnyMySqlUpdateBase = MySqlUpdateBase<any, any, any, any, any>;\n\nexport interface MySqlUpdateBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<QueryResultKind<TQueryResult, never>>, SQLWrapper {\n\treadonly _: {\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t};\n}\n\nexport class MySqlUpdateBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<QueryResultKind<TQueryResult, never>> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'MySqlUpdate';\n\n\tprivate config: MySqlUpdateConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: MySqlSession,\n\t\tprivate dialect: MySqlDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList };\n\t}\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t * \n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t * \n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t * \n\t * @param where the 'where' clause.\n\t * \n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t * \n\t * ```ts\n\t * // Update all cars with green color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t * \n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t * \n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t * \n\t * // Update all cars with the green or blue color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): MySqlUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tprepare(): MySqlUpdatePrepare<this> {\n\t\treturn this.session.prepareQuery(\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t) as MySqlUpdatePrepare<this>;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this.prepare().execute(placeholderValues);\n\t};\n\n\tprivate createIterator = (): ReturnType<this['prepare']>['iterator'] => {\n\t\tconst self = this;\n\t\treturn async function*(placeholderValues) {\n\t\t\tyield* self.prepare().iterator(placeholderValues);\n\t\t};\n\t};\n\n\titerator = this.createIterator();\n\n\t$dynamic(): MySqlUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAY3B,SAAS,oBAAoB;AAE7B,SAAS,oBAAoC;AAoBtC,MAAM,mBAIX;AAAA,EAOD,YACS,OACA,SACA,SACA,UACP;AAJO;AACA;AACA;AACA;AAAA,EACN;AAAA,EAXH,QAAiB,UAAU,IAAY;AAAA,EAavC,IAAI,QAAgG;AACnG,WAAO,IAAI,gBAAgB,KAAK,OAAO,aAAa,KAAK,OAAO,MAAM,GAAG,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ;AAAA,EACnH;AACD;AAwDO,MAAM,wBASH,aAAyE;AAAA,EAKlF,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,SAAS;AAAA,EACtC;AAAA,EAbA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8CR,MAAM,OAAqE;AAC1E,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,UAAoC;AACnC,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,IACb;AAAA,EACD;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+C;AACvE,UAAM,OAAO;AACb,WAAO,iBAAgB,mBAAmB;AACzC,aAAO,KAAK,QAAQ,EAAE,SAAS,iBAAiB;AAAA,IACjD;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,eAAe;AAAA,EAE/B,WAAqC;AACpC,WAAO;AAAA,EACR;AACD;", "names": []}