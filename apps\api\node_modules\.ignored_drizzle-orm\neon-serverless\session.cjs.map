{"version": 3, "sources": ["../../src/neon-serverless/session.ts"], "sourcesContent": ["import {\n\ttype Client,\n\tPool,\n\ttype PoolClient,\n\ttype QueryArrayConfig,\n\ttype QueryConfig,\n\ttype QueryResult,\n\ttype QueryResultRow,\n} from '@neondatabase/serverless';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgTransactionConfig, PreparedQueryConfig, QueryResultHKT } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nexport type NeonClient = Pool | PoolClient | Client;\n\nexport class NeonPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'NeonPreparedQuery';\n\n\tprivate rawQueryConfig: QueryConfig;\n\tprivate queryConfig: QueryArrayConfig;\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQueryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t};\n\t\tthis.queryConfig = {\n\t\t\tname,\n\t\t\ttext: queryString,\n\t\t\trowMode: 'array',\n\t\t};\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\n\t\tconst { fields, client, rawQueryConfig: rawQuery, queryConfig: query, joinsNotNullableMap, customResultMapper } =\n\t\t\tthis;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client.query(rawQuery, params);\n\t\t}\n\n\t\tconst result = await client.query(query, params);\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(result.rows)\n\t\t\t: result.rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\treturn this.client.query(this.rawQueryConfig, params).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\tthis.logger.logQuery(this.rawQueryConfig.text, params);\n\t\treturn this.client.query(this.queryConfig, params).then((result) => result.rows);\n\t}\n}\n\nexport interface NeonSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NeonQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NeonSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NeonSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new NeonPreparedQuery(this.client, query.sql, query.params, this.logger, fields, name, customResultMapper);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResult> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.query({\n\t\t\trowMode: 'array',\n\t\t\ttext: query,\n\t\t\tvalues: params,\n\t\t});\n\t\treturn result;\n\t}\n\n\tasync queryObjects<T extends QueryResultRow>(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<QueryResult<T>> {\n\t\treturn this.client.query<T>(query, params);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tconst session = this.client instanceof Pool // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t? new NeonSession(await this.client.connect(), this.dialect, this.schema, this.options)\n\t\t\t: this;\n\t\tconst tx = new NeonTransaction(this.dialect, session, this.schema);\n\t\tawait tx.execute(sql`begin ${tx.getTransactionConfigSQL(config)}`);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (error) {\n\t\t\tawait tx.execute(sql`rollback`);\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tif (this.client instanceof Pool) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\t(session.client as PoolClient).release();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class NeonTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NeonQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NeonTransaction';\n\n\toverride async transaction<T>(transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new NeonTransaction(this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport interface NeonQueryResultHKT extends QueryResultHKT {\n\ttype: QueryResult<Assume<this['row'], QueryResultRow>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAQO;AACP,oBAA2B;AAE3B,oBAA2B;AAE3B,qBAA8B;AAG9B,qBAA2C;AAE3C,iBAAkD;AAClD,mBAA0C;AAInC,MAAM,0BAAyD,+BAAmB;AAAA,EAMxF,YACS,QACR,aACQ,QACA,QACA,QACR,MACQ,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAR1B;AAEA;AACA;AACA;AAEA;AAGR,SAAK,iBAAiB;AAAA,MACrB;AAAA,MACA,MAAM;AAAA,IACP;AACA,SAAK,cAAc;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EAxBA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAuBR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AAErD,UAAM,EAAE,QAAQ,QAAQ,gBAAgB,UAAU,aAAa,OAAO,qBAAqB,mBAAmB,IAC7G;AACD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAM,UAAU,MAAM;AAAA,IACrC;AAEA,UAAM,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM;AAE/C,WAAO,qBACJ,mBAAmB,OAAO,IAAI,IAC9B,OAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EAC1F;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AACrD,WAAO,KAAK,OAAO,MAAM,KAAK,gBAAgB,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EACnF;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAC9D,SAAK,OAAO,SAAS,KAAK,eAAe,MAAM,MAAM;AACrD,WAAO,KAAK,OAAO,MAAM,KAAK,aAAa,MAAM,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChF;AACD;AAMO,MAAM,oBAGH,yBAAoD;AAAA,EAK7D,YACS,QACR,SACQ,QACA,UAA8B,CAAC,GACtC;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,oBACqB;AACrB,WAAO,IAAI,kBAAkB,KAAK,QAAQ,MAAM,KAAK,MAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM,kBAAkB;AAAA,EACjH;AAAA,EAEA,MAAM,MAAM,OAAe,QAAyC;AACnE,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,MAAM;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,IACT,CAAC;AACD,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,aACL,OACA,QAC0B;AAC1B,WAAO,KAAK,OAAO,MAAS,OAAO,MAAM;AAAA,EAC1C;AAAA,EAEA,MAAe,YACd,aACA,SAA8B,CAAC,GAClB;AACb,UAAM,UAAU,KAAK,kBAAkB,yBACpC,IAAI,YAAY,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,IACpF;AACH,UAAM,KAAK,IAAI,gBAAgB,KAAK,SAAS,SAAS,KAAK,MAAM;AACjE,UAAM,GAAG,QAAQ,uBAAY,GAAG,wBAAwB,MAAM,CAAC,EAAE;AACjE,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,sBAAW;AAC5B,aAAO;AAAA,IACR,SAAS,OAAO;AACf,YAAM,GAAG,QAAQ,wBAAa;AAC9B,YAAM;AAAA,IACP,UAAE;AACD,UAAI,KAAK,kBAAkB,wBAAM;AAChC,QAAC,QAAQ,OAAsB,QAAQ;AAAA,MACxC;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,wBAGH,6BAAwD;AAAA,EACjE,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAe,YAAe,aAAoF;AACjH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI,gBAAgB,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAC5F,UAAM,GAAG,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,GAAG;AACX,YAAM,GAAG,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": []}