{"version": 3, "file": "ScopedCache.d.ts", "sourceRoot": "", "sources": ["../../src/ScopedCache.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AAEtC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,OAAO,MAAmC,CAAA;AAE1E;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,OAAO,iBAAiB,CAAA;AAExD;;;GAGG;AACH,MAAM,WAAW,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,KAAK,CAC/D,SAAQ,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,QAAQ;IAEzD;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;IAE5E;;;OAGG;IACH,iBAAiB,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;IAEpF;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;IAEpD;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAE1C;;OAEG;IACH,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;IAEpE;;;;OAIG;IACH,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;IAEvD;;OAEG;IACH,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAEzC;;OAEG;IACH,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAE3C;;;;;;;OAOG;IACH,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAE7C;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;CACrC;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK;QACpD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;YAC5B,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;YAC9B,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;SAC/B,CAAA;KACF;CACF;AAED;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EAChE,OAAO,EAAE;IACP,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAA;IACvD,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,aAAa,CAAA;CAC5C,KACE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,WAAW,CAAiB,CAAA;AAEpG;;;;;;;GAOG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EACpE,OAAO,EAAE;IACP,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,CAAA;IACvD,QAAQ,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,QAAQ,CAAC,aAAa,CAAA;CAC/E,KACE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,WAAW,CAAqB,CAAA;AAExG;;;;;GAKG;AACH,MAAM,MAAM,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,IAAI,CACnE,GAAG,EAAE,GAAG,KACL,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA"}