import{createRequire}from"module";const require=createRequire(import.meta.url);
import{fileURLToPath as Fr}from"url";import{Buffer as _r}from"node:buffer";import Er from"node:process";import{pipeline as Pr,Readable as Lr}from"node:stream";import{promisify as Ar}from"node:util";var qr=globalThis.Buffer?e=>Buffer.from(e,"base64").toString():e=>atob(e),Re=globalThis.Buffer?e=>Buffer.from(e).toString("base64"):e=>btoa(e);var be=e=>{throw TypeError(e)},ke=(e,t,r)=>t.has(e)||be("Cannot "+r),y=(e,t,r)=>(ke(e,t,"read from private field"),r?r.call(e):t.get(e)),q=(e,t,r)=>t.has(e)?be("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),H=(e,t,r,n)=>(ke(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),X=(e=>(e.Delete="delete",e.Read="read",e.Write="write",e))(X||{}),Ct={invalid_vary:"Responses must not use unsupported directives of the `Netlify-Vary` header (https://ntl.fyi/cache_api_invalid_vary).",no_cache:"Responses must not set cache control headers with the `private`, `no-cache` or `no-store` directives (https://ntl.fyi/cache_api_no_cache).",low_ttl:"Responses must have a cache control header with a `max-age` or `s-maxage` directive (https://ntl.fyi/cache_api_low_ttl).",no_directive:"Responses must have a cache control header with caching directives (https://ntl.fyi/cache_api_no_directive).",no_ttl:"Responses must have a cache control header with a `max-age` or `s-maxage` directive (https://ntl.fyi/cache_api_no_ttl).",no_status:"Responses must specify a status code (https://ntl.fyi/cache_api_no_status).",invalid_directive:"Responses must have a cache control header with caching directives (https://ntl.fyi/cache_api_invalid_directive).",status:"Responses must have a status code between 200 and 299 (https://ntl.fyi/cache_api_status)."},bt="The server has returned an unexpected error (https://ntl.fyi/cache_api_error).",kt="netlify-programmable-error",vt="netlify-programmable-headers",It="netlify-programmable-status",_t="netlify-programmable-store",Et="netlify-forwarded-host",Pt="user-agent",Se=new Set(["http:","https:"]),Lt=new Set(["cookie","content-encoding","content-length"]),J=Symbol("getInternalHeaders"),Ce=Symbol("serializeResourceHeaders"),A,Y,F,At=class{constructor({getContext:e,name:t,userAgent:r}){q(this,A),q(this,Y),q(this,F),H(this,A,e),H(this,Y,t),H(this,F,r)}[J](e){let{host:t,token:r}=e,n={Authorization:`Bearer ${r}`,[_t]:y(this,Y)};return t&&(n[Et]=t),y(this,F)&&(n[Pt]=y(this,F)),n}[Ce](e){let t={};return e.forEach((r,n)=>{Lt.has(n)||(n==="set-cookie"?(t[n]=t[n]||[],t[n].push(r)):t[n]=r.split(","))}),Re(JSON.stringify(t))}async add(e){await this.put(new Request(e),await fetch(e))}async addAll(e){await Promise.allSettled(e.map(t=>this.add(t)))}async delete(e){let t=y(this,A).call(this,{operation:"delete"});if(t){let r=ce(e);await fetch(`${t.url}/${he(r)}`,{headers:this[J](t),method:"DELETE"})}return!0}async keys(e){return[]}async match(e){try{let t=y(this,A).call(this,{operation:"read"});if(!t)return;let r=ce(e),n=`${t.url}/${he(r)}`,o=await fetch(n,{headers:this[J](t),method:"GET"});return o.ok?o:void 0}catch{}}async matchAll(e,t){if(!e)return[];let r=await this.match(e);return r?[r]:[]}async put(e,t){var c,i,s;if(!t.ok)throw new TypeError(`Cannot cache response with status ${t.status}.`);if(e instanceof Request&&e.method!=="GET")throw new TypeError(`Cannot cache response to ${e.method} request.`);if(t.status===206)throw new TypeError("Cannot cache response to a range request (206 Partial Content).");if((c=t.headers.get("vary"))!=null&&c.includes("*"))throw new TypeError("Cannot cache response with 'Vary: *' header.");let r=y(this,A).call(this,{operation:"write"});if(!r)return;let n=ce(e),o=await fetch(`${r.url}/${he(n)}`,{body:t.body,headers:{...this[J](r),[vt]:this[Ce](t.headers),[It]:t.status.toString()},duplex:"half",method:"POST"});if(!o.ok){let h=((i=o.headers)==null?void 0:i.get(kt))??"",a=Ct[h]||bt;(s=r.logger)==null||s.call(r,`Failed to write to the cache: ${a}`)}}};A=new WeakMap;Y=new WeakMap;F=new WeakMap;var ce=e=>{let t;if(e instanceof Request)t=new URL(e.url);else try{t=new URL(String(e))}catch{throw new TypeError(`${e} is not a valid URL.`)}if(!Se.has(t.protocol))throw new TypeError(`Cannot cache response for URL with unsupported protocol (${t.protocol}). Supported protocols are ${[...Se].join(", ")}.`);return t},he=e=>encodeURIComponent(e.toString()),K,k,ve=class{constructor(e){q(this,K),q(this,k),H(this,K,e),H(this,k,new Map)}open(e){let t=y(this,k).get(e);return t||(t=new At({...y(this,K),name:e}),y(this,k).set(e,t)),Promise.resolve(t)}has(e){return Promise.resolve(y(this,k).has(e))}delete(e){return Promise.resolve(y(this,k).delete(e))}keys(){return Promise.resolve([...y(this,k).keys()])}async match(e,t){var r;if(t!=null&&t.cacheName)return(r=y(this,k).get(t.cacheName))==null?void 0:r.match(e);for(let n of y(this,k).values())if(await n.match(e)===void 0)return}};K=new WeakMap;k=new WeakMap;var T=class e extends Error{constructor(t){super(t),this.name="NetlifyUserError",Object.setPrototypeOf(this,e.prototype)}};var Tt={"cache-api-read":100,"cache-api-write":20},Q=class{constructor(t=Tt){this.counts=new Map,this.limits=t}register(t){let r=this.counts.get(t)||0,n=this.limits[t]||0;return r>=n?!1:(this.counts.set(t,r+1),!0)}};import{AsyncLocalStorage as Nt}from"node:async_hooks";var w=new Nt;import{env as Ie}from"node:process";var Z=()=>!!Ie.NETLIFY_DEV||!!Ie.NETLIFY_LOCAL;var _e="__nfSystemLog",N=(e,t)=>{Z()||console.log(_e,{msg:e,fields:t})},ue=e=>{console.log(_e,JSON.stringify(e))};var Ee=e=>{let{pcToken:t,pcURL:r,rawUrl:n}=e;return!t||!r?null:{host:new URL(n).hostname,token:t,url:new URL("/.netlify/cache",r).toString()}},$t=()=>{globalThis.caches=new ve({getContext:({operation:e})=>{let t=w.getStore();if(!(t!=null&&t.operationCounter))throw new T("The Cache API must be used within the scope of the request handler. Refer to https://ntl.fyi/cache-api-scope for more information.");let{cacheAPI:r,operationCounter:n}=t,o=e===X.Delete||e===X.Write?"cache-api-write":"cache-api-read";return n.register(o)?r||(N("missing Cache API metadata in request"),null):(console.log(`You've exceeded the number of allowed Cache API ${o==="cache-api-write"?"writes":"reads"} for a single invocation. Refer to https://ntl.fyi/cache-api-limits for more information.`),null)},userAgent:"netlify-functions"})};$t();var Pe="x-nf-client-connection-ip",Le="x-nf-geo",Ae="x-nf-account-id",ee="x-nf-deploy-id",Te="x-nf-deploy-context",Ne="x-nf-deploy-published",$e="x-nf-site-id",Ue="x-nf-request-flags",le="x-nf-invocation-metadata",Oe="x-nf-request-id",De="x-nf-start-timestamp",Ut=255,Fe=e=>{let t=new Headers;return Object.entries(e).forEach(([r,n])=>{if(n!==void 0)try{t.set(r.toLowerCase(),n)}catch(o){if([...n].every(i=>i.codePointAt(0)<=Ut))throw o;console.warn(`Discarded request header '${r}' because it contains non-ASCII characters`)}}),t},qe=e=>{let t={};for(let[r,n]of e.entries())r in t?t[r].push(n):t[r]=[n];return t};var te=e=>({id:e.headers.get(Ae)??""});var re=e=>({context:e.get(Te)??"",id:e.get(ee)??"",published:e.get(Ne)==="1"});import{Buffer as Ot}from"buffer";var se=Symbol("Request flags"),j=Symbol("Request route"),Dt=typeof Request>"u"?class{}:Request,He,ne=class extends(He=Dt,se,j,He){},je=(e,t)=>{if(!(e==null||e===""))return t?Ot.from(e,"base64"):e};var M=class{#e;#t;constructor(t){this.#e=new Set,this.#t=t}get(t){let r=this.#t[t];return r!==void 0&&this.#e.add(t),r}get evaluations(){return this.#e}},Me=e=>e[se]??new M({}),Be=(e,t)=>{e[se]=t};import{Buffer as Ft}from"node:buffer";var We=e=>{if(e===null)return{};try{let{postal_code:t,...r}=JSON.parse(Ft.from(e,"base64").toString("utf-8"));return Object.fromEntries(Object.entries({...r,postalCode:t}).filter(([,o])=>o!==void 0))}catch{return{}}};var Ge=e=>e??"";var G=class{constructor(e,t,r,n,o,c){this.type=3,this.name="",this.prefix="",this.value="",this.suffix="",this.modifier=3,this.type=e,this.name=t,this.prefix=r,this.value=n,this.suffix=o,this.modifier=c}hasCustomName(){return this.name!==""&&typeof this.name!="number"}},qt=/[$_\p{ID_Start}]/u,Ht=/[$_\u200C\u200D\p{ID_Continue}]/u,pe=".*";function jt(e,t){return(t?/^[\x00-\xFF]*$/:/^[\x00-\x7F]*$/).test(e)}function Je(e,t=!1){let r=[],n=0;for(;n<e.length;){let o=e[n],c=function(i){if(!t)throw new TypeError(i);r.push({type:"INVALID_CHAR",index:n,value:e[n++]})};if(o==="*"){r.push({type:"ASTERISK",index:n,value:e[n++]});continue}if(o==="+"||o==="?"){r.push({type:"OTHER_MODIFIER",index:n,value:e[n++]});continue}if(o==="\\"){r.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if(o==="{"){r.push({type:"OPEN",index:n,value:e[n++]});continue}if(o==="}"){r.push({type:"CLOSE",index:n,value:e[n++]});continue}if(o===":"){let i="",s=n+1;for(;s<e.length;){let h=e.substr(s,1);if(s===n+1&&qt.test(h)||s!==n+1&&Ht.test(h)){i+=e[s++];continue}break}if(!i){c(`Missing parameter name at ${n}`);continue}r.push({type:"NAME",index:n,value:i}),n=s;continue}if(o==="("){let i=1,s="",h=n+1,a=!1;if(e[h]==="?"){c(`Pattern cannot start with "?" at ${h}`);continue}for(;h<e.length;){if(!jt(e[h],!1)){c(`Invalid character '${e[h]}' at ${h}.`),a=!0;break}if(e[h]==="\\"){s+=e[h++]+e[h++];continue}if(e[h]===")"){if(i--,i===0){h++;break}}else if(e[h]==="("&&(i++,e[h+1]!=="?")){c(`Capturing groups are not allowed at ${h}`),a=!0;break}s+=e[h++]}if(a)continue;if(i){c(`Unbalanced pattern at ${n}`);continue}if(!s){c(`Missing pattern at ${n}`);continue}r.push({type:"REGEX",index:n,value:s}),n=h;continue}r.push({type:"CHAR",index:n,value:e[n++]})}return r.push({type:"END",index:n,value:""}),r}function Ye(e,t={}){let r=Je(e);t.delimiter??(t.delimiter="/#?"),t.prefixes??(t.prefixes="./");let n=`[^${R(t.delimiter)}]+?`,o=[],c=0,i=0,s="",h=new Set,a=l=>{if(i<r.length&&r[i].type===l)return r[i++].value},u=()=>a("OTHER_MODIFIER")??a("ASTERISK"),d=l=>{let p=a(l);if(p!==void 0)return p;let{type:m,index:C}=r[i];throw new TypeError(`Unexpected ${m} at ${C}, expected ${l}`)},x=()=>{let l="",p;for(;p=a("CHAR")??a("ESCAPED_CHAR");)l+=p;return l},U=l=>l,P=t.encodePart||U,S="",L=l=>{S+=l},O=()=>{S.length&&(o.push(new G(3,"","",P(S),"",3)),S="")},z=(l,p,m,C,D)=>{let g=3;switch(D){case"?":g=1;break;case"*":g=0;break;case"+":g=2;break}if(!p&&!m&&g===3){L(l);return}if(O(),!p&&!m){if(!l)return;o.push(new G(3,"","",P(l),"",g));return}let f;m?m==="*"?f=pe:f=m:f=n;let v=2;f===n?(v=1,f=""):f===pe&&(v=0,f="");let b;if(p?b=p:m&&(b=c++),h.has(b))throw new TypeError(`Duplicate name '${b}'.`);h.add(b),o.push(new G(v,b,P(l),f,P(C),g))};for(;i<r.length;){let l=a("CHAR"),p=a("NAME"),m=a("REGEX");if(!p&&!m&&(m=a("ASTERISK")),p||m){let g=l??"";t.prefixes.indexOf(g)===-1&&(L(g),g=""),O();let f=u();z(g,p,m,"",f);continue}let C=l??a("ESCAPED_CHAR");if(C){L(C);continue}if(a("OPEN")){let g=x(),f=a("NAME"),v=a("REGEX");!f&&!v&&(v=a("ASTERISK"));let b=x();d("CLOSE");let St=u();z(g,f,v,b,St);continue}O(),d("END")}return o}function R(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function Ve(e){return e&&e.ignoreCase?"ui":"u"}function Mt(e,t,r){return Ke(Ye(e,r),t,r)}function $(e){switch(e){case 0:return"*";case 1:return"?";case 2:return"+";case 3:return""}}function Ke(e,t,r={}){r.delimiter??(r.delimiter="/#?"),r.prefixes??(r.prefixes="./"),r.sensitive??(r.sensitive=!1),r.strict??(r.strict=!1),r.end??(r.end=!0),r.start??(r.start=!0),r.endsWith="";let n=r.start?"^":"";for(let s of e){if(s.type===3){s.modifier===3?n+=R(s.value):n+=`(?:${R(s.value)})${$(s.modifier)}`;continue}t&&t.push(s.name);let h=`[^${R(r.delimiter)}]+?`,a=s.value;if(s.type===1?a=h:s.type===0&&(a=pe),!s.prefix.length&&!s.suffix.length){s.modifier===3||s.modifier===1?n+=`(${a})${$(s.modifier)}`:n+=`((?:${a})${$(s.modifier)})`;continue}if(s.modifier===3||s.modifier===1){n+=`(?:${R(s.prefix)}(${a})${R(s.suffix)})`,n+=$(s.modifier);continue}n+=`(?:${R(s.prefix)}`,n+=`((?:${a})(?:`,n+=R(s.suffix),n+=R(s.prefix),n+=`(?:${a}))*)${R(s.suffix)})`,s.modifier===0&&(n+="?")}let o=`[${R(r.endsWith)}]|$`,c=`[${R(r.delimiter)}]`;if(r.end)return r.strict||(n+=`${c}?`),r.endsWith.length?n+=`(?=${o})`:n+="$",new RegExp(n,Ve(r));r.strict||(n+=`(?:${c}(?=${o}))?`);let i=!1;if(e.length){let s=e[e.length-1];s.type===3&&s.modifier===3&&(i=r.delimiter.indexOf(s)>-1)}return i||(n+=`(?=${c}|${o})`),new RegExp(n,Ve(r))}var E={delimiter:"",prefixes:"",sensitive:!0,strict:!0},Bt={delimiter:".",prefixes:"",sensitive:!0,strict:!0},Wt={delimiter:"/",prefixes:"/",sensitive:!0,strict:!0};function Gt(e,t){return e.length?e[0]==="/"?!0:!t||e.length<2?!1:(e[0]=="\\"||e[0]=="{")&&e[1]=="/":!1}function Xe(e,t){return e.startsWith(t)?e.substring(t.length,e.length):e}function Vt(e,t){return e.endsWith(t)?e.substr(0,e.length-t.length):e}function Qe(e){return!e||e.length<2?!1:e[0]==="["||(e[0]==="\\"||e[0]==="{")&&e[1]==="["}var Ze=["ftp","file","http","https","ws","wss"];function et(e){if(!e)return!0;for(let t of Ze)if(e.test(t))return!0;return!1}function zt(e,t){if(e=Xe(e,"#"),t||e==="")return e;let r=new URL("https://example.com");return r.hash=e,r.hash?r.hash.substring(1,r.hash.length):""}function Jt(e,t){if(e=Xe(e,"?"),t||e==="")return e;let r=new URL("https://example.com");return r.search=e,r.search?r.search.substring(1,r.search.length):""}function Yt(e,t){return t||e===""?e:Qe(e)?nt(e):rt(e)}function Kt(e,t){if(t||e==="")return e;let r=new URL("https://example.com");return r.password=e,r.password}function Xt(e,t){if(t||e==="")return e;let r=new URL("https://example.com");return r.username=e,r.username}function Qt(e,t,r){if(r||e==="")return e;if(t&&!Ze.includes(t))return new URL(`${t}:${e}`).pathname;let n=e[0]=="/";return e=new URL(n?e:"/-"+e,"https://example.com").pathname,n||(e=e.substring(2,e.length)),e}function Zt(e,t,r){return tt(t)===e&&(e=""),r||e===""?e:st(e)}function er(e,t){return e=Vt(e,":"),t||e===""?e:de(e)}function tt(e){switch(e){case"ws":case"http":return"80";case"wws":case"https":return"443";case"ftp":return"21";default:return""}}function de(e){if(e==="")return e;if(/^[-+.A-Za-z0-9]*$/.test(e))return e.toLowerCase();throw new TypeError(`Invalid protocol '${e}'.`)}function tr(e){if(e==="")return e;let t=new URL("https://example.com");return t.username=e,t.username}function rr(e){if(e==="")return e;let t=new URL("https://example.com");return t.password=e,t.password}function rt(e){if(e==="")return e;if(/[\t\n\r #%/:<>?@[\]^\\|]/g.test(e))throw new TypeError(`Invalid hostname '${e}'`);let t=new URL("https://example.com");return t.hostname=e,t.hostname}function nt(e){if(e==="")return e;if(/[^0-9a-fA-F[\]:]/g.test(e))throw new TypeError(`Invalid IPv6 hostname '${e}'`);return e.toLowerCase()}function st(e){if(e===""||/^[0-9]*$/.test(e)&&parseInt(e)<=65535)return e;throw new TypeError(`Invalid port '${e}'.`)}function nr(e){if(e==="")return e;let t=new URL("https://example.com");return t.pathname=e[0]!=="/"?"/-"+e:e,e[0]!=="/"?t.pathname.substring(2,t.pathname.length):t.pathname}function sr(e){return e===""?e:new URL(`data:${e}`).pathname}function or(e){if(e==="")return e;let t=new URL("https://example.com");return t.search=e,t.search.substring(1,t.search.length)}function ir(e){if(e==="")return e;let t=new URL("https://example.com");return t.hash=e,t.hash.substring(1,t.hash.length)}var ar=class{constructor(e){this.tokenList=[],this.internalResult={},this.tokenIndex=0,this.tokenIncrement=1,this.componentStart=0,this.state=0,this.groupDepth=0,this.hostnameIPv6BracketDepth=0,this.shouldTreatAsStandardURL=!1,this.input=e}get result(){return this.internalResult}parse(){for(this.tokenList=Je(this.input,!0);this.tokenIndex<this.tokenList.length;this.tokenIndex+=this.tokenIncrement){if(this.tokenIncrement=1,this.tokenList[this.tokenIndex].type==="END"){if(this.state===0){this.rewind(),this.isHashPrefix()?this.changeState(9,1):this.isSearchPrefix()?(this.changeState(8,1),this.internalResult.hash=""):(this.changeState(7,0),this.internalResult.search="",this.internalResult.hash="");continue}else if(this.state===2){this.rewindAndSetState(5);continue}this.changeState(10,0);break}if(this.groupDepth>0)if(this.isGroupClose())this.groupDepth-=1;else continue;if(this.isGroupOpen()){this.groupDepth+=1;continue}switch(this.state){case 0:this.isProtocolSuffix()&&(this.internalResult.username="",this.internalResult.password="",this.internalResult.hostname="",this.internalResult.port="",this.internalResult.pathname="",this.internalResult.search="",this.internalResult.hash="",this.rewindAndSetState(1));break;case 1:if(this.isProtocolSuffix()){this.computeShouldTreatAsStandardURL();let e=7,t=1;this.shouldTreatAsStandardURL&&(this.internalResult.pathname="/"),this.nextIsAuthoritySlashes()?(e=2,t=3):this.shouldTreatAsStandardURL&&(e=2),this.changeState(e,t)}break;case 2:this.isIdentityTerminator()?this.rewindAndSetState(3):(this.isPathnameStart()||this.isSearchPrefix()||this.isHashPrefix())&&this.rewindAndSetState(5);break;case 3:this.isPasswordPrefix()?this.changeState(4,1):this.isIdentityTerminator()&&this.changeState(5,1);break;case 4:this.isIdentityTerminator()&&this.changeState(5,1);break;case 5:this.isIPv6Open()?this.hostnameIPv6BracketDepth+=1:this.isIPv6Close()&&(this.hostnameIPv6BracketDepth-=1),this.isPortPrefix()&&!this.hostnameIPv6BracketDepth?this.changeState(6,1):this.isPathnameStart()?this.changeState(7,0):this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 6:this.isPathnameStart()?this.changeState(7,0):this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 7:this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 8:this.isHashPrefix()&&this.changeState(9,1);break;case 9:break;case 10:break}}}changeState(e,t){switch(this.state){case 0:break;case 1:this.internalResult.protocol=this.makeComponentString();break;case 2:break;case 3:this.internalResult.username=this.makeComponentString();break;case 4:this.internalResult.password=this.makeComponentString();break;case 5:this.internalResult.hostname=this.makeComponentString();break;case 6:this.internalResult.port=this.makeComponentString();break;case 7:this.internalResult.pathname=this.makeComponentString();break;case 8:this.internalResult.search=this.makeComponentString();break;case 9:this.internalResult.hash=this.makeComponentString();break;case 10:break}this.changeStateWithoutSettingComponent(e,t)}changeStateWithoutSettingComponent(e,t){this.state=e,this.componentStart=this.tokenIndex+t,this.tokenIndex+=t,this.tokenIncrement=0}rewind(){this.tokenIndex=this.componentStart,this.tokenIncrement=0}rewindAndSetState(e){this.rewind(),this.state=e}safeToken(e){return e<0&&(e=this.tokenList.length-e),e<this.tokenList.length?this.tokenList[e]:this.tokenList[this.tokenList.length-1]}isNonSpecialPatternChar(e,t){let r=this.safeToken(e);return r.value===t&&(r.type==="CHAR"||r.type==="ESCAPED_CHAR"||r.type==="INVALID_CHAR")}isProtocolSuffix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}nextIsAuthoritySlashes(){return this.isNonSpecialPatternChar(this.tokenIndex+1,"/")&&this.isNonSpecialPatternChar(this.tokenIndex+2,"/")}isIdentityTerminator(){return this.isNonSpecialPatternChar(this.tokenIndex,"@")}isPasswordPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}isPortPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}isPathnameStart(){return this.isNonSpecialPatternChar(this.tokenIndex,"/")}isSearchPrefix(){if(this.isNonSpecialPatternChar(this.tokenIndex,"?"))return!0;if(this.tokenList[this.tokenIndex].value!=="?")return!1;let e=this.safeToken(this.tokenIndex-1);return e.type!=="NAME"&&e.type!=="REGEX"&&e.type!=="CLOSE"&&e.type!=="ASTERISK"}isHashPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,"#")}isGroupOpen(){return this.tokenList[this.tokenIndex].type=="OPEN"}isGroupClose(){return this.tokenList[this.tokenIndex].type=="CLOSE"}isIPv6Open(){return this.isNonSpecialPatternChar(this.tokenIndex,"[")}isIPv6Close(){return this.isNonSpecialPatternChar(this.tokenIndex,"]")}makeComponentString(){let e=this.tokenList[this.tokenIndex],t=this.safeToken(this.componentStart).index;return this.input.substring(t,e.index)}computeShouldTreatAsStandardURL(){let e={};Object.assign(e,E),e.encodePart=de;let t=Mt(this.makeComponentString(),void 0,e);this.shouldTreatAsStandardURL=et(t)}},fe=["protocol","username","password","hostname","port","pathname","search","hash"],_="*";function ze(e,t){if(typeof e!="string")throw new TypeError("parameter 1 is not of type 'string'.");let r=new URL(e,t);return{protocol:r.protocol.substring(0,r.protocol.length-1),username:r.username,password:r.password,hostname:r.hostname,port:r.port,pathname:r.pathname,search:r.search!==""?r.search.substring(1,r.search.length):void 0,hash:r.hash!==""?r.hash.substring(1,r.hash.length):void 0}}function I(e,t){return t?W(e):e}function B(e,t,r){let n;if(typeof t.baseURL=="string")try{n=new URL(t.baseURL),e.protocol=I(n.protocol.substring(0,n.protocol.length-1),r),e.username=I(n.username,r),e.password=I(n.password,r),e.hostname=I(n.hostname,r),e.port=I(n.port,r),e.pathname=I(n.pathname,r),e.search=I(n.search.substring(1,n.search.length),r),e.hash=I(n.hash.substring(1,n.hash.length),r)}catch{throw new TypeError(`invalid baseURL '${t.baseURL}'.`)}if(typeof t.protocol=="string"&&(e.protocol=er(t.protocol,r)),typeof t.username=="string"&&(e.username=Xt(t.username,r)),typeof t.password=="string"&&(e.password=Kt(t.password,r)),typeof t.hostname=="string"&&(e.hostname=Yt(t.hostname,r)),typeof t.port=="string"&&(e.port=Zt(t.port,e.protocol,r)),typeof t.pathname=="string"){if(e.pathname=t.pathname,n&&!Gt(e.pathname,r)){let o=n.pathname.lastIndexOf("/");o>=0&&(e.pathname=I(n.pathname.substring(0,o+1),r)+e.pathname)}e.pathname=Qt(e.pathname,e.protocol,r)}return typeof t.search=="string"&&(e.search=Jt(t.search,r)),typeof t.hash=="string"&&(e.hash=zt(t.hash,r)),e}function W(e){return e.replace(/([+*?:{}()\\])/g,"\\$1")}function cr(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function hr(e,t){t.delimiter??(t.delimiter="/#?"),t.prefixes??(t.prefixes="./"),t.sensitive??(t.sensitive=!1),t.strict??(t.strict=!1),t.end??(t.end=!0),t.start??(t.start=!0),t.endsWith="";let r=".*",n=`[^${cr(t.delimiter)}]+?`,o=/[$_\u200C\u200D\p{ID_Continue}]/u,c="";for(let i=0;i<e.length;++i){let s=e[i];if(s.type===3){if(s.modifier===3){c+=W(s.value);continue}c+=`{${W(s.value)}}${$(s.modifier)}`;continue}let h=s.hasCustomName(),a=!!s.suffix.length||!!s.prefix.length&&(s.prefix.length!==1||!t.prefixes.includes(s.prefix)),u=i>0?e[i-1]:null,d=i<e.length-1?e[i+1]:null;if(!a&&h&&s.type===1&&s.modifier===3&&d&&!d.prefix.length&&!d.suffix.length)if(d.type===3){let x=d.value.length>0?d.value[0]:"";a=o.test(x)}else a=!d.hasCustomName();if(!a&&!s.prefix.length&&u&&u.type===3){let x=u.value[u.value.length-1];a=t.prefixes.includes(x)}a&&(c+="{"),c+=W(s.prefix),h&&(c+=`:${s.name}`),s.type===2?c+=`(${s.value})`:s.type===1?h||(c+=`(${n})`):s.type===0&&(!h&&(!u||u.type===3||u.modifier!==3||a||s.prefix!=="")?c+="*":c+=`(${r})`),s.type===1&&h&&s.suffix.length&&o.test(s.suffix[0])&&(c+="\\"),c+=W(s.suffix),a&&(c+="}"),s.modifier!==3&&(c+=$(s.modifier))}return c}var oe=class{constructor(e={},t,r){this.regexp={},this.names={},this.component_pattern={},this.parts={};try{let n;if(typeof t=="string"?n=t:r=t,typeof e=="string"){let s=new ar(e);if(s.parse(),e=s.result,n===void 0&&typeof e.protocol!="string")throw new TypeError("A base URL must be provided for a relative constructor string.");e.baseURL=n}else{if(!e||typeof e!="object")throw new TypeError("parameter 1 is not of type 'string' and cannot convert to dictionary.");if(n)throw new TypeError("parameter 1 is not of type 'string'.")}typeof r>"u"&&(r={ignoreCase:!1});let o={ignoreCase:r.ignoreCase===!0},c={pathname:_,protocol:_,username:_,password:_,hostname:_,port:_,search:_,hash:_};this.pattern=B(c,e,!0),tt(this.pattern.protocol)===this.pattern.port&&(this.pattern.port="");let i;for(i of fe){if(!(i in this.pattern))continue;let s={},h=this.pattern[i];switch(this.names[i]=[],i){case"protocol":Object.assign(s,E),s.encodePart=de;break;case"username":Object.assign(s,E),s.encodePart=tr;break;case"password":Object.assign(s,E),s.encodePart=rr;break;case"hostname":Object.assign(s,Bt),Qe(h)?s.encodePart=nt:s.encodePart=rt;break;case"port":Object.assign(s,E),s.encodePart=st;break;case"pathname":et(this.regexp.protocol)?(Object.assign(s,Wt,o),s.encodePart=nr):(Object.assign(s,E,o),s.encodePart=sr);break;case"search":Object.assign(s,E,o),s.encodePart=or;break;case"hash":Object.assign(s,E,o),s.encodePart=ir;break}try{this.parts[i]=Ye(h,s),this.regexp[i]=Ke(this.parts[i],this.names[i],s),this.component_pattern[i]=hr(this.parts[i],s)}catch{throw new TypeError(`invalid ${i} pattern '${this.pattern[i]}'.`)}}}catch(n){throw new TypeError(`Failed to construct 'URLPattern': ${n.message}`)}}test(e={},t){let r={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if(typeof e!="string"&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return!1;try{typeof e=="object"?r=B(r,e,!1):r=B(r,ze(e,t),!1)}catch{return!1}let n;for(n of fe)if(!this.regexp[n].exec(r[n]))return!1;return!0}exec(e={},t){let r={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if(typeof e!="string"&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return;try{typeof e=="object"?r=B(r,e,!1):r=B(r,ze(e,t),!1)}catch{return null}let n={};t?n.inputs=[e,t]:n.inputs=[e];let o;for(o of fe){let c=this.regexp[o].exec(r[o]);if(!c)return null;let i={};for(let[s,h]of this.names[o].entries())if(typeof h=="string"||typeof h=="number"){let a=c[s+1];i[h]=a}n[o]={input:r[o]??"",groups:i}}return n}static compareComponent(e,t,r){let n=(s,h)=>{for(let a of["type","modifier","prefix","value","suffix"]){if(s[a]<h[a])return-1;if(s[a]===h[a])continue;return 1}return 0},o=new G(3,"","","","",3),c=new G(0,"","","","",3),i=(s,h)=>{let a=0;for(;a<Math.min(s.length,h.length);++a){let u=n(s[a],h[a]);if(u)return u}return s.length===h.length?0:n(s[a]??o,h[a]??o)};return!t.component_pattern[e]&&!r.component_pattern[e]?0:t.component_pattern[e]&&!r.component_pattern[e]?i(t.parts[e],[c]):!t.component_pattern[e]&&r.component_pattern[e]?i([c],r.parts[e]):i(t.parts[e],r.parts[e])}get protocol(){return this.component_pattern.protocol}get username(){return this.component_pattern.username}get password(){return this.component_pattern.password}get hostname(){return this.component_pattern.hostname}get port(){return this.component_pattern.port}get pathname(){return this.component_pattern.pathname}get search(){return this.component_pattern.search}get hash(){return this.component_pattern.hash}};globalThis.URLPattern||(globalThis.URLPattern=oe);var ot=(e,t)=>{var i;if(e===void 0)return{};let r=t.endsWith("/")?t.slice(0,-1):t,o=(i=new oe({pathname:e,baseURL:r}).exec(r))==null?void 0:i.pathname.groups;return o?Object.entries(o).reduce((s,[h,a])=>a===void 0?s:{...s,[h]:a},{}):{}};var ie=e=>e.headers.get(Oe)||"";import{env as ur}from"process";var it=()=>({region:ur.AWS_REGION});import{env as me}from"process";var at=()=>({id:me.SITE_ID,name:me.SITE_NAME,url:me.URL});var lr=typeof Response<"u"&&typeof Response.json=="function"?e=>Response.json(e):e=>new Response(JSON.stringify(e),{headers:{"content-type":"application/json"}}),ct=(e,t)=>{let r={},n=e[j];try{r=ot(n,e.url)}catch{console.log(`Could not parse function route ${n}.`)}let o={enqueuedPromises:[]};return{context:{account:te(e),cookies:t.getPublicInterface(),deploy:re(e.headers),flags:Me(e),path:n,geo:We(e.headers.get(Le)),ip:Ge(e.headers.get(Pe)),json:lr,log:console.log,next:()=>{throw new Error("`context.next` is not implemented for serverless functions")},params:r,requestId:ie(e),rewrite:i=>{let s=fr(i,e.url);return pr(s)},server:it(),site:at(),url:new URL(e.url),waitUntil:function(s){if(arguments.length===0)throw new TypeError("waitUntil: At least 1 argument required, but only 0 passed");o.enqueuedPromises.push(Promise.resolve(s).catch(h=>console.error(h)))}},state:o}},fr=(e,t)=>{if(e instanceof URL)return e;if(e.startsWith("/")){let r=new URL(t);return r.pathname=e,r}return new URL(e)},pr=async e=>await fetch(e);import ut from"node:assert";function ht(e){function t(a,u=2){return a.padStart(u,"0")}let r=t(e.getUTCDate().toString()),n=t(e.getUTCHours().toString()),o=t(e.getUTCMinutes().toString()),c=t(e.getUTCSeconds().toString()),i=e.getUTCFullYear(),s=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],h=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];return`${s[e.getUTCDay()]}, ${r} ${h[e.getUTCMonth()]} ${i} ${n}:${o}:${c} GMT`}var dr=/^(?=[\u0020-\u007E]*$)[^()@<>,;:\\"[\]?={}\s]+$/;function mr(e){if(!e.name)return"";let t=[];if(gr(e.name),xr(e.name,e.value),t.push(`${e.name}=${e.value}`),e.name.startsWith("__Secure")&&(e.secure=!0),e.name.startsWith("__Host")&&(e.path="/",e.secure=!0,delete e.domain),e.secure&&t.push("Secure"),e.httpOnly&&t.push("HttpOnly"),typeof e.maxAge=="number"&&Number.isInteger(e.maxAge)&&(ut(e.maxAge>=0,"Max-Age must be an integer superior or equal to 0"),t.push(`Max-Age=${e.maxAge}`)),e.domain&&(wr(e.domain),t.push(`Domain=${e.domain}`)),e.sameSite&&t.push(`SameSite=${e.sameSite}`),e.path&&(yr(e.path),t.push(`Path=${e.path}`)),e.expires){let{expires:r}=e,n=ht(typeof r=="number"?new Date(r):r);t.push(`Expires=${n}`)}return e.unparsed&&t.push(e.unparsed.join("; ")),t.join("; ")}function gr(e){if(e&&!dr.test(e))throw new TypeError(`Invalid cookie name: "${e}".`)}function yr(e){if(e!=null)for(let t=0;t<e.length;t++){let r=e.charAt(t);if(r<" "||r>"~"||r==";")throw new Error(`${e}: Invalid cookie path char '${r}'`)}}function xr(e,t){if(!(t==null||e==null))for(let r=0;r<t.length;r++){let n=t.charAt(r);if(n<"!"||n=='"'||n==","||n==";"||n=="\\"||n=="\x7F")throw new Error(`RFC2616 cookie '${e}' cannot contain character '${n}'`);if(n>"\x80")throw new Error(`RFC2616 cookie '${e}' can only have US-ASCII chars as value${n.charCodeAt(0).toString(16)}`)}}function wr(e){if(e==null)return;let t=e.charAt(0),r=e.charAt(e.length-1);if(t=="-"||r=="."||r=="-")throw new Error(`Invalid first/last char in cookie domain: ${e}`)}function lt(e){let t=e.get("Cookie");if(t!=null){let r={},n=t.split(";");for(let o of n){let[c,...i]=o.split("=");ut(c!=null);let s=c.trim();r[s]=i.join("=")}return r}return{}}function ge(e,t){let r=mr(t);r&&e.append("Set-Cookie",r)}function ft(e,t,r){ge(e,Object.assign({name:t,value:"",expires:new Date(0)},r))}var ae=class{constructor(t){this.ops=[],this.request=t}apply(t){this.ops.forEach(r=>{switch(r.type){case"delete":ft(t,r.options.name,{domain:r.options.domain,path:r.options.path});break;case"set":ge(t,r.cookie);break;default:}})}delete(t){let r={path:"/"},n=typeof t=="string"?{name:t}:t;this.ops.push({options:{...r,...n},type:"delete"})}get(t){return lt(this.request.headers)[t]}getPublicInterface(){return{delete:this.delete.bind(this),get:this.get.bind(this),set:this.set.bind(this)}}set(t,r){let n;if(typeof t=="string"){if(typeof r!="string")throw new TypeError("You must provide the cookie value as a string to 'cookies.set'");n={name:t,value:r}}else n=t;this.ops.push({cookie:n,type:"set"})}};import{Buffer as pt}from"node:buffer";import ye from"node:process";var Rr="NETLIFY_PURGE_API_TOKEN",Sr="NETLIFY_BRANCH",Cr="NETLIFY_BLOBS_CONTEXT",xe=e=>typeof e=="string"&&e.length!==0,dt=(e,t,r,n)=>{var i,s;let{blobs:o}=t,c=(s=(i=r==null?void 0:r.clientContext)==null?void 0:i.custom)==null?void 0:s.purge_api_token;if(xe(o))try{let h=pt.from(o,"base64").toString("utf8"),a=JSON.parse(h),u=e.get($e),d=e.get(ee);ye.env[Cr]=pt.from(JSON.stringify({edgeURL:a.url,uncachedEdgeURL:a.url_uncached,token:a.token,siteID:u,deployID:d,primaryRegion:a.primary_region})).toString("base64")}catch(h){console.error("An internal error occurred while setting up Netlify Blobs:",h)}xe(c)&&(ye.env[Rr]=c),xe(n==null?void 0:n.branch)&&(ye.env[Sr]=n.branch)};var mt=({awsRequestID:e,req:t,branch:r,functionName:n,logToken:o})=>{let c=new URL(t.url),i=ie(t),s={aws_req_id:e,aid:te(t).id,branch:r??"",did:re(t.headers).id,fn:n??"",host:c.host,log_token:o,method:t.method,nf_req_id:i,ts:Date.now(),path:c.pathname};ue({fields:s,type:"bootstrap/request"})},we=({awsRequestID:e,result:t})=>{let r={aws_req_id:e};t instanceof Response?r.status=t.status:t instanceof Error?r.error_message=t.message:r.status=204,ue({fields:r,type:"bootstrap/response"})};var gt=!1,br=e=>(...t)=>{let[r,n]=t;if(typeof r=="string"&&r.startsWith("/"))try{let o=w.getStore(),c=o==null?void 0:o.context.url;if(!c)throw new Error("Could not find request in context");let i=new URL(r,c);return e(i,n)}catch{N("An error occurred in the patched Response.redirect")}return e(...t)},yt=()=>{gt||(gt=!0,"Response"in globalThis&&Response.redirect&&(Response.redirect=br(Response.redirect)))};import kr from"node:http";import vr from"node:https";var Ir=globalThis.fetch,xt=e=>{let t=w.getStore();if(t!=null&&t.cdnLoopHeader){if(e.headersSent){N("Headers already sent, cannot add CDN-Loop header");return}e.setHeader("CDN-Loop",t==null?void 0:t.cdnLoopHeader)}};globalThis.fetch=function(t,r){let n=new Request(t,r),o=w.getStore();return o!=null&&o.cdnLoopHeader&&n.headers.set("CDN-Loop",o.cdnLoopHeader),Ir(n)};for(let e of[kr,vr]){let t=e.request;e.get=function(...n){let o=t(...n);return xt(o),o.end(),o},e.request=function(...n){let o=t(...n);return xt(o),o}}var Tr=Ar(Pr),Nr=20,$r=e=>awslambda.streamifyResponse(async(t,r,n)=>{let o=Date.now(),c=n.awsRequestId,{body:i,flags:s,headers:h,httpMethod:a,invocationMetadata:u,isBase64Encoded:d,logToken:x,rawUrl:U,route:P}=t,S=new M(s||{}),L=Fe(h),O=je(i,d),z=S.get("serverless_functions_abort_signal")===!0?AbortSignal.timeout(n.getRemainingTimeInMillis()-Nr):void 0,l=new ne(U,{body:O,headers:L,method:a,signal:z}),p=!Z()&&S.get("serverless_functions_log_metadata")===!0;S.get("serverless_functions_response_redirect_relative")===!0&&yt(),p&&mt({awsRequestID:c,branch:u==null?void 0:u.branch,functionName:u==null?void 0:u.function_name,logToken:x,req:l}),dt(L,t,n,u),Be(l,S),P&&(l[j]=P),S.get("serverless_functions_wait_event_loop")===!0&&(n.callbackWaitsForEmptyEventLoop=!1);let m=new ae(l),{context:C,state:D}=ct(l,m),g={cacheAPI:Ee(t),cdnLoopHeader:L.get("cdn-loop"),context:C,operationCounter:new Q};try{let f;if(typeof e=="string"){let v=await import(e),b=wt(wt(v));f=await w.run(g,()=>b(l,C))}else f=await w.run(g,()=>e.default(l,C));p&&we({awsRequestID:c,result:f}),await Ur(f,r,m,S,o)}catch(f){throw p&&we({awsRequestID:c,result:f}),f}if(D.enqueuedPromises.length!==0)try{await Promise.allSettled(D.enqueuedPromises)}catch(f){console.error(f)}}),wt=e=>e&&typeof e=="object"&&"default"in e?e.default:e,Ur=async(e,t,r,n,o)=>{let c={version:Er.version},i=_r.from(JSON.stringify(c)).toString("base64");if(e instanceof Response){let s=new Headers(e.headers);r.apply(s);let{body:h,status:a}=e,u=n.evaluations;u.size!==0&&s.set(Ue,[...u].join(",")),s.set(le,i),s.set(De,o.toString());let d={multiValueHeaders:qe(s),statusCode:a},x=awslambda.HttpResponseStream.from(t,d);if((n.get("serverless_functions_fix_empty_body")===!0||h===null)&&x.write(""),h===null){x.end();return}let U=Lr.fromWeb(h);await Tr(U,x);return}if(e===void 0){let s=awslambda.HttpResponseStream.from(t,{statusCode:204,headers:{[le]:i}});s.write(""),s.end();return}throw new T("Function returned an unsupported value. Accepted types are 'Response' or 'undefined'")};import V from"process";var Or={delete:e=>{delete V.env[e]},get:e=>V.env[e],has:e=>!!V.env[e],set:(e,t)=>{V.env[e]=t},toObject:()=>Object.entries(V.env).reduce((e,[t,r])=>r===void 0?e:{...e,[t]:r},{})},Rt={get context(){let e=w.getStore();return(e==null?void 0:e.context)??null},env:Or};globalThis.Netlify=Rt;var Dr=()=>Rt;var Ws=()=>Fr(import.meta.url);export{$r as getLambdaHandler,Dr as getNetlifyGlobal,Ws as getPath};
