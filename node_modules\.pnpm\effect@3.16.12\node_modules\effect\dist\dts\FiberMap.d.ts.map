{"version": 3, "file": "FiberMap.d.ts", "sourceRoot": "", "sources": ["../../src/FiberMap.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAExD,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AACzC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAGvC,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAG/C,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,EAAE,KAAK,QAAQ,EAAiB,MAAM,eAAe,CAAA;AAE5D,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAsC,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAClE,SAAQ,QAAQ,EAAE,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAElF,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;CAQpD;AAED;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,GAAG,OAAO,KAAG,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAqC,CAAA;AAqClG;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAmBrG,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,WAAW,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EAC1E,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC7C,IACC,SAAS,KACV,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EACjF,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC7C,IACC,SAAS,KACV,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAaH;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAA;IACpC;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACvB,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,IAAI,CAAA;CA+CP,CAAA;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACnD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACvB,GAAG,EAAE,CAAC,EACN,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CAkBpB,CAAA;AAEJ;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACvF;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;CAgB0B,CAAA;AAE/G;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAA;IAC/G;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAA;CAgB9C,CAAA;AAE/D;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAA;IACvD;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO,CAAA;CAKpD,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACtE;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAInE,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACnE;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CA2B5D,CAAA;AAEL;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CASvE,CAAA;AAYJ;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EACN,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACvB,GAAG,EAAE,CAAC,EACN,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,KAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IACxD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACrC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACvB,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;CASvD,CAAA;AAuBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAC5B,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KACpB,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EACjC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACJ,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACrD,GACC,SAAS,KACV,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,CAAC,CA0BA,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,cAAc,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EAC9F,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,GAAG,EAAE,CAAC,EACN,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACrD,IACC,SAAS,KACV,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,CAAC,CAsBA,CAAA;AAEH;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CACmB,CAAA;AAE/F;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAChB,CAAA;AAE7D;;;;;GAKG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAK/E,CAAA"}