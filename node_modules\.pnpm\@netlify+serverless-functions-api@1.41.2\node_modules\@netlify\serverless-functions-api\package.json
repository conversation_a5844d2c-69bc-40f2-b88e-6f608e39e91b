{"name": "@netlify/serverless-functions-api", "type": "module", "version": "1.41.2", "files": ["dist/**/*.d.ts", "dist/**/*.js"], "main": "dist/index.js", "types": "dist/index.d.ts", "description": "The runtime API for Netlify Functions", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./instrumentation.js": "./dist/telemetry/instrumentation.js", "./package.json": "./package.json"}, "scripts": {"build": "run-s build:*", "build:pre": "rm -rf dist", "build:bundle": "node scripts/build.js", "build:types": "tsc", "dev:watch": "tsc -w", "prepare": "husky install node_modules/@netlify/eslint-config-node/.husky/", "prepublishOnly": "npm ci && npm test", "prepack": "npm run build", "test": "run-s build format test:dev", "format": "run-s format:check-fix:*", "format:ci": "run-s format:check:*", "format:check-fix:lint": "run-e format:check:lint format:fix:lint", "format:check:lint": "cross-env-shell eslint $npm_package_config_eslint", "format:fix:lint": "cross-env-shell eslint --fix $npm_package_config_eslint", "format:check-fix:prettier": "run-e format:check:prettier format:fix:prettier", "format:check:prettier": "cross-env-shell prettier --check $npm_package_config_prettier", "format:fix:prettier": "cross-env-shell prettier --write $npm_package_config_prettier", "test:dev": "run-s build test:dev:*", "test:dev:vitest": "vitest run", "test:dev:vitest:watch": "vitest watch", "test:ci": "run-s build test:ci:*", "test:ci:vitest": "vitest run --coverage"}, "config": {"eslint": "--ignore-pattern \"src/vendor/**/*\" --ignore-path .gitignore --cache --format=codeframe --max-warnings=0 \"{src,scripts,.github}/**/*.{js,ts,md,html}\"", "prettier": "--ignore-path .gitignore --loglevel=warn \"{src,scripts,.github}/**/*.{js,ts,md,yml,json,html}\" \"*.{js,ts,yml,json,html}\" \".*.{js,ts,yml,json,html}\" \"!**/package-lock.json\" \"!package-lock.json\" \"!src/vendor/**/*\""}, "ava": {"verbose": true, "files": ["tests/*", "tests/unit/**/*.js"]}, "keywords": [], "license": "MIT", "repository": "https://github.com/netlify/serverless-functions-api", "bugs": {"url": "https://github.com/netlify/serverless-functions-api/issues"}, "author": "Netlify Inc.", "directories": {"test": "test"}, "devDependencies": {"@commitlint/cli": "^19.0.0", "@commitlint/config-conventional": "^19.0.0", "@netlify/cache": "^1.11.0", "@netlify/eslint-config-node": "^7.0.1", "@netlify/node-cookies": "^0.1.0", "@opentelemetry/api": "1.9.0", "@opentelemetry/core": "^1.25.1", "@opentelemetry/otlp-transformer": "^0.56.0", "@opentelemetry/resources": "^1.25.1", "@opentelemetry/sdk-node": "^0.56.0", "@opentelemetry/sdk-trace-node": "^1.25.1", "@vitest/coverage-v8": "^3.0.0", "esbuild": "0.23.1", "husky": "^9.0.0", "lambda-local": "^2.2.0", "npm-run-all2": "^8.0.0", "typescript": "^5.0.0", "unixify": "^1.0.0", "urlpattern-polyfill": "8.0.2", "vitest": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}