var A=function(U){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(U)},z=function(U,J){var Y=Object.keys(U);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(U);J&&(I=I.filter(function(D){return Object.getOwnPropertyDescriptor(U,D).enumerable})),Y.push.apply(Y,I)}return Y},x=function(U){for(var J=1;J<arguments.length;J++){var Y=arguments[J]!=null?arguments[J]:{};J%2?z(Object(Y),!0).forEach(function(I){CC(U,I,Y[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(Y)):z(Object(Y)).forEach(function(I){Object.defineProperty(U,I,Object.getOwnPropertyDescriptor(Y,I))})}return U},CC=function(U,J,Y){if(J=HC(J),J in U)Object.defineProperty(U,J,{value:Y,enumerable:!0,configurable:!0,writable:!0});else U[J]=Y;return U},HC=function(U){var J=BC(U,"string");return A(J)=="symbol"?J:String(J)},BC=function(U,J){if(A(U)!="object"||!U)return U;var Y=U[Symbol.toPrimitive];if(Y!==void 0){var I=Y.call(U,J||"default");if(A(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,Y=function C(G,B){for(var H in B)J(G,H,{get:B[H],enumerable:!0,configurable:!0,set:function X(Z){return B[H]=function(){return Z}}})},I={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},D=function C(G,B,H,X){return I[G]};function E(C){return function(G,B){var H=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",X;if(H==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,Q=B!==null&&B!==void 0&&B.width?String(B.width):Z;X=C.formattingValues[Q]||C.formattingValues[Z]}else{var T=C.defaultWidth,K=B!==null&&B!==void 0&&B.width?String(B.width):C.defaultWidth;X=C.values[K]||C.values[T]}var q=C.argumentCallback?C.argumentCallback(G):G;return X[q]}}var S={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},$={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},R={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},M={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},L={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},V={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},f=function C(G,B){var H=Number(G),X=H%100;if(X>20||X<10)switch(X%10){case 1:return H+"st";case 2:return H+"nd";case 3:return H+"rd"}return H+"th"},j={ordinalNumber:f,era:E({values:S,defaultWidth:"wide"}),quarter:E({values:$,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:E({values:R,defaultWidth:"wide"}),day:E({values:M,defaultWidth:"wide"}),dayPeriod:E({values:L,defaultWidth:"wide",formattingValues:V,defaultFormattingWidth:"wide"})};function O(C){return function(G){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=B.width,X=H&&C.matchPatterns[H]||C.matchPatterns[C.defaultMatchWidth],Z=G.match(X);if(!Z)return null;var Q=Z[0],T=H&&C.parsePatterns[H]||C.parsePatterns[C.defaultParseWidth],K=Array.isArray(T)?F(T,function(W){return W.test(Q)}):v(T,function(W){return W.test(Q)}),q;q=C.valueCallback?C.valueCallback(K):K,q=B.valueCallback?B.valueCallback(q):q;var t=G.slice(Q.length);return{value:q,rest:t}}}var v=function C(G,B){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&B(G[H]))return H;return},F=function C(G,B){for(var H=0;H<G.length;H++)if(B(G[H]))return H;return};function _(C){return function(G){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(C.matchPattern);if(!H)return null;var X=H[0],Z=G.match(C.parsePattern);if(!Z)return null;var Q=C.valueCallback?C.valueCallback(Z[0]):Z[0];Q=B.valueCallback?B.valueCallback(Q):Q;var T=G.slice(X.length);return{value:Q,rest:T}}}var w=/^(\d+)(th|st|nd|rd)?/i,P=/\d+/i,k={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},h={any:[/^b/i,/^(a|c)/i]},b={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},c={any:[/1/i,/2/i,/3/i,/4/i]},m={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},y={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},p={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},g={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},d={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},u={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},l={ordinalNumber:_({matchPattern:w,parsePattern:P,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:k,defaultMatchWidth:"wide",parsePatterns:h,defaultParseWidth:"any"}),quarter:O({matchPatterns:b,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:m,defaultMatchWidth:"wide",parsePatterns:y,defaultParseWidth:"any"}),day:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:d,defaultMatchWidth:"any",parsePatterns:u,defaultParseWidth:"any"})},i={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"a second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"a minute",other:"{{count}} minutes"},aboutXHours:{one:"about an hour",other:"about {{count}} hours"},xHours:{one:"an hour",other:"{{count}} hours"},xDays:{one:"a day",other:"{{count}} days"},aboutXWeeks:{one:"about a week",other:"about {{count}} weeks"},xWeeks:{one:"a week",other:"{{count}} weeks"},aboutXMonths:{one:"about a month",other:"about {{count}} months"},xMonths:{one:"a month",other:"{{count}} months"},aboutXYears:{one:"about a year",other:"about {{count}} years"},xYears:{one:"a year",other:"{{count}} years"},overXYears:{one:"over a year",other:"over {{count}} years"},almostXYears:{one:"almost a year",other:"almost {{count}} years"}},n=function C(G,B,H){var X,Z=i[G];if(typeof Z==="string")X=Z;else if(B===1)X=Z.one;else X=Z.other.replace("{{count}}",B.toString());if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"in "+X;else return X+" ago";return X};function N(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=G.width?String(G.width):C.defaultWidth,H=C.formats[B]||C.formats[C.defaultWidth];return H}}var s={full:"EEEE, MMMM do, yyyy",long:"MMMM do, yyyy",medium:"MMM d, yyyy",short:"yyyy-MM-dd"},o={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},r={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},a={date:N({formats:s,defaultWidth:"full"}),time:N({formats:o,defaultWidth:"full"}),dateTime:N({formats:r,defaultWidth:"full"})},e={code:"en-CA",formatDistance:n,formatLong:a,formatRelative:D,localize:j,match:l,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{enCA:e})})})();

//# debugId=62821CC2999DEF7164756e2164756e21
