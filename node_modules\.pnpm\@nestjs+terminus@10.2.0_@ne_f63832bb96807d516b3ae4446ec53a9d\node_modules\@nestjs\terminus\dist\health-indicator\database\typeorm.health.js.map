{"version": 3, "file": "typeorm.health.js", "sourceRoot": "", "sources": ["../../../lib/health-indicator/database/typeorm.health.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAAmD;AACnD,uCAAyC;AAGzC,2BAAkE;AAClE,yCAIsB;AACtB,8EAAyE;AACzE,uCAIqB;AAcrB;;;;;;GAMG;AAEI,IAAM,sBAAsB,GAA5B,MAAM,sBAAuB,SAAQ,mBAAe;IACzD;;;;OAIG;IACH,YAAoB,SAAoB;QACtC,KAAK,EAAE,CAAC;QADU,cAAS,GAAT,SAAS,CAAW;QAEtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAA,qBAAa,EAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,EAAE,kBAAkB,EAAE;QAC1B,8DAA8D;QAC9D,OAAO,CAAC,2CAA2C,CAAyB,CAAC;QAE/E,IAAI;YACF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAY,EAAE;gBACxD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;SACJ;QAAC,OAAO,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEa,sBAAsB,CAAC,UAAe;;YAClD,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3C,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACjC,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG;oBAChC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG;oBACxB,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAElD,4DAA4D;gBAC5D,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAChC,GAAG,EACH,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,CAClD;qBACE,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,6BAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;qBACpE,IAAI,CAAC,CAAC,MAA2B,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;oBAEtD,gDAAgD;oBAChD,gEAAgE;qBAC/D,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC;qBACf,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC;KAAA;IAED;;;;;;OAMG;IACW,MAAM,CAAC,UAA8B,EAAE,OAAe;;YAClE,IAAI,KAAmB,CAAC;YACxB,QAAQ,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC/B,KAAK,SAAS;oBACZ,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;oBAChD,MAAM;gBACR,KAAK,QAAQ;oBACX,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;oBAC/C,MAAM;gBACR,KAAK,KAAK;oBACR,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;oBACpD,MAAM;gBACR;oBACE,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;oBACrC,MAAM;aACT;YACD,OAAO,MAAM,IAAA,sBAAc,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,CAAC;KAAA;IAED;;;;;;;;OAQG;IACG,SAAS,CACb,GAAW,EACX,UAAoC,EAAE;;YAEtC,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,MAAM,UAAU,GACd,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACpD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;YAExC,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,IAAI,gCAAuB,CAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;oBAC7B,OAAO,EAAE,sDAAsD;iBAChE,CAAC,CACH,CAAC;aACH;YAED,IAAI;gBACF,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACvC,SAAS,GAAG,IAAI,CAAC;aAClB;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,GAAG,YAAY,oBAAmB,EAAE;oBACtC,MAAM,IAAI,qBAAY,CACpB,OAAO,EACP,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;wBAC7B,OAAO,EAAE,cAAc,OAAO,aAAa;qBAC5C,CAAC,CACH,CAAC;iBACH;gBACD,IAAI,GAAG,YAAY,6BAAoB,EAAE;oBACvC,MAAM,IAAI,qCAAgB,CACxB,GAAG,CAAC,OAAO,EACX,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE;wBAC7B,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CACH,CAAC;iBACH;aACF;YAED,IAAI,SAAS,EAAE;gBACb,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;aACvC;iBAAM;gBACL,MAAM,IAAI,qCAAgB,CACxB,GAAG,GAAG,mBAAmB,EACzB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAC/B,CAAC;aACH;QACH,CAAC;KAAA;CACF,CAAA;AA9IY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,EAAC,EAAE,KAAK,EAAE,cAAK,CAAC,SAAS,EAAE,CAAC;qCAON,gBAAS;GAN7B,sBAAsB,CA8IlC"}