{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,kBAAkB,EAAE,MAAM,4BAA4B,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAE/C,4EAA4E;AAC5E,oCAAoC;AACpC,4EAA4E;AAE5E,cAAc,aAAa,CAAC;AAC5B,cAAc,wBAAwB,CAAC;AACvC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,2CAA2C,CAAC;AAC1D,cAAc,8BAA8B,CAAC;AAC7C,cAAc,+BAA+B,CAAC;AAC9C,cAAc,kCAAkC,CAAC;AACjD,cAAc,8BAA8B,CAAC;AAC7C,cAAc,wBAAwB,CAAC;AACvC,cAAc,sCAAsC,CAAC;AACrD,cAAc,sBAAsB,CAAC;AACrC,cAAc,4BAA4B,CAAC;AAoB3C;;GAEG;AACH,MAAM,UAAU,QAAQ,CACtB,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;IAExC,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,QAAQ,CACzC,kBAAkB,EAClB,yBAAmC,EACnC,qBAAqB,CACtB,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE,yBAA6C,CAAC,CAAC;IACjH,CAAC;AACH,CAAC;AAgBD;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAC9B,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;IAExC,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,gBAAgB,CACjD,kBAAkB,EAClB,yBAAmC,EACnC,qBAAqB,CACtB,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,gBAAgB,CACjD,kBAAkB,EAClB,yBAA6C,CAC9C,CAAC;IACJ,CAAC;AACH,CAAC;AAoBD;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAC1B,kBAAmC,EACnC,yBAAqD,EACrD,qBAAwC;IAExC,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,YAAY,CAC7C,kBAAkB,EAClB,yBAAmC,EACnC,qBAAqB,CACtB,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,kBAAkB,EAAE,yBAA6C,CAAC,CAAC;IACrH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,MAAwB;IACrD,kBAAkB,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC", "sourcesContent": ["import { ValidationError } from './validation/ValidationError';\nimport { ValidatorOptions } from './validation/ValidatorOptions';\nimport { ValidationSchema } from './validation-schema/ValidationSchema';\nimport { getMetadataStorage } from './metadata/MetadataStorage';\nimport { Validator } from './validation/Validator';\nimport { getFromContainer } from './container';\n\n// -------------------------------------------------------------------------\n// Export everything api users needs\n// -------------------------------------------------------------------------\n\nexport * from './container';\nexport * from './decorator/decorators';\nexport * from './decorator/ValidationOptions';\nexport * from './validation/ValidatorConstraintInterface';\nexport * from './validation/ValidationError';\nexport * from './validation/ValidatorOptions';\nexport * from './validation/ValidationArguments';\nexport * from './validation/ValidationTypes';\nexport * from './validation/Validator';\nexport * from './validation-schema/ValidationSchema';\nexport * from './register-decorator';\nexport * from './metadata/MetadataStorage';\n\n// -------------------------------------------------------------------------\n// Shortcut methods for api users\n// -------------------------------------------------------------------------\n\n/**\n * Validates given object.\n */\nexport function validate(object: object, validatorOptions?: ValidatorOptions): Promise<ValidationError[]>;\n\n/**\n * Validates given object by a given validation schema.\n */\nexport function validate(\n  schemaName: string,\n  object: object,\n  validatorOptions?: ValidatorOptions\n): Promise<ValidationError[]>;\n\n/**\n * Validates given object by object's decorators or given validation schema.\n */\nexport function validate(\n  schemaNameOrObject: object | string,\n  objectOrValidationOptions?: object | ValidatorOptions,\n  maybeValidatorOptions?: ValidatorOptions\n): Promise<ValidationError[]> {\n  if (typeof schemaNameOrObject === 'string') {\n    return getFromContainer(Validator).validate(\n      schemaNameOrObject,\n      objectOrValidationOptions as object,\n      maybeValidatorOptions\n    );\n  } else {\n    return getFromContainer(Validator).validate(schemaNameOrObject, objectOrValidationOptions as ValidatorOptions);\n  }\n}\n\n/**\n * Validates given object and reject on error.\n */\nexport function validateOrReject(object: object, validatorOptions?: ValidatorOptions): Promise<void>;\n\n/**\n * Validates given object by a given validation schema and reject on error.\n */\nexport function validateOrReject(\n  schemaName: string,\n  object: object,\n  validatorOptions?: ValidatorOptions\n): Promise<void>;\n\n/**\n * Validates given object by object's decorators or given validation schema and reject on error.\n */\nexport function validateOrReject(\n  schemaNameOrObject: object | string,\n  objectOrValidationOptions?: object | ValidatorOptions,\n  maybeValidatorOptions?: ValidatorOptions\n): Promise<void> {\n  if (typeof schemaNameOrObject === 'string') {\n    return getFromContainer(Validator).validateOrReject(\n      schemaNameOrObject,\n      objectOrValidationOptions as object,\n      maybeValidatorOptions\n    );\n  } else {\n    return getFromContainer(Validator).validateOrReject(\n      schemaNameOrObject,\n      objectOrValidationOptions as ValidatorOptions\n    );\n  }\n}\n\n/**\n * Performs sync validation of the given object.\n * Note that this method completely ignores async validations.\n * If you want to properly perform validation you need to call validate method instead.\n */\nexport function validateSync(object: object, validatorOptions?: ValidatorOptions): ValidationError[];\n\n/**\n * Validates given object by a given validation schema.\n * Note that this method completely ignores async validations.\n * If you want to properly perform validation you need to call validate method instead.\n */\nexport function validateSync(\n  schemaName: string,\n  object: object,\n  validatorOptions?: ValidatorOptions\n): ValidationError[];\n\n/**\n * Validates given object by object's decorators or given validation schema.\n * Note that this method completely ignores async validations.\n * If you want to properly perform validation you need to call validate method instead.\n */\nexport function validateSync(\n  schemaNameOrObject: object | string,\n  objectOrValidationOptions?: object | ValidatorOptions,\n  maybeValidatorOptions?: ValidatorOptions\n): ValidationError[] {\n  if (typeof schemaNameOrObject === 'string') {\n    return getFromContainer(Validator).validateSync(\n      schemaNameOrObject,\n      objectOrValidationOptions as object,\n      maybeValidatorOptions\n    );\n  } else {\n    return getFromContainer(Validator).validateSync(schemaNameOrObject, objectOrValidationOptions as ValidatorOptions);\n  }\n}\n\n/**\n * Registers a new validation schema.\n */\nexport function registerSchema(schema: ValidationSchema): void {\n  getMetadataStorage().addValidationSchema(schema);\n}\n"]}