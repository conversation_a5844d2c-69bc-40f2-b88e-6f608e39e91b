{"version": 3, "file": "MetricHook.d.ts", "sourceRoot": "", "sources": ["../../src/MetricHook.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAE5C,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,OAAO,MAAkC,CAAA;AAExE;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,gBAAgB,CAAA;AAEtD;;;GAGG;AACH,MAAM,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAE,SAAQ,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,QAAQ;IACxF,GAAG,IAAI,GAAG,CAAA;IACV,MAAM,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,CAAA;IACvB,MAAM,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,CAAA;CACxB;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC;;;OAGG;IACH,KAAY,IAAI,GAAG,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IAEnE;;;OAGG;IACH,KAAY,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAE1C;;;OAGG;IACH,KAAY,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAEpG;;;OAGG;IACH,KAAY,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEhG;;;OAGG;IACH,KAAY,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;IAE7E;;;OAGG;IACH,KAAY,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,CAAA;IAE7E;;;OAGG;IACH,KAAY,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IAE5F;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG;QACtC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;YAC3B,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YACrC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;SACpC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE;IACpC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;IAC1B,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,CAAA;IACpC,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,CAAA;CACrC,KAAK,UAAU,CAAC,EAAE,EAAE,GAAG,CAAiB,CAAA;AAEzC;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,CAC9F,CAAA;AAElB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,UAAU,CAAC,SAA8B,CAAA;AAE1G;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;OAGG;IACH,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IACnF;;;OAGG;IACH,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;CACnE,CAAA;AAElB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,UAAU,CAAC,SAA8B,CAAA;AAEzG;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,KAAK,UAAU,CAAC,OAA0B,CAAA;AAEjG;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;IACrF;;;OAGG;IACH,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,GAAG,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;CAC9D,CAAA;AAErB;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;IACrF;;;OAGG;IACH,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,IAAI,GAAG,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAA;CAC9D,CAAA"}