{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestLibrary", "title": "Nest Library Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the library.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the library?"}, "prefix": {"type": "string", "description": "The prefix of the library.", "x-prompt": "What prefix would you like to use for the library (default: @app or 'defaultLibraryPrefix' setting value)?"}, "language": {"type": "string", "description": "Nest library language."}, "path": {"type": "string", "format": "path", "description": "The path to create the library."}, "rootDir": {"type": "string", "format": "path", "description": "The libraries root directory."}}, "required": ["name", "prefix"]}