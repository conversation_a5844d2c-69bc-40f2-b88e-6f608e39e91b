"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const errors_1 = require("../../errors");
const health_check_1 = require("../../health-check");
const utils_1 = require("../../utils");
const health_indicator_1 = require("../health-indicator");
/**
 * The PrismaHealthIndicator contains health indicators
 * which are used for health checks related to Prisma
 *
 * @publicApi
 * @module TerminusModule
 */
let PrismaHealthIndicator = class PrismaHealthIndicator extends health_indicator_1.HealthIndicator {
    constructor() {
        super();
    }
    pingDb(timeout, prismaClientSQLOrMongo) {
        return __awaiter(this, void 0, void 0, function* () {
            // The prisma client generates two different typescript types for different databases
            // but inside they've the same methods
            // But they will fail when using a document method on sql database, that's why we do the try catch down below
            const prismaClient = prismaClientSQLOrMongo;
            try {
                yield (0, utils_1.promiseTimeout)(timeout, prismaClient.$runCommandRaw({ ping: 1 }));
            }
            catch (error) {
                if (error instanceof Error &&
                    error.toString().includes('Use the mongodb provider')) {
                    yield (0, utils_1.promiseTimeout)(timeout, prismaClient.$queryRawUnsafe('SELECT 1'));
                    return;
                }
                throw error;
            }
        });
    }
    /**
     * Checks if the Prisma responds in (default) 1000ms and
     * returns a result object corresponding to the result
     *
     * @param key The key which will be used for the result object
     * @param prismaClient PrismaClient
     * @param options The options for the ping
     */
    pingCheck(key, prismaClient, options = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            let isHealthy = false;
            const timeout = options.timeout || 1000;
            try {
                yield this.pingDb(timeout, prismaClient);
                isHealthy = true;
            }
            catch (error) {
                if (error instanceof utils_1.TimeoutError) {
                    throw new errors_1.TimeoutError(timeout, this.getStatus(key, isHealthy, {
                        message: `timeout of ${timeout}ms exceeded`,
                    }));
                }
            }
            if (isHealthy) {
                return this.getStatus(key, isHealthy);
            }
            else {
                throw new health_check_1.HealthCheckError(`${key} is not available`, this.getStatus(key, isHealthy));
            }
        });
    }
};
exports.PrismaHealthIndicator = PrismaHealthIndicator;
exports.PrismaHealthIndicator = PrismaHealthIndicator = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], PrismaHealthIndicator);
//# sourceMappingURL=prisma.health.js.map