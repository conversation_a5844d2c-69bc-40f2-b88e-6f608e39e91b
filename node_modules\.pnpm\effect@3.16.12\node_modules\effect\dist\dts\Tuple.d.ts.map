{"version": 3, "file": "Tuple.d.ts", "sourceRoot": "", "sources": ["../../src/Tuple.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAE/C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,UAAU,CAAA;AAC1C,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAEzC;;;GAGG;AACH,MAAM,WAAW,eAAgB,SAAQ,UAAU;IACjD,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;CAC9C;AAED;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,SAAS,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAG,CAAa,CAAA;AAEjF;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,QAAQ,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,CAAY,CAAA;AAEnE;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,SAAS,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,CAAY,CAAA;AAEpE;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,SAAS,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3G;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA;CAOxG,CAAA;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAA;QAC/B,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAA;KACjC,GACA,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IACxC;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACb,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EACvB,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAA;QAC/B,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAA;KACjC,GACA,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CAUZ,CAAA;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACrE;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;CACoC,CAAA;AAEvG;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IACtE;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;CACoC,CAAA;AAExG;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,CAAC,CAAuB,CAAA;AAE/E;;;;;;GAMG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,SAAS,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EACjF,GAAG,aAAa,EAAE,CAAC,KAChB,WAAW,CAAC,WAAW,CAC1B,QAAQ,CAAC;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;CAAE,CAAC,CACxE,CAAA;AAErB;;;;;;;;GAQG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,SAAS,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAC/D,GAAG,QAAQ,EAAE,CAAC,KACX,KAAK,CAAC,KAAK,CAAC;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK;CAAE,CAAe,CAAA;AAErG;;;;;GAKG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;IACtE;;;;;OAKG;IACH,CAAC,CAAC,SAAS,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;CAC8B,CAAA;AAElG;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,EAAE,EAAE;IACf;;;;;;;;;;;;;OAaG;IACH,CAAC,CAAC,SAAS,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACjF;;;;;;;;;;;;;OAaG;IACH,CAAC,CAAC,SAAS,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;CAC0B,CAAA;AAEzG,OAAO;AACL;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,SAAS;AACT;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,gBAAgB,EACjB,MAAM,gBAAgB,CAAA"}