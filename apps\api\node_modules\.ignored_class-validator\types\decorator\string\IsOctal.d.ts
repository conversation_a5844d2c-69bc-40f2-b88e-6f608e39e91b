import { ValidationOptions } from '../ValidationOptions';
export declare const IS_OCTAL = "isOctal";
/**
 * Check if the string is a valid octal number.
 * If given value is not a string, then it returns false.
 */
export declare function isOctal(value: unknown): boolean;
/**
 * Check if the string is a valid octal number.
 * If given value is not a string, then it returns false.
 */
export declare function IsOctal(validationOptions?: ValidationOptions): PropertyDecorator;
