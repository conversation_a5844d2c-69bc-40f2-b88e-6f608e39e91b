{"version": 3, "file": "Tracer.d.ts", "sourceRoot": "", "sources": ["../../src/Tracer.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AACtC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAG5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAE1C;;GAEG;AACH,eAAO,MAAM,YAAY,EAAE,OAAO,MAA8B,CAAA;AAEhE;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,OAAO,YAAY,CAAA;AAE9C;;GAEG;AACH,MAAM,WAAW,MAAM;IACrB,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,YAAY,CAAA;IACrC,IAAI,CACF,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAC9B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAC/B,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,EAC9B,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,QAAQ,GACb,IAAI,CAAA;IACP,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;CAC/D;AAED;;;GAGG;AACH,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,SAAS,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;CAClB,GAAG;IACF,IAAI,EAAE,OAAO,CAAA;IACb,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;CAClC,CAAA;AAED;;;GAGG;AACH,MAAM,MAAM,OAAO,GAAG,IAAI,GAAG,YAAY,CAAA;AAEzC;;;GAGG;AACH,MAAM,WAAW,UAAU;IACzB,QAAQ,CAAC,CAAC,EAAE,OAAO,MAAM,CAAA;CAC1B;AAED;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAoB,CAAA;AAE5E;;;GAGG;AACH,MAAM,WAAW,YAAY;IAC3B,QAAQ,CAAC,IAAI,EAAE,cAAc,CAAA;IAC7B,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAA;IACzB,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;CACzC;AAED;;;GAGG;AACH,MAAM,WAAW,WAAW;IAC1B,QAAQ,CAAC,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,CAAA;IACzD,QAAQ,CAAC,KAAK,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAA;IACpD,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACnC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;IACrD,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAA;IACpC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,SAAS,CAAA;CAC/E;AAED;;;GAGG;AACH,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAA;AAEjF;;;GAGG;AACH,MAAM,WAAW,IAAI;IACnB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACvC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACxC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAA;IAC3B,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACjD,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAA;IACvC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAA;IACzB,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;IACvB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,IAAI,CAAA;IAC7D,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,CAAA;IAC5C,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,IAAI,CAAA;IAClF,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;CAC/C;AAED;;;GAGG;AACH,MAAM,WAAW,QAAQ;IACvB,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAA;IACzB,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAA;CACvD;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAsB,CAAA;AAErE;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,YAAY,CAAC,KAAK,MAAsB,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,CACzB,OAAO,EAAE;IACP,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACtC,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;CACtD,KACE,YAAoC,CAAA;AAEzC;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAC9E,CAAA;AAE5B;;;GAGG;AACH,MAAM,WAAW,kBAAkB;IACjC,QAAQ,CAAC,CAAC,EAAE,OAAO,MAAM,CAAA;CAC1B;AAED;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,OAAO,CAAC,SAAS,CAAC,kBAAkB,EAAE,OAAO,CAA+B,CAAA"}