{"version": 3, "file": "MetricKey.d.ts", "sourceRoot": "", "sources": ["../../src/MetricKey.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,KAAK,gBAAgB,MAAM,uBAAuB,CAAA;AAC9D,OAAO,KAAK,KAAK,aAAa,MAAM,oBAAoB,CAAA;AACxD,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,OAAO,MAAiC,CAAA;AAEtE;;;GAGG;AACH,MAAM,MAAM,eAAe,GAAG,OAAO,eAAe,CAAA;AAEpD;;;;;;;;;GASG;AACH,MAAM,WAAW,SAAS,CAAC,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAC/E,SAAQ,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ;IAEvD,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAA;IACtB,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAC3C,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;CACtD;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC;;;OAGG;IACH,KAAY,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAA;IAEpC;;;OAGG;IACH,KAAY,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAEpG;;;OAGG;IACH,KAAY,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEhG;;;OAGG;IACH,KAAY,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;IAExE;;;OAGG;IACH,KAAY,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;IAExE;;;OAGG;IACH,KAAY,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IAEpE;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,IAAI;QAChC,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE;YAC1B,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;SAC7B,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAChF,CAAA;AAEtB;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CACE,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,QAAQ,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;QACnC,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAC3C,GACA,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAC5B;;;;;OAKG;IACH,CACE,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAA;QACrB,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAC3C,GACA,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;CACV,CAAA;AAEpB;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE,CACtB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EACJ;IACA,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IACzC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;CAChE,GACC,SAAS,KACV,SAAS,CAAC,SAA8B,CAAA;AAE7C;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CACE,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,QAAQ,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;KACpC,GACA,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IAC1B;;;;;OAKG;IACH,CACE,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAA;KACtB,GACA,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;CACV,CAAA;AAElB;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CACtB,IAAI,EAAE,MAAM,EACZ,UAAU,EAAE,gBAAgB,CAAC,gBAAgB,EAC7C,WAAW,CAAC,EAAE,MAAM,KACjB,SAAS,CAAC,SAA8B,CAAA;AAE7C;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE,CACpB,OAAO,EAAE;IACP,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAA;IACvC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,CAAC,CAAA;IACzC,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC1C,KACE,SAAS,CAAC,OAA0B,CAAA;AAEzC;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;OAKG;IACH,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IAC5H;;;;;OAKG;IACH,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;CACvG,CAAA;AAEnB;;;;;GAKG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;;;OAKG;IACH,CAAC,SAAS,EAAE,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IACnJ;;;;;OAKG;IACH,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;CACpH,CAAA"}