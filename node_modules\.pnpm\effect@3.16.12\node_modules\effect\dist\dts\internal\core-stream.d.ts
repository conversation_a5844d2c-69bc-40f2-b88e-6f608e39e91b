import type * as Channel from "../Channel.js";
import type { LazyArg } from "../Function.js";
export type Primitive = BracketOut | Bridge | ConcatAll | Emit | Ensuring | Fail | Fold | FromEffect | PipeTo | Provide | Read | Succeed | SucceedNow | Suspend;
export declare const sync: <OutDone>(evaluate: LazyArg<OutDone>) => Channel.Channel<never, unknown, never, unknown, OutDone, unknown>;
declare const void_: Channel.Channel<never>;
export { 
/** @internal */
void_ as void };
//# sourceMappingURL=core-stream.d.ts.map