var z=function(J){return z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},z(J)},W=function(J,H){var Y=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);H&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),Y.push.apply(Y,Z)}return Y},K=function(J){for(var H=1;H<arguments.length;H++){var Y=arguments[H]!=null?arguments[H]:{};H%2?W(Object(Y),!0).forEach(function(Z){GC(J,Z,Y[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(Y)):W(Object(Y)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(Y,Z))})}return J},GC=function(J,H,Y){if(H=UC(H),H in J)Object.defineProperty(J,H,{value:Y,enumerable:!0,configurable:!0,writable:!0});else J[H]=Y;return J},UC=function(J){var H=BC(J,"string");return z(H)=="symbol"?H:String(H)},BC=function(J,H){if(z(J)!="object"||!J)return J;var Y=J[Symbol.toPrimitive];if(Y!==void 0){var Z=Y.call(J,H||"default");if(z(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,Y=function C(B,U){for(var G in U)H(B,G,{get:U[G],enumerable:!0,configurable:!0,set:function T(X){return U[G]=function(){return X}}})},Z={lessThanXSeconds:{one:"menys d'un segon",eleven:"menys d'onze segons",other:"menys de {{count}} segons"},xSeconds:{one:"1 segon",other:"{{count}} segons"},halfAMinute:"mig minut",lessThanXMinutes:{one:"menys d'un minut",eleven:"menys d'onze minuts",other:"menys de {{count}} minuts"},xMinutes:{one:"1 minut",other:"{{count}} minuts"},aboutXHours:{one:"aproximadament una hora",other:"aproximadament {{count}} hores"},xHours:{one:"1 hora",other:"{{count}} hores"},xDays:{one:"1 dia",other:"{{count}} dies"},aboutXWeeks:{one:"aproximadament una setmana",other:"aproximadament {{count}} setmanes"},xWeeks:{one:"1 setmana",other:"{{count}} setmanes"},aboutXMonths:{one:"aproximadament un mes",other:"aproximadament {{count}} mesos"},xMonths:{one:"1 mes",other:"{{count}} mesos"},aboutXYears:{one:"aproximadament un any",other:"aproximadament {{count}} anys"},xYears:{one:"1 any",other:"{{count}} anys"},overXYears:{one:"m\xE9s d'un any",eleven:"m\xE9s d'onze anys",other:"m\xE9s de {{count}} anys"},almostXYears:{one:"gaireb\xE9 un any",other:"gaireb\xE9 {{count}} anys"}},x=function C(B,U,G){var T,X=Z[B];if(typeof X==="string")T=X;else if(U===1)T=X.one;else if(U===11&&X.eleven)T=X.eleven;else T=X.other.replace("{{count}}",String(U));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"en "+T;else return"fa "+T;return T};function N(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):C.defaultWidth,G=C.formats[U]||C.formats[C.defaultWidth];return G}}var $={full:"EEEE, d 'de' MMMM y",long:"d 'de' MMMM y",medium:"d MMM y",short:"dd/MM/y"},D={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} 'a les' {{time}}",long:"{{date}} 'a les' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:D,defaultWidth:"full"}),dateTime:N({formats:M,defaultWidth:"full"})},L={lastWeek:"'el' eeee 'passat a la' LT",yesterday:"'ahir a la' p",today:"'avui a la' p",tomorrow:"'dem\xE0 a la' p",nextWeek:"eeee 'a la' p",other:"P"},V={lastWeek:"'el' eeee 'passat a les' p",yesterday:"'ahir a les' p",today:"'avui a les' p",tomorrow:"'dem\xE0 a les' p",nextWeek:"eeee 'a les' p",other:"P"},j=function C(B,U,G,T){if(U.getHours()!==1)return V[B];return L[B]};function O(C){return function(B,U){var G=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",T;if(G==="formatting"&&C.formattingValues){var X=C.defaultFormattingWidth||C.defaultWidth,E=U!==null&&U!==void 0&&U.width?String(U.width):X;T=C.formattingValues[E]||C.formattingValues[X]}else{var I=C.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;T=C.values[A]||C.values[I]}var Q=C.argumentCallback?C.argumentCallback(B):B;return T[Q]}}var f={narrow:["aC","dC"],abbreviated:["a. de C.","d. de C."],wide:["abans de Crist","despr\xE9s de Crist"]},w={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1r trimestre","2n trimestre","3r trimestre","4t trimestre"]},v={narrow:["GN","FB","M\xC7","AB","MG","JN","JL","AG","ST","OC","NV","DS"],abbreviated:["gen.","febr.","mar\xE7","abr.","maig","juny","jul.","ag.","set.","oct.","nov.","des."],wide:["gener","febrer","mar\xE7","abril","maig","juny","juliol","agost","setembre","octubre","novembre","desembre"]},F={narrow:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],short:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],abbreviated:["dg.","dl.","dt.","dm.","dj.","dv.","ds."],wide:["diumenge","dilluns","dimarts","dimecres","dijous","divendres","dissabte"]},_={narrow:{am:"am",pm:"pm",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"mitjanit",noon:"migdia",morning:"mat\xED",afternoon:"tarda",evening:"vespre",night:"nit"}},P={narrow:{am:"am",pm:"pm",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"},abbreviated:{am:"AM",pm:"PM",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"},wide:{am:"ante meridiem",pm:"post meridiem",midnight:"de la mitjanit",noon:"del migdia",morning:"del mat\xED",afternoon:"de la tarda",evening:"del vespre",night:"de la nit"}},h=function C(B,U){var G=Number(B),T=G%100;if(T>20||T<10)switch(T%10){case 1:return G+"r";case 2:return G+"n";case 3:return G+"r";case 4:return G+"t"}return G+"\xE8"},k={ordinalNumber:h,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:w,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:O({values:v,defaultWidth:"wide"}),day:O({values:F,defaultWidth:"wide"}),dayPeriod:O({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function q(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=U.width,T=G&&C.matchPatterns[G]||C.matchPatterns[C.defaultMatchWidth],X=B.match(T);if(!X)return null;var E=X[0],I=G&&C.parsePatterns[G]||C.parsePatterns[C.defaultParseWidth],A=Array.isArray(I)?m(I,function(S){return S.test(E)}):b(I,function(S){return S.test(E)}),Q;Q=C.valueCallback?C.valueCallback(A):A,Q=U.valueCallback?U.valueCallback(Q):Q;var CC=B.slice(E.length);return{value:Q,rest:CC}}}var b=function C(B,U){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&U(B[G]))return G;return},m=function C(B,U){for(var G=0;G<B.length;G++)if(U(B[G]))return G;return};function c(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.match(C.matchPattern);if(!G)return null;var T=G[0],X=B.match(C.parsePattern);if(!X)return null;var E=C.valueCallback?C.valueCallback(X[0]):X[0];E=U.valueCallback?U.valueCallback(E):E;var I=B.slice(T.length);return{value:E,rest:I}}}var y=/^(\d+)(è|r|n|r|t)?/i,p=/\d+/i,g={narrow:/^(aC|dC)/i,abbreviated:/^(a. de C.|d. de C.)/i,wide:/^(abans de Crist|despr[eé]s de Crist)/i},d={narrow:[/^aC/i,/^dC/i],abbreviated:[/^(a. de C.)/i,/^(d. de C.)/i],wide:[/^(abans de Crist)/i,/^(despr[eé]s de Crist)/i]},u={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](è|r|n|r|t)? trimestre/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^(GN|FB|MÇ|AB|MG|JN|JL|AG|ST|OC|NV|DS)/i,abbreviated:/^(gen.|febr.|març|abr.|maig|juny|jul.|ag.|set.|oct.|nov.|des.)/i,wide:/^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i},n={narrow:[/^GN/i,/^FB/i,/^MÇ/i,/^AB/i,/^MG/i,/^JN/i,/^JL/i,/^AG/i,/^ST/i,/^OC/i,/^NV/i,/^DS/i],abbreviated:[/^gen./i,/^febr./i,/^març/i,/^abr./i,/^maig/i,/^juny/i,/^jul./i,/^ag./i,/^set./i,/^oct./i,/^nov./i,/^des./i],wide:[/^gener/i,/^febrer/i,/^març/i,/^abril/i,/^maig/i,/^juny/i,/^juliol/i,/^agost/i,/^setembre/i,/^octubre/i,/^novembre/i,/^desembre/i]},s={narrow:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,short:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,abbreviated:/^(dg\.|dl\.|dt\.|dm\.|dj\.|dv\.|ds\.)/i,wide:/^(diumenge|dilluns|dimarts|dimecres|dijous|divendres|dissabte)/i},o={narrow:[/^dg./i,/^dl./i,/^dt./i,/^dm./i,/^dj./i,/^dv./i,/^ds./i],abbreviated:[/^dg./i,/^dl./i,/^dt./i,/^dm./i,/^dj./i,/^dv./i,/^ds./i],wide:[/^diumenge/i,/^dilluns/i,/^dimarts/i,/^dimecres/i,/^dijous/i,/^divendres/i,/^disssabte/i]},r={narrow:/^(a|p|mn|md|(del|de la) (matí|tarda|vespre|nit))/i,abbreviated:/^([ap]\.?\s?m\.?|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i,wide:/^(ante meridiem|post meridiem|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^mitjanit/i,noon:/^migdia/i,morning:/matí/i,afternoon:/tarda/i,evening:/vespre/i,night:/nit/i}},e={ordinalNumber:c({matchPattern:y,parsePattern:p,valueCallback:function C(B){return parseInt(B,10)}}),era:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"wide"}),quarter:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"wide"}),day:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"wide"}),dayPeriod:q({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"})},t={code:"ca",formatDistance:x,formatLong:R,formatRelative:j,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{ca:t})})})();

//# debugId=EBB047E2DA13338564756e2164756e21
