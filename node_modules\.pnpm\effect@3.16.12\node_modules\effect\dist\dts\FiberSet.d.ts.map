{"version": 3, "file": "FiberSet.d.ts", "sourceRoot": "", "sources": ["../../src/FiberSet.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AACzC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAGvC,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAE/C,OAAO,EAAE,KAAK,QAAQ,EAAiB,MAAM,eAAe,CAAA;AAE5D,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAsC,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CACxD,SAAQ,QAAQ,EAAE,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7E,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;CAQpD;AAED;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,GAAG,OAAO,KAAG,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAqC,CAAA;AAqC3G;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAa/F,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,WAAW,GAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EAC/E,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,GAAG,SAAS,KACzC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,GAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EACtF,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,GAAG,SAAS,KACzC,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAaH;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAA;IACjC;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,IAAI,CAAA;CAgCP,CAAA;AAEF;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAChD;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CAkBvB,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAQjE,CAAA;AAYJ;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,KAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IACxD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACpB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GAAG,SAAS,GACZ,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;CAQvD,CAAA;AAuBD;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EACzB,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KACjB,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EACjC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACJ,OAAO,CAAC,cAAc,GAAG;IAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAAE,GACjF,SAAS,KACV,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,CAAC,CAoBA,CAAA;AAEH;;;;;;;GAOG;AACH,eAAO,MAAM,cAAc,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EACxF,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAAE,IACjF,SAAS,KACV,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,CAAC,CAqBA,CAAA;AAEH;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CACS,CAAA;AAE/E;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CACV,CAAA;AAE7D;;;;;GAKG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAKtE,CAAA"}