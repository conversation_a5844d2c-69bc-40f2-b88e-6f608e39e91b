{"version": 3, "file": "http.health.js", "sourceRoot": "", "sources": ["../../../lib/health-indicator/http/http.health.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,2CAA0E;AAC1E,uCAAyC;AACzC,+BAAsD;AAKtD,0BAAiE;AAEjE,8EAAyE;AACzE,+EAA4E;AAC5E,uCAA0D;AAM1D;;;;;;GAMG;AAII,IAAM,mBAAmB,2BAAzB,MAAM,mBAAoB,SAAQ,mBAAe;IAGtD,YACmB,SAAoB,EAEpB,MAAqB;QAEtC,KAAK,EAAE,CAAC;QAJS,cAAS,GAAT,SAAS,CAAW;QAEpB,WAAM,GAAN,MAAM,CAAe;QAGtC,IAAI,IAAI,CAAC,MAAM,YAAY,sBAAa,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAA,qBAAa,EAC9B,CAAC,eAAe,CAAC,EACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CACtB,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc;QACpB,IAAI;YACF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;gBACtD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;SACJ;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+IAA+I,CAChJ,CAAC;YACF,MAAM,IAAI,KAAK,CACb,+IAA+I,CAChJ,CAAC;SACH;IACH,CAAC;IAED;;;;;;OAMG;IACK,iBAAiB,CAAC,GAAW,EAAE,KAAuB;QAC5D,IAAI,IAAA,oBAAY,EAAC,KAAK,CAAC,EAAE;YACvB,MAAM,QAAQ,GAA2B;gBACvC,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC;YACF,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAClB,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC5C,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;aACjD;YACD,MAAM,IAAI,qCAAgB,CACxB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CACrC,CAAC;SACH;IACH,CAAC;IAED;;;;;;;;;;;OAWG;IACG,SAAS,CACb,GAAW,EACX,GAAW,EACX,KAG0D,EAAE;YAH5D,EACE,UAAU,OAEgD,EADvD,OAAO,cAFZ,cAGC,CADW;;YAGZ,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,+EAA+E;YAC/E,+EAA+E;YAC/E,kBAAkB;YAClB,iDAAiD;YACjD,MAAM,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAExD,IAAI;gBACF,MAAM,IAAA,oBAAa,EAAC,WAAW,CAAC,OAAO,iBAAG,GAAG,IAAK,OAAO,EAAG,CAAC,CAAC;gBAC9D,SAAS,GAAG,IAAI,CAAC;aAClB;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;aAClC;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;;KACvC;IAEK,aAAa,CACjB,GAAW,EACX,GAAiB,EACjB,QAAoE,EACpE,KAG0D,EAAE;YAH5D,EACE,UAAU,OAEgD,EADvD,OAAO,cAFZ,cAGC,CADW;;YAGZ,MAAM,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAExD,IAAI,QAAuB,CAAC;YAC5B,IAAI,UAAU,GAAsB,IAAI,CAAC;YAEzC,IAAI;gBACF,QAAQ,GAAG,MAAM,IAAA,oBAAa,EAC5B,WAAW,CAAC,OAAO,iBAAG,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAK,OAAO,EAAG,CACzD,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,oBAAY,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE;oBACzC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;oBAC1B,UAAU,GAAG,KAAK,CAAC;iBACpB;qBAAM;oBACL,MAAM,KAAK,CAAC;iBACb;aACF;YAED,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAE3C,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,UAAU,EAAE;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;iBAC/C;gBAED,MAAM,IAAI,qCAAgB,CACxB,GAAG,GAAG,mBAAmB,EACzB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAC3B,CAAC;aACH;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;KAClC;CACF,CAAA;AA9IY,kDAAmB;8BAAnB,mBAAmB;IAH/B,IAAA,mBAAU,EAAC;QACV,KAAK,EAAE,cAAK,CAAC,SAAS;KACvB,CAAC;IAMG,WAAA,IAAA,eAAM,EAAC,iCAAe,CAAC,CAAA;qCADI,gBAAS;QAEZ,sBAAa;GAN7B,mBAAmB,CA8I/B"}