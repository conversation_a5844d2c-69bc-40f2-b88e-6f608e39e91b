{"version": 3, "file": "health-check.service.js", "sourceRoot": "", "sources": ["../../lib/health-check/health-check.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAMwB;AAExB,gFAAoE;AACpE,mFAAsE;AAEtE,8DAA2D;AAG3D;;;GAGG;AAEI,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAC7B,YACmB,mBAAwC,EAExC,WAAwB,EAExB,MAAqB;QAJrB,wBAAmB,GAAnB,mBAAmB,CAAqB;QAExC,gBAAW,GAAX,WAAW,CAAa;QAExB,WAAM,GAAN,MAAM,CAAe;QAEtC,IAAI,IAAI,CAAC,MAAM,YAAY,sBAAa,EAAE;YACxC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;SACjD;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACG,KAAK,CACT,gBAA2C;;YAE3C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;gBAC1B,OAAO,MAAM,CAAC;aACf;YAED,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,EAAE;gBAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAC1C,0BAA0B,EAC1B,MAAM,CAAC,OAAO,CACf,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACxB;YAED,MAAM,IAAI,oCAA2B,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC;KAAA;CACF,CAAA;AA5CY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,eAAM,EAAC,oCAAY,CAAC,CAAA;IAEpB,WAAA,IAAA,eAAM,EAAC,iCAAe,CAAC,CAAA;qCAHc,mDAAmB;GAFhD,kBAAkB,CA4C9B"}