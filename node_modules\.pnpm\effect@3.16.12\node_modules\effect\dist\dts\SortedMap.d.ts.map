{"version": 3, "file": "SortedMap.d.ts", "sourceRoot": "", "sources": ["../../src/SortedMap.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAInC,OAAO,EAAU,KAAK,WAAW,EAA6B,MAAM,kBAAkB,CAAA;AACtF,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAI7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,QAAA,MAAM,MAAM,EAAE,OAAO,MAAuC,CAAA;AAE5D;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW;IACtG,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;QACjB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAC/B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;KAChC,CAAA;CAGF;AA2CD;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC1D;;;OAGG;IACH,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;CAC4B,CAAA;AAE5E;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,KAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAyC,CAAA;AAE3G;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC5F;;;;;OAKG;IACH,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAKzF,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,GACd,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,MAChB,OAAO,SAAS,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAS,OAAO,KAAG,SAAS,CAChF,CAAC,EACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAChC,CAAA;AAEjC;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,OAA2B,CAAA;AAEjF;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,OAAyB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC3D;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CAIP,CAAA;AAElD;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,KAAK,CAAC,CAAC,CAA4B,CAAA;AAE1F;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAA;IAClD;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO,CAAA;CAIE,CAAA;AAElD;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAyB,CAAA;AAEtG;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3E;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CASrE,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;IACpF;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;CAI3B,CAAA;AAEvD;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC1D;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAIM,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACpE;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAOhB,CAAA;AAElD;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAA6B,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,gBAAgB,CAAC,CAAC,CAAwB,CAAA;AAE7F;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,gBAAgB,CAAC,CAAC,CAA0B,CAAA;AAEjG;;;GAGG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAI5E,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAwB,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACxI;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;CAmBtH,CAAA"}