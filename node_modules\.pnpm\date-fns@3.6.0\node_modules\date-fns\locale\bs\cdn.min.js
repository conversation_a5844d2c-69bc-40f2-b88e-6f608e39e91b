var W=function(G){return W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},W(G)},D=function(G,C){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);C&&(Y=Y.filter(function(x){return Object.getOwnPropertyDescriptor(G,x).enumerable})),X.push.apply(X,Y)}return X},K=function(G){for(var C=1;C<arguments.length;C++){var X=arguments[C]!=null?arguments[C]:{};C%2?D(Object(X),!0).forEach(function(Y){U1(G,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):D(Object(X)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(X,Y))})}return G},U1=function(G,C,X){if(C=A1(C),C in G)Object.defineProperty(G,C,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[C]=X;return G},A1=function(G){var C=B1(G,"string");return W(C)=="symbol"?C:String(C)},B1=function(G,C){if(W(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(G,C||"default");if(W(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(G)};(function(G){var C=Object.defineProperty,X=function H(B,U){for(var A in U)C(B,A,{get:U[A],enumerable:!0,configurable:!0,set:function T(J){return U[A]=function(){return J}}})},Y={lessThanXSeconds:{one:{standalone:"manje od 1 sekunde",withPrepositionAgo:"manje od 1 sekunde",withPrepositionIn:"manje od 1 sekundu"},dual:"manje od {{count}} sekunde",other:"manje od {{count}} sekundi"},xSeconds:{one:{standalone:"1 sekunda",withPrepositionAgo:"1 sekunde",withPrepositionIn:"1 sekundu"},dual:"{{count}} sekunde",other:"{{count}} sekundi"},halfAMinute:"pola minute",lessThanXMinutes:{one:{standalone:"manje od 1 minute",withPrepositionAgo:"manje od 1 minute",withPrepositionIn:"manje od 1 minutu"},dual:"manje od {{count}} minute",other:"manje od {{count}} minuta"},xMinutes:{one:{standalone:"1 minuta",withPrepositionAgo:"1 minute",withPrepositionIn:"1 minutu"},dual:"{{count}} minute",other:"{{count}} minuta"},aboutXHours:{one:{standalone:"oko 1 sat",withPrepositionAgo:"oko 1 sat",withPrepositionIn:"oko 1 sat"},dual:"oko {{count}} sata",other:"oko {{count}} sati"},xHours:{one:{standalone:"1 sat",withPrepositionAgo:"1 sat",withPrepositionIn:"1 sat"},dual:"{{count}} sata",other:"{{count}} sati"},xDays:{one:{standalone:"1 dan",withPrepositionAgo:"1 dan",withPrepositionIn:"1 dan"},dual:"{{count}} dana",other:"{{count}} dana"},aboutXWeeks:{one:{standalone:"oko 1 sedmicu",withPrepositionAgo:"oko 1 sedmicu",withPrepositionIn:"oko 1 sedmicu"},dual:"oko {{count}} sedmice",other:"oko {{count}} sedmice"},xWeeks:{one:{standalone:"1 sedmicu",withPrepositionAgo:"1 sedmicu",withPrepositionIn:"1 sedmicu"},dual:"{{count}} sedmice",other:"{{count}} sedmice"},aboutXMonths:{one:{standalone:"oko 1 mjesec",withPrepositionAgo:"oko 1 mjesec",withPrepositionIn:"oko 1 mjesec"},dual:"oko {{count}} mjeseca",other:"oko {{count}} mjeseci"},xMonths:{one:{standalone:"1 mjesec",withPrepositionAgo:"1 mjesec",withPrepositionIn:"1 mjesec"},dual:"{{count}} mjeseca",other:"{{count}} mjeseci"},aboutXYears:{one:{standalone:"oko 1 godinu",withPrepositionAgo:"oko 1 godinu",withPrepositionIn:"oko 1 godinu"},dual:"oko {{count}} godine",other:"oko {{count}} godina"},xYears:{one:{standalone:"1 godina",withPrepositionAgo:"1 godine",withPrepositionIn:"1 godinu"},dual:"{{count}} godine",other:"{{count}} godina"},overXYears:{one:{standalone:"preko 1 godinu",withPrepositionAgo:"preko 1 godinu",withPrepositionIn:"preko 1 godinu"},dual:"preko {{count}} godine",other:"preko {{count}} godina"},almostXYears:{one:{standalone:"gotovo 1 godinu",withPrepositionAgo:"gotovo 1 godinu",withPrepositionIn:"gotovo 1 godinu"},dual:"gotovo {{count}} godine",other:"gotovo {{count}} godina"}},x=function H(B,U,A){var T,J=Y[B];if(typeof J==="string")T=J;else if(U===1)if(A!==null&&A!==void 0&&A.addSuffix)if(A.comparison&&A.comparison>0)T=J.one.withPrepositionIn;else T=J.one.withPrepositionAgo;else T=J.one.standalone;else if(U%10>1&&U%10<5&&String(U).substr(-2,1)!=="1")T=J.dual.replace("{{count}}",String(U));else T=J.other.replace("{{count}}",String(U));if(A!==null&&A!==void 0&&A.addSuffix)if(A.comparison&&A.comparison>0)return"za "+T;else return"prije "+T;return T};function z(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,A=H.formats[U]||H.formats[H.defaultWidth];return A}}var M={full:"EEEE, d. MMMM yyyy.",long:"d. MMMM yyyy.",medium:"d. MMM yy.",short:"dd. MM. yy."},S={full:"HH:mm:ss (zzzz)",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},$={full:"{{date}} 'u' {{time}}",long:"{{date}} 'u' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:z({formats:M,defaultWidth:"full"}),time:z({formats:S,defaultWidth:"full"}),dateTime:z({formats:$,defaultWidth:"full"})},L={lastWeek:function H(B){switch(B.getDay()){case 0:return"'pro\u0161le nedjelje u' p";case 3:return"'pro\u0161le srijede u' p";case 6:return"'pro\u0161le subote u' p";default:return"'pro\u0161li' EEEE 'u' p"}},yesterday:"'ju\u010De u' p",today:"'danas u' p",tomorrow:"'sutra u' p",nextWeek:function H(B){switch(B.getDay()){case 0:return"'sljede\u0107e nedjelje u' p";case 3:return"'sljede\u0107u srijedu u' p";case 6:return"'sljede\u0107u subotu u' p";default:return"'sljede\u0107i' EEEE 'u' p"}},other:"P"},j=function H(B,U,A,T){var J=L[B];if(typeof J==="function")return J(U);return J};function O(H){return function(B,U){var A=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",T;if(A==="formatting"&&H.formattingValues){var J=H.defaultFormattingWidth||H.defaultWidth,Z=U!==null&&U!==void 0&&U.width?String(U.width):J;T=H.formattingValues[Z]||H.formattingValues[J]}else{var I=H.defaultWidth,q=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;T=H.values[q]||H.values[I]}var E=H.argumentCallback?H.argumentCallback(B):B;return T[E]}}var V={narrow:["pr.n.e.","AD"],abbreviated:["pr. Hr.","po. Hr."],wide:["Prije Hrista","Poslije Hrista"]},f={narrow:["1.","2.","3.","4."],abbreviated:["1. kv.","2. kv.","3. kv.","4. kv."],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},v={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],wide:["januar","februar","mart","april","maj","juni","juli","avgust","septembar","oktobar","novembar","decembar"]},_={narrow:["1.","2.","3.","4.","5.","6.","7.","8.","9.","10.","11.","12."],abbreviated:["jan","feb","mar","apr","maj","jun","jul","avg","sep","okt","nov","dec"],wide:["januar","februar","mart","april","maj","juni","juli","avgust","septembar","oktobar","novembar","decembar"]},w={narrow:["N","P","U","S","\u010C","P","S"],short:["ned","pon","uto","sre","\u010Det","pet","sub"],abbreviated:["ned","pon","uto","sre","\u010Det","pet","sub"],wide:["nedjelja","ponedjeljak","utorak","srijeda","\u010Detvrtak","petak","subota"]},P={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"poslije podne",evening:"uve\u010De",night:"no\u0107u"}},F={narrow:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},abbreviated:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"popodne",evening:"uve\u010De",night:"no\u0107u"},wide:{am:"AM",pm:"PM",midnight:"pono\u0107",noon:"podne",morning:"ujutru",afternoon:"poslije podne",evening:"uve\u010De",night:"no\u0107u"}},b=function H(B,U){var A=Number(B);return String(A)+"."},k={ordinalNumber:b,era:O({values:V,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function H(B){return B-1}}),month:O({values:v,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"}),day:O({values:w,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function Q(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},A=U.width,T=A&&H.matchPatterns[A]||H.matchPatterns[H.defaultMatchWidth],J=B.match(T);if(!J)return null;var Z=J[0],I=A&&H.parsePatterns[A]||H.parsePatterns[H.defaultParseWidth],q=Array.isArray(I)?m(I,function(N){return N.test(Z)}):h(I,function(N){return N.test(Z)}),E;E=H.valueCallback?H.valueCallback(q):q,E=U.valueCallback?U.valueCallback(E):E;var H1=B.slice(Z.length);return{value:E,rest:H1}}}var h=function H(B,U){for(var A in B)if(Object.prototype.hasOwnProperty.call(B,A)&&U(B[A]))return A;return},m=function H(B,U){for(var A=0;A<B.length;A++)if(U(B[A]))return A;return};function y(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},A=B.match(H.matchPattern);if(!A)return null;var T=A[0],J=B.match(H.parsePattern);if(!J)return null;var Z=H.valueCallback?H.valueCallback(J[0]):J[0];Z=U.valueCallback?U.valueCallback(Z):Z;var I=B.slice(T.length);return{value:Z,rest:I}}}var c=/^(\d+)\./i,g=/\d+/i,d={narrow:/^(pr\.n\.e\.|AD)/i,abbreviated:/^(pr\.\s?Hr\.|po\.\s?Hr\.)/i,wide:/^(Prije Hrista|prije nove ere|Poslije Hrista|nova era)/i},p={any:[/^pr/i,/^(po|nova)/i]},u={narrow:/^[1234]/i,abbreviated:/^[1234]\.\s?kv\.?/i,wide:/^[1234]\. kvartal/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^(10|11|12|[123456789])\./i,abbreviated:/^(jan|feb|mar|apr|maj|jun|jul|avg|sep|okt|nov|dec)/i,wide:/^((januar|januara)|(februar|februara)|(mart|marta)|(april|aprila)|(maj|maja)|(juni|juna)|(juli|jula)|(avgust|avgusta)|(septembar|septembra)|(oktobar|oktobra)|(novembar|novembra)|(decembar|decembra))/i},n={narrow:[/^1/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^7/i,/^8/i,/^9/i,/^10/i,/^11/i,/^12/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^avg/i,/^s/i,/^o/i,/^n/i,/^d/i]},s={narrow:/^[npusčc]/i,short:/^(ned|pon|uto|sre|(čet|cet)|pet|sub)/i,abbreviated:/^(ned|pon|uto|sre|(čet|cet)|pet|sub)/i,wide:/^(nedjelja|ponedjeljak|utorak|srijeda|(četvrtak|cetvrtak)|petak|subota)/i},r={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},o={any:/^(am|pm|ponoc|ponoć|(po)?podne|uvece|uveče|noću|poslije podne|ujutru)/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^pono/i,noon:/^pod/i,morning:/jutro/i,afternoon:/(poslije\s|po)+podne/i,evening:/(uvece|uveče)/i,night:/(nocu|noću)/i}},a={ordinalNumber:y({matchPattern:c,parsePattern:g,valueCallback:function H(B){return parseInt(B,10)}}),era:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},t={code:"bs",formatDistance:x,formatLong:R,formatRelative:j,localize:k,match:a,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{bs:t})})})();

//# debugId=2B5DA1EE56BB9F7264756e2164756e21
