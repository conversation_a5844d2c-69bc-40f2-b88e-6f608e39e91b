import type { FullQueryResults, QueryRows } from '@neondatabase/serverless';
import type { BatchItem } from "../batch.js";
import { entityKind } from "../entity.js";
import type { Logger } from "../logger.js";
import type { PgDialect } from "../pg-core/dialect.js";
import { PgTransaction } from "../pg-core/index.js";
import type { SelectedFieldsOrdered } from "../pg-core/query-builders/select.types.js";
import type { PgTransactionConfig, PreparedQueryConfig, QueryResultHKT } from "../pg-core/session.js";
import { PgPreparedQuery as PgPreparedQuery, PgSession } from "../pg-core/session.js";
import type { RelationalSchemaConfig, TablesRelationalConfig } from "../relations.js";
import { type Query } from "../sql/sql.js";
export type NeonHttpClient = {
    <A extends boolean = false, F extends boolean = true>(strings: string, params?: any[], config?: {
        arrayMode?: A;
        fullResults?: F;
    }): Promise<F extends true ? FullQueryResults<A> : QueryRows<A>>;
    transaction<A extends boolean = false, F extends boolean = true>(queries: Promise<FullQueryResults<boolean> | QueryRows<boolean>>[], config?: {
        arrayMode?: A;
        fullResults?: F;
    }): Promise<F extends true ? FullQueryResults<A>[] : QueryRows<A>[]>;
};
export declare class NeonHttpPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {
    private client;
    private logger;
    private fields;
    private customResultMapper?;
    static readonly [entityKind]: string;
    constructor(client: NeonHttpClient, query: Query, logger: Logger, fields: SelectedFieldsOrdered | undefined, customResultMapper?: ((rows: unknown[][]) => T['execute']) | undefined);
    execute(placeholderValues?: Record<string, unknown> | undefined): Promise<T['execute']>;
    mapResult(result: unknown): unknown;
    all(placeholderValues?: Record<string, unknown> | undefined): Promise<T['all']>;
    values(placeholderValues?: Record<string, unknown> | undefined): Promise<T['values']>;
}
export interface NeonHttpSessionOptions {
    logger?: Logger;
}
export declare class NeonHttpSession<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig> extends PgSession<NeonHttpQueryResultHKT, TFullSchema, TSchema> {
    private client;
    private schema;
    private options;
    static readonly [entityKind]: string;
    private logger;
    constructor(client: NeonHttpClient, dialect: PgDialect, schema: RelationalSchemaConfig<TSchema> | undefined, options?: NeonHttpSessionOptions);
    prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(query: Query, fields: SelectedFieldsOrdered | undefined, name: string | undefined, customResultMapper?: (rows: unknown[][]) => T['execute']): PgPreparedQuery<T>;
    batch<U extends BatchItem<'pg'>, T extends Readonly<[U, ...U[]]>>(queries: T): Promise<unknown[]>;
    query(query: string, params: unknown[]): Promise<FullQueryResults<true>>;
    queryObjects(query: string, params: unknown[]): Promise<FullQueryResults<false>>;
    transaction<T>(_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>, _config?: PgTransactionConfig): Promise<T>;
}
export declare class NeonTransaction<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig> extends PgTransaction<NeonHttpQueryResultHKT, TFullSchema, TSchema> {
    static readonly [entityKind]: string;
    transaction<T>(_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T>;
}
export type NeonHttpQueryResult<T> = Omit<FullQueryResults<false>, 'rows'> & {
    rows: T[];
};
export interface NeonHttpQueryResultHKT extends QueryResultHKT {
    type: NeonHttpQueryResult<this['row']>;
}
