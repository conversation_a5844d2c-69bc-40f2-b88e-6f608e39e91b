{"version": 3, "sources": ["../../src/mysql2/driver.ts"], "sourcesContent": ["import type { Connection as CallbackConnection, Pool as CallbackPool } from 'mysql2';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type { Mode } from '~/mysql-core/session.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { DrizzleError } from '../index.ts';\nimport type { MySql2Client, MySql2PreparedQueryHKT, MySql2QueryResultHKT } from './session.ts';\nimport { MySql2Session } from './session.ts';\n\nexport interface MySqlDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class MySql2Driver {\n\tstatic readonly [entityKind]: string = 'MySql2Driver';\n\n\tconstructor(\n\t\tprivate client: MySql2Client,\n\t\tprivate dialect: MySqlDialect,\n\t\tprivate options: MySqlDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t\tmode: Mode,\n\t): MySql2Session<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new MySql2Session(this.client, this.dialect, schema, { logger: this.options.logger, mode });\n\t}\n}\n\nexport { MySqlDatabase } from '~/mysql-core/db.ts';\n\nexport type MySql2Database<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = MySqlDatabase<MySql2QueryResultHKT, MySql2PreparedQueryHKT, TSchema>;\n\nexport type MySql2DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> =\n\t& Omit<DrizzleConfig<TSchema>, 'schema'>\n\t& ({ schema: TSchema; mode: Mode } | { schema?: undefined; mode?: Mode });\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: MySql2Client | CallbackConnection | CallbackPool,\n\tconfig: MySql2DrizzleConfig<TSchema> = {},\n): MySql2Database<TSchema> {\n\tconst dialect = new MySqlDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\tif (isCallbackClient(client)) {\n\t\tclient = client.promise();\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tif (config.mode === undefined) {\n\t\t\tthrow new DrizzleError({\n\t\t\t\tmessage:\n\t\t\t\t\t'You need to specify \"mode\": \"planetscale\" or \"default\" when providing a schema. Read more: https://orm.drizzle.team/docs/rqb#modes',\n\t\t\t});\n\t\t}\n\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst mode = config.mode ?? 'default';\n\n\tconst driver = new MySql2Driver(client as MySql2Client, dialect, { logger });\n\tconst session = driver.createSession(schema, mode);\n\treturn new MySqlDatabase(dialect, session, schema, mode) as MySql2Database<TSchema>;\n}\n\ninterface CallbackClient {\n\tpromise(): MySql2Client;\n}\n\nfunction isCallbackClient(client: any): client is CallbackClient {\n\treturn typeof client.promise === 'function';\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAE7B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AAEP,SAAS,oBAAoB;AAE7B,SAAS,qBAAqB;AAMvB,MAAM,aAAa;AAAA,EAGzB,YACS,QACA,SACA,UAA8B,CAAC,GACtC;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,UAAU,IAAY;AAAA,EASvC,cACC,QACA,MACiE;AACjE,WAAO,IAAI,cAAc,KAAK,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,QAAQ,QAAQ,KAAK,CAAC;AAAA,EAClG;AACD;AAEA,SAAS,iBAAAA,sBAAqB;AAUvB,SAAS,QACf,QACA,SAAuC,CAAC,GACd;AAC1B,QAAM,UAAU,IAAI,aAAa;AACjC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AACA,MAAI,iBAAiB,MAAM,GAAG;AAC7B,aAAS,OAAO,QAAQ;AAAA,EACzB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,QAAI,OAAO,SAAS,QAAW;AAC9B,YAAM,IAAI,aAAa;AAAA,QACtB,SACC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,OAAO,OAAO,QAAQ;AAE5B,QAAM,SAAS,IAAI,aAAa,QAAwB,SAAS,EAAE,OAAO,CAAC;AAC3E,QAAM,UAAU,OAAO,cAAc,QAAQ,IAAI;AACjD,SAAO,IAAI,cAAc,SAAS,SAAS,QAAQ,IAAI;AACxD;AAMA,SAAS,iBAAiB,QAAuC;AAChE,SAAO,OAAO,OAAO,YAAY;AAClC;", "names": ["MySqlDatabase"]}