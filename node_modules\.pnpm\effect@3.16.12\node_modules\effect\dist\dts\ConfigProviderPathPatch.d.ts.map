{"version": 3, "file": "ConfigProviderPathPatch.d.ts", "sourceRoot": "", "sources": ["../../src/ConfigProviderPathPatch.ts"], "names": [], "mappings": "AAKA;;;;;;GAMG;AACH,MAAM,MAAM,SAAS,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG,QAAQ,CAAA;AAErE;;;GAGG;AACH,MAAM,WAAW,KAAK;IACpB,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;CACvB;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAA;IACzB,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAA;CAC3B;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAA;IACxB,CAAC,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAA;CAC1B;AAED;;;GAGG;AACH,MAAM,WAAW,MAAM;IACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;IACvB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;CACtB;AAED;;;GAGG;AACH,MAAM,WAAW,QAAQ;IACvB,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAA;IACzB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;CACtB;AAED;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,SAA0B,CAAA;AAE9C;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IACjD;;;OAGG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,SAAS,CAAA;CAC3B,CAAA;AAEpB;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IAC/D;;;OAGG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,SAAS,CAAA;CACzC,CAAA;AAEpB;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IAC9C;;;OAGG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,CAAA;CACzB,CAAA;AAEnB;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IAC9C;;;OAGG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,GAAG,SAAS,CAAA;CACvB,CAAA"}