{"name": "@nestjs-modules/ioredis", "version": "1.2.4", "description": "Nest - a ioredis module (@ioredis)", "author": "Nest Modules TM", "private": false, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.json", "format": "prettier **/**/*.ts --ignore-path ./.prettierignore --write", "test": "jest", "release": "standard-version", "patch": "npm run release -- --release-as patch", "minor": "npm run release -- --release-as minor", "major": "npm run release -- --release-as major", "deploy": "sh ./publish.sh"}, "keywords": ["nest", "<PERSON><PERSON><PERSON>", "nest-modules", "nestjs-modules", "redis", "i<PERSON>is"], "peerDependencies": {"@nestjs/common": ">=6.7.0", "@nestjs/core": ">=6.7.0", "ioredis": ">=5.0.0"}, "optionalDependencies": {"@nestjs/terminus": "10.2.0"}, "devDependencies": {"@nestjs/common": "10.3.0", "@nestjs/core": "10.3.0", "@nestjs/terminus": "10.2.0", "@nestjs/testing": "10.3.0", "@types/jest": "29.5.11", "@typescript-eslint/eslint-plugin": "6.15.0", "@typescript-eslint/parser": "6.15.0", "husky": "8.0.3", "ioredis": "5.3.2", "jest": "29.7.0", "lint-staged": "15.2.0", "prettier": "3.1.1", "reflect-metadata": "0.2.1", "rimraf": "5.0.5", "standard-version": "9.5.0", "ts-jest": "29.1.1", "ts-node": "10.9.2", "tslib": "2.6.2", "typescript": "5.3.3"}, "lint-staged": {"*.ts": ["prettier --write"]}, "husky": {"hooks": {"commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/nest-modules/ioredis"}}