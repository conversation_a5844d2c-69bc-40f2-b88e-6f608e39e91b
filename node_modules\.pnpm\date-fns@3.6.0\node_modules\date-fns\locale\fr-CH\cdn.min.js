var T=function(Z){return T=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(Y){return typeof Y}:function(Y){return Y&&typeof Symbol=="function"&&Y.constructor===Symbol&&Y!==Symbol.prototype?"symbol":typeof Y},T(Z)},D=function(Z,Y){var U=Object.keys(Z);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(Z);Y&&(H=H.filter(function($){return Object.getOwnPropertyDescriptor(Z,$).enumerable})),U.push.apply(U,H)}return U},O=function(Z){for(var Y=1;Y<arguments.length;Y++){var U=arguments[Y]!=null?arguments[Y]:{};Y%2?D(Object(U),!0).forEach(function(H){JJ(Z,H,U[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(Z,Object.getOwnPropertyDescriptors(U)):D(Object(U)).forEach(function(H){Object.defineProperty(Z,H,Object.getOwnPropertyDescriptor(U,H))})}return Z},JJ=function(Z,Y,U){if(Y=BJ(Y),Y in Z)Object.defineProperty(Z,Y,{value:U,enumerable:!0,configurable:!0,writable:!0});else Z[Y]=U;return Z},BJ=function(Z){var Y=CJ(Z,"string");return T(Y)=="symbol"?Y:String(Y)},CJ=function(Z,Y){if(T(Z)!="object"||!Z)return Z;var U=Z[Symbol.toPrimitive];if(U!==void 0){var H=U.call(Z,Y||"default");if(T(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(Y==="string"?String:Number)(Z)};(function(Z){var Y=Object.defineProperty,U=function B(X,J){for(var C in J)Y(X,C,{get:J[C],enumerable:!0,configurable:!0,set:function G(I){return J[C]=function(){return I}}})},H={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},$=function B(X,J,C){var G,I=H[X];if(typeof I==="string")G=I;else if(J===1)G=I.one;else G=I.other.replace("{{count}}",String(J));if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"dans "+G;else return"il y a "+G;return G};function E(B){return function(X,J){var C=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",G;if(C==="formatting"&&B.formattingValues){var I=B.defaultFormattingWidth||B.defaultWidth,Q=J!==null&&J!==void 0&&J.width?String(J.width):I;G=B.formattingValues[Q]||B.formattingValues[I]}else{var q=B.defaultWidth,W=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;G=B.values[W]||B.values[q]}var A=B.argumentCallback?B.argumentCallback(X):X;return G[A]}}var V={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},N={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},M={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},R={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},S={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},L=function B(X,J){var C=Number(X),G=J===null||J===void 0?void 0:J.unit;if(C===0)return"0";var I=["year","week","hour","minute","second"],Q;if(C===1)Q=G&&I.includes(G)?"\xE8re":"er";else Q="\xE8me";return C+Q},j=["MMM","MMMM"],w={preprocessor:function B(X,J){if(X.getDate()===1)return J;var C=J.some(function(G){return G.isToken&&j.includes(G.value)});if(!C)return J;return J.map(function(G){return G.isToken&&G.value==="do"?{isToken:!0,value:"d"}:G})},ordinalNumber:L,era:E({values:V,defaultWidth:"wide"}),quarter:E({values:N,defaultWidth:"wide",argumentCallback:function B(X){return X-1}}),month:E({values:M,defaultWidth:"wide"}),day:E({values:R,defaultWidth:"wide"}),dayPeriod:E({values:S,defaultWidth:"wide"})};function K(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=J.width,G=C&&B.matchPatterns[C]||B.matchPatterns[B.defaultMatchWidth],I=X.match(G);if(!I)return null;var Q=I[0],q=C&&B.parsePatterns[C]||B.parsePatterns[B.defaultParseWidth],W=Array.isArray(q)?_(q,function(z){return z.test(Q)}):P(q,function(z){return z.test(Q)}),A;A=B.valueCallback?B.valueCallback(W):W,A=J.valueCallback?J.valueCallback(A):A;var t=X.slice(Q.length);return{value:A,rest:t}}}var P=function B(X,J){for(var C in X)if(Object.prototype.hasOwnProperty.call(X,C)&&J(X[C]))return C;return},_=function B(X,J){for(var C=0;C<X.length;C++)if(J(X[C]))return C;return};function f(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=X.match(B.matchPattern);if(!C)return null;var G=C[0],I=X.match(B.parsePattern);if(!I)return null;var Q=B.valueCallback?B.valueCallback(I[0]):I[0];Q=J.valueCallback?J.valueCallback(Q):Q;var q=X.slice(G.length);return{value:Q,rest:q}}}var v=/^(\d+)(ième|ère|ème|er|e)?/i,F=/\d+/i,k={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},b={any:[/^av/i,/^ap/i]},h={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},c={any:[/1/i,/2/i,/3/i,/4/i]},m={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},y={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},g={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},u={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},d={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},l={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},p={ordinalNumber:f({matchPattern:v,parsePattern:F,valueCallback:function B(X){return parseInt(X)}}),era:K({matchPatterns:k,defaultMatchWidth:"wide",parsePatterns:b,defaultParseWidth:"any"}),quarter:K({matchPatterns:h,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any",valueCallback:function B(X){return X+1}}),month:K({matchPatterns:m,defaultMatchWidth:"wide",parsePatterns:y,defaultParseWidth:"any"}),day:K({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),dayPeriod:K({matchPatterns:d,defaultMatchWidth:"any",parsePatterns:l,defaultParseWidth:"any"})};function x(B){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=X.width?String(X.width):B.defaultWidth,C=B.formats[J]||B.formats[B.defaultWidth];return C}}var i={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},n={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},s={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},o={date:x({formats:i,defaultWidth:"full"}),time:x({formats:n,defaultWidth:"full"}),dateTime:x({formats:s,defaultWidth:"full"})},e={lastWeek:"eeee 'la semaine derni\xE8re \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'la semaine prochaine \xE0' p",other:"P"},r=function B(X,J,C,G){return e[X]},a={code:"fr-CH",formatDistance:$,formatLong:o,formatRelative:r,localize:w,match:p,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=O(O({},window.dateFns),{},{locale:O(O({},(Z=window.dateFns)===null||Z===void 0?void 0:Z.locale),{},{frCH:a})})})();

//# debugId=0642E1C8CB72C34864756e2164756e21
