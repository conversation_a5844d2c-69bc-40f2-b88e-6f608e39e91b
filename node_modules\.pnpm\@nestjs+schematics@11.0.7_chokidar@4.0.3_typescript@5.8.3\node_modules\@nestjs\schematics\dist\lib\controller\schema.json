{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestController", "title": "Nest Controller Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the controller.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the controller?"}, "path": {"type": "string", "format": "path", "description": "The path to create the controller."}, "language": {"type": "string", "description": "Nest controller language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest controller source root directory."}, "skipImport": {"type": "boolean", "description": "Flag to skip the module import.", "default": false}, "module": {"type": "string", "description": "Allows specification of the declaring module."}, "flat": {"type": "boolean", "default": false, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}