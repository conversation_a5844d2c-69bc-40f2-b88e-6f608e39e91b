{"version": 3, "file": "FiberRefs.d.ts", "sourceRoot": "", "sources": ["../../src/FiberRefs.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,GAAG,MAAM,YAAY,CAAA;AACtC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAE5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,OAAO,MAA8B,CAAA;AAEhE;;;GAGG;AACH,MAAM,MAAM,YAAY,GAAG,OAAO,YAAY,CAAA;AAE9C;;;;;;;;GAQG;AACH,MAAM,WAAW,SAAU,SAAQ,QAAQ;IACzC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE,YAAY,CAAA;IACrC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;CACxG;AAED,QAAA,MAAM,OAAO,EAAE;IACb,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IACnE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAA;CAC7C,CAAA;AAEpB,OAAO;AACL;;;;;GAKG;AACH,OAAO,IAAI,MAAM,EAClB,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAsB,CAAA;AAEzG;;;;;;;GAOG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;;OAOG;IACH,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IACzD;;;;;;;OAOG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAA;CACpC,CAAA;AAEnB;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC1E;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;CACxD,CAAA;AAEhB;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC,CAAA;IAC3D;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;CAChC,CAAA;AAEzB;;;;;;;GAOG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;;OAOG;IACH,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IAC1E;;;;;;;OAOG;IACH,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,SAAS,CAAA;CACrD,CAAA;AAEnB;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAmB,CAAA;AAE/E;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;OAKG;IACH,CAAC,CAAC,EACA,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAA;QAChC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QACvC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;KAClB,GACA,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IACjC;;;;;OAKG;IACH,CAAC,CAAC,EACA,IAAI,EAAE,SAAS,EACf,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAA;QAChC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QACvC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;KAClB,GACA,SAAS,CAAA;CACO,CAAA;AAErB;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;OAKG;IACH,CACE,OAAO,EAAE;QACP,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,OAAO,EAAE,SAAS;YACzB,SAAS;gBACP,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACtB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;aACpF;YACD,GAAG,KAAK,CACN,SAAS;gBACP,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACtB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;aACpF,CACF;SACF,CAAA;KACF,GACA,CAAC,IAAI,EAAE,SAAS,KAAK,SAAS,CAAA;IACjC;;;;;OAKG;IACH,CACE,IAAI,EAAE,SAAS,EACf,OAAO,EAAE;QACP,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,OAAO,EAAE,SAAS;YACzB,SAAS;gBACP,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACtB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;aACpF;YACD,GAAG,KAAK,CACN,SAAS;gBACP,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACtB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;aACpF,CACF;SACF,CAAA;KACF,GACA,SAAS,CAAA;CACW,CAAA;AAEzB;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CACvB,cAAc,EAAE,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,qBAAqB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,KACnG,SAA+B,CAAA;AAEpC;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,MAAM,SAA0B,CAAA"}