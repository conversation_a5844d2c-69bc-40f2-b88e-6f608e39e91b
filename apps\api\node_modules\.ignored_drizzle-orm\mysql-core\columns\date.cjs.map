{"version": 3, "sources": ["../../../src/mysql-core/columns/date.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { Equal } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlDateBuilderInitial<TName extends string> = MySqlDateBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'MySqlDate';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDateBuilder<T extends ColumnBuilderBaseConfig<'date', 'MySqlDate'>> extends MySqlColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'MySqlDateBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'MySqlDate');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDate<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDate<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlDate<T extends ColumnBaseConfig<'date', 'MySqlDate'>> extends MySqlColumn<T> {\n\tstatic readonly [entityKind]: string = 'MySqlDate';\n\n\tconstructor(\n\t\ttable: AnyMySqlTable<{ name: T['tableName'] }>,\n\t\tconfig: MySqlDateBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `date`;\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value);\n\t}\n}\n\nexport type MySqlDateStringBuilderInitial<TName extends string> = MySqlDateStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlDateString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDateStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlDateString'>>\n\textends MySqlColumnBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'MySqlDateStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'MySqlDateString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDateString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDateString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDateString<T extends ColumnBaseConfig<'string', 'MySqlDateString'>> extends MySqlColumn<T> {\n\tstatic readonly [entityKind]: string = 'MySqlDateString';\n\n\tconstructor(\n\t\ttable: AnyMySqlTable<{ name: T['tableName'] }>,\n\t\tconfig: MySqlDateStringBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `date`;\n\t}\n}\n\nexport interface MySqlDateConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n}\n\nexport function date<TName extends string, TMode extends MySqlDateConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: MySqlDateConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? MySqlDateStringBuilderInitial<TName> : MySqlDateBuilderInitial<TName>;\nexport function date(name: string, config: MySqlDateConfig = {}) {\n\tif (config.mode === 'string') {\n\t\treturn new MySqlDateStringBuilder(name);\n\t}\n\treturn new MySqlDateBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAgD;AAWzC,MAAM,yBAAiF,iCAAsB;AAAA,EACnH,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,WAAW;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAmE,0BAAe;AAAA,EAC9F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,IAAI,KAAK,KAAK;AAAA,EACtB;AACD;AAWO,MAAM,+BACJ,iCACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,iBAAiB;AAAA,EACxC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAAiF,0BAAe;AAAA,EAC5G,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAUO,SAAS,KAAK,MAAc,SAA0B,CAAC,GAAG;AAChE,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,uBAAuB,IAAI;AAAA,EACvC;AACA,SAAO,IAAI,iBAAiB,IAAI;AACjC;", "names": []}