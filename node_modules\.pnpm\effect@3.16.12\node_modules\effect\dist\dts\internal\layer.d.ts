import type * as Layer from "../Layer.js";
export declare const annotateLogs: {
    (key: string, value: unknown): <A, E, R>(self: Layer.Layer<A, E, R>) => Layer.Layer<A, E, R>;
    (values: Record<string, unknown>): <A, E, R>(self: Layer.Layer<A, E, R>) => Layer.Layer<A, E, R>;
} & {
    <A, E, R>(self: Layer.Layer<A, E, R>, key: string, value: unknown): Layer.Layer<A, E, R>;
    <A, E, R>(self: Layer.Layer<A, E, R>, values: Record<string, unknown>): Layer.Layer<A, E, R>;
};
export declare const annotateSpans: {
    (key: string, value: unknown): <A, E, R>(self: Layer.Layer<A, E, R>) => Layer.Layer<A, E, R>;
    (values: Record<string, unknown>): <A, E, R>(self: Layer.Layer<A, E, R>) => Layer.Layer<A, E, R>;
} & {
    <A, E, R>(self: Layer.Layer<A, E, R>, key: string, value: unknown): Layer.Layer<A, E, R>;
    <A, E, R>(self: Layer.Layer<A, E, R>, values: Record<string, unknown>): Layer.Layer<A, E, R>;
};
//# sourceMappingURL=layer.d.ts.map