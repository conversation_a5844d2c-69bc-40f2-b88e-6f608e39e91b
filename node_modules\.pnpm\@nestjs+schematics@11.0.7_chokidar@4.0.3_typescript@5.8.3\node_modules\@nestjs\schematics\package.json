{"name": "@nestjs/schematics", "version": "11.0.7", "description": "Nest - modern, fast, powerful node.js web framework (@schematics)", "main": "dist/index.js", "files": ["dist/**/*"], "publishConfig": {"access": "public"}, "scripts": {"postbuild": "npm run copy:collection && npm run copy:lib", "build": "rm -rf dist && tsc --project tsconfig.lib.json", "clean": "gulp clean:src", "copy:collection": "cpx src/collection.json dist && cpx 'src/lib/**/schema.json' dist/lib", "copy:lib": "cpx 'src/lib/**/{files,workspace}/**/*.*' dist/lib && cpx 'src/lib/**/{files,workspace}/**/.!(gitignore)' dist/lib", "lint": "eslint '{src,test}/**/*.ts' --fix", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "test": "npm run clean && NODE_ENV=test jest -w 1 --no-cache --config jest.json", "test:dev": "NODE_ENV=test npm run -s test -- --watchAll", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/schematics.git"}, "contributors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "license": "MIT", "bugs": {"url": "https://github.com/nestjs/schematics/issues"}, "homepage": "https://github.com/nestjs/schematics#readme", "dependencies": {"@angular-devkit/core": "19.2.15", "@angular-devkit/schematics": "19.2.15", "comment-json": "4.2.5", "jsonc-parser": "3.3.1", "pluralize": "8.0.0"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-angular": "19.8.1", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.32.0", "@types/jest": "30.0.0", "@types/node": "22.17.0", "@typescript-eslint/eslint-plugin": "8.38.0", "@typescript-eslint/parser": "8.38.0", "cpx2": "8.0.0", "eslint": "9.32.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "2.32.0", "eslint-plugin-prettier": "^5.2.1", "globals": "16.3.0", "gulp": "5.0.1", "gulp-clean": "0.4.0", "husky": "9.1.7", "jest": "30.0.5", "nyc": "17.1.0", "prettier": "3.6.2", "release-it": "19.0.4", "ts-jest": "29.4.0", "ts-node": "10.9.2", "typescript": "5.8.3", "typescript-eslint": "^8.15.0"}, "peerDependencies": {"typescript": ">=4.8.2"}, "schematics": "./dist/collection.json", "nyc": {"include": ["src"], "exclude": ["node_modules", "test"], "extension": [".ts"], "require": ["ts-node/register"], "reporter": ["text-summary", "html"], "sourceMap": true, "instrument": true}, "lint-staged": {"**/*.{ts,json}": []}}