{"version": 3, "sources": ["../../src/mysql-proxy/session.ts"], "sourcesContent": ["import type { FieldPacket, ResultSetHeader } from 'mysql2/promise';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport { MySqlTransaction } from '~/mysql-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/mysql-core/query-builders/select.types.ts';\nimport type {\n\tMySqlTransactionConfig,\n\tPreparedQueryConfig,\n\tPreparedQueryHKT,\n\tPreparedQueryKind,\n\tQueryResultHKT,\n} from '~/mysql-core/session.ts';\nimport { MySqlSession, PreparedQuery as PreparedQueryBase } from '~/mysql-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\nimport type { RemoteCallback } from './driver.ts';\n\nexport type MySqlRawQueryResult = [ResultSetHeader, FieldPacket[]];\n\nexport interface MySqlRemoteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class MySqlRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlSession<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'MySqlRemoteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: MySqlDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: MySqlRemoteSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PreparedQueryKind<MySqlRemotePreparedQueryHKT, T> {\n\t\treturn new PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t) as PreparedQueryKind<MySqlRemotePreparedQueryHKT, T>;\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\t\treturn this.client(querySql.sql, querySql.params, 'all').then(({ rows }) => rows) as Promise<T[]>;\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: MySqlProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t_config?: MySqlTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the MySql Proxy driver');\n\t}\n}\n\nexport class MySqlProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlTransaction<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'MySqlProxyTransaction';\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: MySqlProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the MySql Proxy driver');\n\t}\n}\n\nexport class PreparedQuery<T extends PreparedQueryConfig> extends PreparedQueryBase<T> {\n\tstatic readonly [entityKind]: string = 'MySqlProxyPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper();\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tconst { fields, client, queryString, logger, joinsNotNullableMap, customResultMapper } = this;\n\n\t\tlogger.logQuery(queryString, params);\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst { rows } = await client(queryString, params, 'execute');\n\n\t\t\treturn rows;\n\t\t}\n\n\t\tconst { rows } = await client(queryString, params, 'all');\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\toverride iterator(\n\t\t_placeholderValues: Record<string, unknown> = {},\n\t): AsyncGenerator<T['iterator']> {\n\t\tthrow new Error('Streaming is not supported by the MySql Proxy driver');\n\t}\n}\n\nexport interface MySqlRemoteQueryResultHKT extends QueryResultHKT {\n\ttype: MySqlRawQueryResult;\n}\n\nexport interface MySqlRemotePreparedQueryHKT extends PreparedQueryHKT {\n\ttype: PreparedQuery<Assume<this['config'], PreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAE3B,oBAA2B;AAE3B,wBAAiC;AASjC,qBAAiE;AAEjE,iBAAiC;AAEjC,mBAA0C;AASnC,MAAM,2BAGH,4BAA2F;AAAA,EAKpG,YACS,QACR,SACQ,QACR,SACC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,oBACoD;AACpD,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAClD,WAAO,KAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI;AAAA,EACjF;AAAA,EAEA,MAAe,YACd,cACA,SACa;AACb,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AACD;AAEO,MAAM,8BAGH,mCAA+F;AAAA,EACxG,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAe,YACd,cACa;AACb,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AACD;AAEO,MAAM,sBAAqD,eAAAA,cAAqB;AAAA,EAGtF,YACS,QACA,aACA,QACA,QACA,QACA,oBACP;AACD,UAAM;AAPE;AACA;AACA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAXA,QAAiB,wBAAU,IAAY;AAAA,EAavC,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,QAAQ,iBAAiB;AAE9D,UAAM,EAAE,QAAQ,QAAQ,aAAa,QAAQ,qBAAqB,mBAAmB,IAAI;AAEzF,WAAO,SAAS,aAAa,MAAM;AAEnC,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,EAAE,MAAAC,MAAK,IAAI,MAAM,OAAO,aAAa,QAAQ,SAAS;AAE5D,aAAOA;AAAA,IACR;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,OAAO,aAAa,QAAQ,KAAK;AAExD,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAES,SACR,qBAA8C,CAAC,GACf;AAChC,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACvE;AACD;", "names": ["PreparedQueryBase", "rows"]}