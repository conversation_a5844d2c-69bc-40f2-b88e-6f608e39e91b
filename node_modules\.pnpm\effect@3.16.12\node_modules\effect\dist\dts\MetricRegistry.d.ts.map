{"version": 3, "file": "MetricRegistry.d.ts", "sourceRoot": "", "sources": ["../../src/MetricRegistry.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAClD,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,KAAK,aAAa,MAAM,oBAAoB,CAAA;AACxD,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAElD;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;GAGG;AACH,MAAM,WAAW,cAAc;IAC7B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;IACrD,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IAChD,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EACpD,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,GAC7B,UAAU,CAAC,UAAU,CACtB,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EACzD,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,CAC3D,CAAA;IACD,UAAU,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,EACpC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAClC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACnC,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,SAAS,CAAA;IACjF,QAAQ,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACxG,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAC,SAAS,CAAA;IACjF,UAAU,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,OAAO,CAAA;CAC5E;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,cAA8B,CAAA"}