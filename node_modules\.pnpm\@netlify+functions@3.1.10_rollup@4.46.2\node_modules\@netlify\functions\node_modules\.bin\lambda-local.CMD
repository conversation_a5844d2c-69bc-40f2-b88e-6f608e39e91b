@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules\lambda-local\build\node_modules;F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules\lambda-local\node_modules;F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules;F:\www\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules\lambda-local\build\node_modules;F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules\lambda-local\node_modules;F:\www\node_modules\.pnpm\lambda-local@2.2.0\node_modules;F:\www\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\lambda-local@2.2.0\node_modules\lambda-local\build\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\lambda-local@2.2.0\node_modules\lambda-local\build\cli.js" %*
)
