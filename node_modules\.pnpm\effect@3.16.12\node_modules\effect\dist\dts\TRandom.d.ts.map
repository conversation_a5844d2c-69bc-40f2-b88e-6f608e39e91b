{"version": 3, "file": "TRandom.d.ts", "sourceRoot": "", "sources": ["../../src/TRandom.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAE5C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AAIpC;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,OAAO,MAA+B,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,OAAO,aAAa,CAAA;AAEhD;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,aAAa,CAAA;IACvC;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAC9B;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IACtC;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACjC;;;OAGG;IACH,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACpD;;;OAGG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IACzD;;OAEG;IACH,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACrD;AAUD;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAgB,CAAA;AAE9D;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAiB,CAAA;AAEvD;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAiB,CAAA;AAElE;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAwB,CAAA;AAEjF;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAoB,CAAA;AAExE;;;;;;GAMG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAA2B,CAAA;AAErH;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAsB,CAAA;AAE1G;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAoB,CAAA"}