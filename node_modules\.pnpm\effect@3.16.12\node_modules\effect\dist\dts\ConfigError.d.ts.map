{"version": 3, "file": "ConfigError.d.ts", "sourceRoot": "", "sources": ["../../src/ConfigError.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAGxC;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,OAAO,MAAmC,CAAA;AAE1E;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,OAAO,iBAAiB,CAAA;AAExD;;;;;GAKG;AACH,MAAM,MAAM,WAAW,GACnB,GAAG,GACH,EAAE,GACF,WAAW,GACX,WAAW,GACX,iBAAiB,GACjB,WAAW,CAAA;AAEf;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC;;;OAGG;IACH,UAAiB,KAAK;QACpB,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAA;QAC5B,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE,iBAAiB,CAAA;KAChD;IAED;;;OAGG;IACH,KAAY,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CACrD;AAED;;;GAGG;AACH,MAAM,WAAW,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IAChD,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;IACzC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;IACxC,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,CAAA;IACpE,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,CAAA;IACpE,qBAAqB,CACnB,OAAO,EAAE,CAAC,EACV,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EACnB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,GAC1B,CAAC,CAAA;IACJ,eAAe,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,CAAA;CACrE;AAED;;;GAGG;AACH,MAAM,WAAW,GAAI,SAAQ,WAAW,CAAC,KAAK;IAC5C,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAA;IACnB,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAA;IAC1B,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAA;IAC3B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,EAAG,SAAQ,WAAW,CAAC,KAAK;IAC3C,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAA;IAClB,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAA;IAC1B,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAA;IAC3B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;IACpD,QAAQ,CAAC,GAAG,EAAE,aAAa,CAAA;IAC3B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAC5B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;IACpD,QAAQ,CAAC,GAAG,EAAE,aAAa,CAAA;IAC3B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAC5B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,iBAAkB,SAAQ,WAAW,CAAC,KAAK;IAC1D,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAA;IACjC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAC5B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;CACrC;AAED;;;GAGG;AACH,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;IACpD,QAAQ,CAAC,GAAG,EAAE,aAAa,CAAA;IAC3B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;IAC5B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;CACzB;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;CAC3B;AAED;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,KAAK,WAA0B,CAAA;AAEtF;;;GAGG;AACH,eAAO,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,KAAK,WAAyB,CAAA;AAEpF;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,WACjE,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,WACjE,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,CAC9B,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EACnB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAC3B,OAAO,CAAC,EAAE,OAAO,KACd,WAAwC,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,WACjE,CAAA;AAEtB;;;;;GAKG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAoC,CAAA;AAErF;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,GAAoB,CAAA;AAEvE;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,EAAkB,CAAA;AAEpE;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,WAAoC,CAAA;AAE/F;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,WAAoC,CAAA;AAE/F;;;;;GAKG;AACH,eAAO,MAAM,iBAAiB,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,OAAoC,CAAA;AAE3F;;;;;;GAMG;AACH,eAAO,MAAM,mBAAmB,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,iBAAgD,CAAA;AAEjH;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,IAAI,WAAoC,CAAA;AAE/F;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,KAAK,WAAW,CAAA;IAC3D;;;OAGG;IACH,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,CAAA;CACpC,CAAA;AAErB;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE;IAC9B;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC,CAAA;IAC/E;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;CAC/C,CAAA"}