{"version": 3, "file": "Console.d.ts", "sourceRoot": "", "sources": ["../../src/Console.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAGzC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AAEvC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAA8B,CAAA;AAE1D;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACrE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACnC,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACxC,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAChD,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAC3C,MAAM,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACjD,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAChD,KAAK,CAAC,OAAO,CAAC,EAAE;QACd,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACnC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACzC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAChB,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/B,IAAI,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/C,GAAG,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAC9C,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACzE,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAClC,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IACrC,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAClE,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAChD,IAAI,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/C,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAA;CAC/B;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa;IAC5B,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IAC7D,KAAK,IAAI,IAAI,CAAA;IACb,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC3B,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAChC,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACxC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI,CAAA;IACnC,MAAM,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACzC,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACxC,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACxC,cAAc,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACjD,QAAQ,IAAI,IAAI,CAAA;IAChB,IAAI,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACvC,GAAG,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACtC,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;IACjE,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC1B,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC7B,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IAC1D,KAAK,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IACxC,IAAI,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;CACxC;AAED;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAA6B,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACtF;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAC5D,CAAA;AAExB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,CAAuB,CAAA;AAEpG;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAwB,CAAA;AAEvH;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAmB,CAAA;AAExG;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,MAAM,CAAC,IAAI,CAAkB,CAAA;AAEjD;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC,IAAI,CAAkB,CAAA;AAErE;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC,IAAI,CAAuB,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAkB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,IAAI,CAAgB,CAAA;AAE3E;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAmB,CAAA;AAEpF;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAkB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAClB,OAAO,CAAC,EAAE;IAAE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAAC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAAE,GAAG,SAAS,KAClF,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAkB,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAiB,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAgB,CAAA;AAE9E;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,WAAW,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,CAAkB,CAAA;AAE3G;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAiB,CAAA;AAE7F;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAoB,CAAA;AAEtG;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAkB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAiB,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CACC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACnC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACzC,GACC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACtD;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACrB,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACnC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACzC,GACC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACE,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACrE;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAC9C,CAAA"}