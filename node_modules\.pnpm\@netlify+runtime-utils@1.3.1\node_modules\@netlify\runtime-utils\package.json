{"name": "@netlify/runtime-utils", "version": "1.3.1", "description": "Cross-environment utilities for the Netlify runtime", "type": "module", "engines": {"node": ">=16.0.0"}, "main": "./dist/main.js", "exports": "./dist/main.js", "types": "./dist/main.d.ts", "files": ["dist/**/*"], "scripts": {"build": "tsup-node", "prepack": "npm run build", "test": "vitest run", "test:dev": "vitest", "test:ci": "npm run build && vitest run", "dev": "tsup-node --watch", "publint": "npx -y publint --strict"}, "keywords": [], "license": "MIT", "repository": "netlify/primitives", "bugs": {"url": "https://github.com/netlify/primitives/issues"}, "author": "Netlify Inc.", "devDependencies": {"@types/node": "^20.5.1", "tsup": "^8.0.0", "vitest": "^3.0.0"}}