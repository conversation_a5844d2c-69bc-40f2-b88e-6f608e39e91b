import { entityKind } from "../../entity.js";
import { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from "./common.js";
class MySqlBigInt53Builder extends MySqlColumnBuilderWithAutoIncrement {
  static [entityKind] = "MySqlBigInt53Builder";
  constructor(name, unsigned = false) {
    super(name, "number", "MySqlBigInt53");
    this.config.unsigned = unsigned;
  }
  /** @internal */
  build(table) {
    return new MySqlBigInt53(
      table,
      this.config
    );
  }
}
class MySqlBigInt53 extends MySqlColumnWithAutoIncrement {
  static [entityKind] = "MySqlBigInt53";
  getSQLType() {
    return `bigint${this.config.unsigned ? " unsigned" : ""}`;
  }
  mapFromDriverValue(value) {
    if (typeof value === "number") {
      return value;
    }
    return Number(value);
  }
}
class MySqlBigInt64Builder extends MySqlColumnBuilderWithAutoIncrement {
  static [entityKind] = "MySqlBigInt64Builder";
  constructor(name, unsigned = false) {
    super(name, "bigint", "MySqlBigInt64");
    this.config.unsigned = unsigned;
  }
  /** @internal */
  build(table) {
    return new MySqlBigInt64(
      table,
      this.config
    );
  }
}
class MySqlBigInt64 extends MySqlColumnWithAutoIncrement {
  static [entityKind] = "MySqlBigInt64";
  getSQLType() {
    return `bigint${this.config.unsigned ? " unsigned" : ""}`;
  }
  // eslint-disable-next-line unicorn/prefer-native-coercion-functions
  mapFromDriverValue(value) {
    return BigInt(value);
  }
}
function bigint(name, config) {
  if (config.mode === "number") {
    return new MySqlBigInt53Builder(name, config.unsigned);
  }
  return new MySqlBigInt64Builder(name, config.unsigned);
}
export {
  MySqlBigInt53,
  MySqlBigInt53Builder,
  MySqlBigInt64,
  MySqlBigInt64Builder,
  bigint
};
//# sourceMappingURL=bigint.js.map