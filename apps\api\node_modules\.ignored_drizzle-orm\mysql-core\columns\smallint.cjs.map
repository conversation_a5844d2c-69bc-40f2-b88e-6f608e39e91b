{"version": 3, "sources": ["../../../src/mysql-core/columns/smallint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\nimport type { MySqlIntConfig } from './int.ts';\n\nexport type MySqlSmallIntBuilderInitial<TName extends string> = MySqlSmallIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlSmallInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlSmallIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlSmallInt'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlSmallIntBuilder';\n\n\tconstructor(name: T['name'], config?: MySqlIntConfig) {\n\t\tsuper(name, 'number', 'MySqlSmallInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlSmallInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlSmallInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlSmallInt<T extends ColumnBaseConfig<'number', 'MySqlSmallInt'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlSmallInt';\n\n\tgetSQLType(): string {\n\t\treturn `smallint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function smallint<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlIntConfig,\n): MySqlSmallIntBuilderInitial<TName> {\n\treturn new MySqlSmallIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkF;AAY3E,MAAM,6BACJ,kDACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,eAAe;AACrC,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBACJ,2CACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO,WAAW,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EAC1D;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,SACf,MACA,QACqC;AACrC,SAAO,IAAI,qBAAqB,MAAM,MAAM;AAC7C;", "names": []}