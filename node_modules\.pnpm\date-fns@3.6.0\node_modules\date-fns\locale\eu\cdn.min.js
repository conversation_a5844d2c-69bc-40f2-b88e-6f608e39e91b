var q=function(T){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},q(T)},D=function(T,J){var U=Object.keys(T);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(T);J&&(Z=Z.filter(function(S){return Object.getOwnPropertyDescriptor(T,S).enumerable})),U.push.apply(U,Z)}return U},K=function(T){for(var J=1;J<arguments.length;J++){var U=arguments[J]!=null?arguments[J]:{};J%2?D(Object(U),!0).forEach(function(Z){C8(T,Z,U[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(T,Object.getOwnPropertyDescriptors(U)):D(Object(U)).forEach(function(Z){Object.defineProperty(T,Z,Object.getOwnPropertyDescriptor(U,Z))})}return T},C8=function(T,J,U){if(J=G8(J),J in T)Object.defineProperty(T,J,{value:U,enumerable:!0,configurable:!0,writable:!0});else T[J]=U;return T},G8=function(T){var J=H8(T,"string");return q(J)=="symbol"?J:String(J)},H8=function(T,J){if(q(T)!="object"||!T)return T;var U=T[Symbol.toPrimitive];if(U!==void 0){var Z=U.call(T,J||"default");if(q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(T)};(function(T){var J=Object.defineProperty,U=function B(G,C){for(var H in C)J(G,H,{get:C[H],enumerable:!0,configurable:!0,set:function Y(X){return C[H]=function(){return X}}})},Z={lessThanXSeconds:{one:"segundo bat baino gutxiago",other:"{{count}} segundo baino gutxiago"},xSeconds:{one:"1 segundo",other:"{{count}} segundo"},halfAMinute:"minutu erdi",lessThanXMinutes:{one:"minutu bat baino gutxiago",other:"{{count}} minutu baino gutxiago"},xMinutes:{one:"1 minutu",other:"{{count}} minutu"},aboutXHours:{one:"1 ordu gutxi gorabehera",other:"{{count}} ordu gutxi gorabehera"},xHours:{one:"1 ordu",other:"{{count}} ordu"},xDays:{one:"1 egun",other:"{{count}} egun"},aboutXWeeks:{one:"aste 1 inguru",other:"{{count}} aste inguru"},xWeeks:{one:"1 aste",other:"{{count}} astean"},aboutXMonths:{one:"1 hilabete gutxi gorabehera",other:"{{count}} hilabete gutxi gorabehera"},xMonths:{one:"1 hilabete",other:"{{count}} hilabete"},aboutXYears:{one:"1 urte gutxi gorabehera",other:"{{count}} urte gutxi gorabehera"},xYears:{one:"1 urte",other:"{{count}} urte"},overXYears:{one:"1 urte baino gehiago",other:"{{count}} urte baino gehiago"},almostXYears:{one:"ia 1 urte",other:"ia {{count}} urte"}},S=function B(G,C,H){var Y,X=Z[G];if(typeof X==="string")Y=X;else if(C===1)Y=X.one;else Y=X.other.replace("{{count}}",String(C));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"en "+Y;else return"duela "+Y;return Y};function z(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,H=B.formats[C]||B.formats[B.defaultWidth];return H}}var W={full:"EEEE, y'ko' MMMM'ren' d'a' y'ren'",long:"y'ko' MMMM'ren' d'a'",medium:"y MMM d",short:"yy/MM/dd"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'tan' {{time}}",long:"{{date}} 'tan' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},M={date:z({formats:W,defaultWidth:"full"}),time:z({formats:$,defaultWidth:"full"}),dateTime:z({formats:R,defaultWidth:"full"})},L={lastWeek:"'joan den' eeee, LT",yesterday:"'atzo,' p",today:"'gaur,' p",tomorrow:"'bihar,' p",nextWeek:"eeee, p",other:"P"},V={lastWeek:"'joan den' eeee, p",yesterday:"'atzo,' p",today:"'gaur,' p",tomorrow:"'bihar,' p",nextWeek:"eeee, p",other:"P"},f=function B(G,C){if(C.getHours()!==1)return V[G];return L[G]};function O(B){return function(G,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Y;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):X;Y=B.formattingValues[I]||B.formattingValues[X]}else{var A=B.defaultWidth,x=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;Y=B.values[x]||B.values[A]}var E=B.argumentCallback?B.argumentCallback(G):G;return Y[E]}}var j={narrow:["k.a.","k.o."],abbreviated:["k.a.","k.o."],wide:["kristo aurretik","kristo ondoren"]},v={narrow:["1","2","3","4"],abbreviated:["1H","2H","3H","4H"],wide:["1. hiruhilekoa","2. hiruhilekoa","3. hiruhilekoa","4. hiruhilekoa"]},w={narrow:["u","o","m","a","m","e","u","a","i","u","a","a"],abbreviated:["urt","ots","mar","api","mai","eka","uzt","abu","ira","urr","aza","abe"],wide:["urtarrila","otsaila","martxoa","apirila","maiatza","ekaina","uztaila","abuztua","iraila","urria","azaroa","abendua"]},_={narrow:["i","a","a","a","o","o","l"],short:["ig","al","as","az","og","or","lr"],abbreviated:["iga","ast","ast","ast","ost","ost","lar"],wide:["igandea","astelehena","asteartea","asteazkena","osteguna","ostirala","larunbata"]},F={narrow:{am:"a",pm:"p",midnight:"ge",noon:"eg",morning:"goiza",afternoon:"arratsaldea",evening:"arratsaldea",night:"gaua"},abbreviated:{am:"AM",pm:"PM",midnight:"gauerdia",noon:"eguerdia",morning:"goiza",afternoon:"arratsaldea",evening:"arratsaldea",night:"gaua"},wide:{am:"a.m.",pm:"p.m.",midnight:"gauerdia",noon:"eguerdia",morning:"goiza",afternoon:"arratsaldea",evening:"arratsaldea",night:"gaua"}},P={narrow:{am:"a",pm:"p",midnight:"ge",noon:"eg",morning:"goizean",afternoon:"arratsaldean",evening:"arratsaldean",night:"gauean"},abbreviated:{am:"AM",pm:"PM",midnight:"gauerdia",noon:"eguerdia",morning:"goizean",afternoon:"arratsaldean",evening:"arratsaldean",night:"gauean"},wide:{am:"a.m.",pm:"p.m.",midnight:"gauerdia",noon:"eguerdia",morning:"goizean",afternoon:"arratsaldean",evening:"arratsaldean",night:"gauean"}},b=function B(G,C){var H=Number(G);return H+"."},h={ordinalNumber:b,era:O({values:j,defaultWidth:"wide"}),quarter:O({values:v,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:O({values:w,defaultWidth:"wide"}),day:O({values:_,defaultWidth:"wide"}),dayPeriod:O({values:F,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function Q(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,Y=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=G.match(Y);if(!X)return null;var I=X[0],A=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],x=Array.isArray(A)?m(A,function(N){return N.test(I)}):k(A,function(N){return N.test(I)}),E;E=B.valueCallback?B.valueCallback(x):x,E=C.valueCallback?C.valueCallback(E):E;var B8=G.slice(I.length);return{value:E,rest:B8}}}var k=function B(G,C){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&C(G[H]))return H;return},m=function B(G,C){for(var H=0;H<G.length;H++)if(C(G[H]))return H;return};function y(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(B.matchPattern);if(!H)return null;var Y=H[0],X=G.match(B.parsePattern);if(!X)return null;var I=B.valueCallback?B.valueCallback(X[0]):X[0];I=C.valueCallback?C.valueCallback(I):I;var A=G.slice(Y.length);return{value:I,rest:A}}}var c=/^(\d+)(.)?/i,p=/\d+/i,g={narrow:/^(k.a.|k.o.)/i,abbreviated:/^(k.a.|k.o.)/i,wide:/^(kristo aurretik|kristo ondoren)/i},u={narrow:[/^k.a./i,/^k.o./i],abbreviated:[/^(k.a.)/i,/^(k.o.)/i],wide:[/^(kristo aurretik)/i,/^(kristo ondoren)/i]},d={narrow:/^[1234]/i,abbreviated:/^[1234]H/i,wide:/^[1234](.)? hiruhilekoa/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[uomaei]/i,abbreviated:/^(urt|ots|mar|api|mai|eka|uzt|abu|ira|urr|aza|abe)/i,wide:/^(urtarrila|otsaila|martxoa|apirila|maiatza|ekaina|uztaila|abuztua|iraila|urria|azaroa|abendua)/i},n={narrow:[/^u/i,/^o/i,/^m/i,/^a/i,/^m/i,/^e/i,/^u/i,/^a/i,/^i/i,/^u/i,/^a/i,/^a/i],any:[/^urt/i,/^ots/i,/^mar/i,/^api/i,/^mai/i,/^eka/i,/^uzt/i,/^abu/i,/^ira/i,/^urr/i,/^aza/i,/^abe/i]},s={narrow:/^[iaol]/i,short:/^(ig|al|as|az|og|or|lr)/i,abbreviated:/^(iga|ast|ast|ast|ost|ost|lar)/i,wide:/^(igandea|astelehena|asteartea|asteazkena|osteguna|ostirala|larunbata)/i},o={narrow:[/^i/i,/^a/i,/^a/i,/^a/i,/^o/i,/^o/i,/^l/i],short:[/^ig/i,/^al/i,/^as/i,/^az/i,/^og/i,/^or/i,/^lr/i],abbreviated:[/^iga/i,/^ast/i,/^ast/i,/^ast/i,/^ost/i,/^ost/i,/^lar/i],wide:[/^igandea/i,/^astelehena/i,/^asteartea/i,/^asteazkena/i,/^osteguna/i,/^ostirala/i,/^larunbata/i]},r={narrow:/^(a|p|ge|eg|((goiza|goizean)|arratsaldea|(gaua|gauean)))/i,any:/^([ap]\.?\s?m\.?|gauerdia|eguerdia|((goiza|goizean)|arratsaldea|(gaua|gauean)))/i},a={narrow:{am:/^a/i,pm:/^p/i,midnight:/^ge/i,noon:/^eg/i,morning:/goiz/i,afternoon:/arratsaldea/i,evening:/arratsaldea/i,night:/gau/i},any:{am:/^a/i,pm:/^p/i,midnight:/^gauerdia/i,noon:/^eguerdia/i,morning:/goiz/i,afternoon:/arratsaldea/i,evening:/arratsaldea/i,night:/gau/i}},e={ordinalNumber:y({matchPattern:c,parsePattern:p,valueCallback:function B(G){return parseInt(G,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"wide"}),quarter:Q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:Q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:Q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"wide"}),dayPeriod:Q({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"eu",formatDistance:S,formatLong:M,formatRelative:f,localize:h,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(T=window.dateFns)===null||T===void 0?void 0:T.locale),{},{eu:t})})})();

//# debugId=D5C79FAEA2FEF1FA64756e2164756e21
