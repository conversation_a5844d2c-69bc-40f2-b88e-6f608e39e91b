{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestSubApp", "title": "Nest SubApp Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the application.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the app?"}, "language": {"type": "string", "description": "Nest application language."}, "path": {"type": "string", "format": "path", "description": "The path to create the application."}, "rootDir": {"type": "string", "format": "path", "description": "Applications root directory."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}