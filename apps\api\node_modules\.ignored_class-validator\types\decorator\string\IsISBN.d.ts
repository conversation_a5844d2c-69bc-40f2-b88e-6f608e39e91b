import { ValidationOptions } from '../ValidationOptions';
export type IsISBNVersion = '10' | '13' | 10 | 13;
export declare const IS_ISBN = "isIsbn";
/**
 * Checks if the string is an ISBN (version 10 or 13).
 * If given value is not a string, then it returns false.
 */
export declare function isISBN(value: unknown, version?: IsISBNVersion): boolean;
/**
 * Checks if the string is an ISBN (version 10 or 13).
 * If given value is not a string, then it returns false.
 */
export declare function IsISBN(version?: IsISBNVersion, validationOptions?: ValidationOptions): PropertyDecorator;
