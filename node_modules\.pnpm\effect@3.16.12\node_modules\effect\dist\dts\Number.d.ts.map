{"version": 3, "file": "Number.d.ts", "sourceRoot": "", "sources": ["../../src/Number.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+FG;AAEH,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAI/C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACzC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAG7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,KAAK,IAAI,MAA2B,CAAA;AAE/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,eAAO,MAAM,MAAM,GAAI,GAAG,MAAM,KAAG,MAAyB,CAAA;AAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;OAOG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAA;IAExC;;;;;;;OAOG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;CAC0B,CAAA;AAEhE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,eAAO,MAAM,MAAM,GAAI,YAAY,QAAQ,CAAC,MAAM,CAAC,KAAG,MAA8C,CAAA;AAEpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;OAQG;IACH,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,KAAK,MAAM,CAAA;IAEjD;;;;;;;OAOG;IACH,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,MAAM,CAAA;CAI9C,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;OAQG;IACH,CAAC,YAAY,EAAE,MAAM,GAAG,CAAC,UAAU,EAAE,MAAM,KAAK,MAAM,CAAA;IAEtD;;;;;;;OAOG;IACH,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,GAAG,MAAM,CAAA;CAInD,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CG;AACH,eAAO,MAAM,WAAW,GAAI,YAAY,QAAQ,CAAC,MAAM,CAAC,KAAG,MAS1D,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuDG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;;OAOG;IACH,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAA;IAEvD;;;;;;;;OAQG;IACH,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;CAC4D,CAAA;AAEjH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;;;;OAQG;IACH,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAA;IAE/C;;;;;;;;;OASG;IACH,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAA;CACiC,CAAA;AAE9E;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,SAAS,GAAI,GAAG,MAAM,KAAG,MAAmB,CAAA;AAEzD;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,SAAS,GAAI,GAAG,MAAM,KAAG,MAAwB,CAAA;AAE9D;;;;GAIG;AACH,eAAO,MAAM,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAsB,CAAA;AAE9E;;;;GAIG;AACH,eAAO,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAgB,CAAA;AAEtD;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAA;IACzC;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;CACd,CAAA;AAEzB;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,iBAAiB,EAAE;IAC9B;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAA;IACzC;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;CACL,CAAA;AAElC;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAA;IACzC;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;CACX,CAAA;AAE5B;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,oBAAoB,EAAE;IACjC;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAA;IACzC;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAA;CACF,CAAA;AAErC;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;;;;;;;;;;;;;OAkBG;IACH,CAAC,OAAO,EAAE;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAA;IAC1E;;;;;;;;;;;;;;;;;;OAkBG;IACH,CACC,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,OAAO,EAAE,MAAM,CAAA;QACf,OAAO,EAAE,MAAM,CAAA;KAChB,GACC,OAAO,CAAA;CACY,CAAA;AAExB;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,CAAC,OAAO,EAAE;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,MAAM,CAAA;KAAE,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAA;IACzE;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,CACC,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,OAAO,EAAE,MAAM,CAAA;QACf,OAAO,EAAE,MAAM,CAAA;KAChB,GACC,MAAM,CAAA;CACW,CAAA;AAEtB;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAA;IACxC;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;CAClB,CAAA;AAEpB;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAA;IACxC;;;;;;;;;;;;;OAaG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;CAClB,CAAA;AAEpB;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,IAAI,GAAI,GAAG,MAAM,KAAG,QAAuB,CAAA;AAExD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;;;;;;;;;;;;;;OAmBG;IACH,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAA;IAC/C;;;;;;;;;;;;;;;;;;;OAmBG;IACH,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAA;CAS3C,CAAA;AAEF;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,eAAO,MAAM,QAAQ,GAAI,GAAG,MAAM,KAAG,MAGpC,CAAA;AAED;;;;;;;;GAQG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;;OAQG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;CAgB5B,CAAA;AAED;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;;;;;;;;;OAeG;IACH,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAA;IAC7C;;;;;;;;;;;;;;;OAeG;IACH,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM,CAAA;CAIzC,CAAA"}