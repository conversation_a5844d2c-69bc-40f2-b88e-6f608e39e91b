{"version": 3, "file": "MetricPair.d.ts", "sourceRoot": "", "sources": ["../../src/MetricPair.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,KAAK,aAAa,MAAM,oBAAoB,CAAA;AACxD,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,OAAO,MAAkC,CAAA;AAExE;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,gBAAgB,CAAA;AAEtD;;;GAGG;AACH,MAAM,WAAW,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAChF,SAAQ,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ;IAE3C,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAC7C,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;CACzF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC;;;OAGG;IACH,UAAiB,OAAQ,SAAQ,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;KAAG;IAErF;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC;QAC9E,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;YAC3B,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;SACtC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EACpE,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EACpC,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAC5E,UAAU,CAAC,OAAuB,CAAA;AAEvC;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,SAAS,aAAa,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAC1E,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,EACpC,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,KACzC,UAAU,CAAC,OAA6B,CAAA"}