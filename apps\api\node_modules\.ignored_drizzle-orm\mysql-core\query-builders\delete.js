import { entityKind } from "../../entity.js";
import { QueryPromise } from "../../query-promise.js";
class MySqlDeleteBase extends QueryPromise {
  constructor(table, session, dialect, withList) {
    super();
    this.table = table;
    this.session = session;
    this.dialect = dialect;
    this.config = { table, withList };
  }
  static [entityKind] = "MySqlDelete";
  config;
  /** 
   * Adds a `where` clause to the query.
   * 
   * Calling this method will delete only those rows that fulfill a specified condition.
   * 
   * See docs: {@link https://orm.drizzle.team/docs/delete}
   * 
   * @param where the `where` clause.
   * 
   * @example
   * You can use conditional operators and `sql function` to filter the rows to be deleted.
   * 
   * ```ts
   * // Delete all cars with green color
   * db.delete(cars).where(eq(cars.color, 'green'));
   * // or
   * db.delete(cars).where(sql`${cars.color} = 'green'`)
   * ```
   * 
   * You can logically combine conditional operators with `and()` and `or()` operators:
   * 
   * ```ts
   * // Delete all BMW cars with a green color
   * db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));
   * 
   * // Delete all cars with the green or blue color
   * db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));
   * ```
  */
  where(where) {
    this.config.where = where;
    return this;
  }
  /** @internal */
  getSQL() {
    return this.dialect.buildDeleteQuery(this.config);
  }
  toSQL() {
    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());
    return rest;
  }
  prepare() {
    return this.session.prepareQuery(
      this.dialect.sqlToQuery(this.getSQL()),
      this.config.returning
    );
  }
  execute = (placeholderValues) => {
    return this.prepare().execute(placeholderValues);
  };
  createIterator = () => {
    const self = this;
    return async function* (placeholderValues) {
      yield* self.prepare().iterator(placeholderValues);
    };
  };
  iterator = this.createIterator();
  $dynamic() {
    return this;
  }
}
export {
  MySqlDeleteBase
};
//# sourceMappingURL=delete.js.map