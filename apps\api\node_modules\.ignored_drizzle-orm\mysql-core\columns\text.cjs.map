{"version": 3, "sources": ["../../../src/mysql-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlTextColumnType = 'tinytext' | 'text' | 'mediumtext' | 'longtext';\n\nexport type MySqlTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = MySqlTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlText';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n}>;\n\nexport class MySqlTextBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlText'>> extends MySqlColumnBuilder<\n\tT,\n\t{ textType: MySqlTextColumnType; enumValues: T['enumValues'] }\n> {\n\tstatic readonly [entityKind]: string = 'MySqlTextBuilder';\n\n\tconstructor(name: T['name'], textType: MySqlTextColumnType, config: MySqlTextConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'MySqlText');\n\t\tthis.config.textType = textType;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlText<T extends ColumnBaseConfig<'string', 'MySqlText'>>\n\textends MySqlColumn<T, { textType: MySqlTextColumnType; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'MySqlText';\n\n\tprivate textType: MySqlTextColumnType = this.config.textType;\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.textType;\n\t}\n}\n\nexport interface MySqlTextConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tenum?: TEnum;\n}\n\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlTextConfig<T | Writable<T>> = {},\n): MySqlTextBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlTextBuilder(name, 'text', config);\n}\n\nexport function tinytext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlTextConfig<T | Writable<T>> = {},\n): MySqlTextBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlTextBuilder(name, 'tinytext', config);\n}\n\nexport function mediumtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlTextConfig<T | Writable<T>> = {},\n): MySqlTextBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlTextBuilder(name, 'mediumtext', config);\n}\n\nexport function longtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlTextConfig<T | Writable<T>> = {},\n): MySqlTextBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlTextBuilder(name, 'longtext', config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAgD;AAazC,MAAM,yBAAmF,iCAG9F;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,UAA+B,QAA0C;AACrG,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBACJ,0BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE/B,WAAgC,KAAK,OAAO;AAAA,EAElC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK;AAAA,EACb;AACD;AAMO,SAAS,KACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,QAAQ,MAAM;AACjD;AAEO,SAAS,SACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,YAAY,MAAM;AACrD;AAEO,SAAS,WACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,cAAc,MAAM;AACvD;AAEO,SAAS,SACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,YAAY,MAAM;AACrD;", "names": []}