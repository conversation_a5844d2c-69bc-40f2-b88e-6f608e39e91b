{"version": 3, "sources": ["../../../src/mysql-core/columns/char.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlCharBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = MySqlCharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlChar';\n\tdata: TEnum[number];\n\tdriverParam: number | string;\n\tenumValues: TEnum;\n}>;\n\nexport class MySqlCharBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlChar'>> extends MySqlColumnBuilder<\n\tT,\n\tMySqlCharConfig<T['enumValues']>\n> {\n\tstatic readonly [entityKind]: string = 'MySqlCharBuilder';\n\n\tconstructor(name: T['name'], config: MySqlCharConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'MySqlChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlChar<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new MySqlChar<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlChar<T extends ColumnBaseConfig<'string', 'MySqlChar'>>\n\textends MySqlColumn<T, MySqlCharConfig<T['enumValues']>>\n{\n\tstatic readonly [entityKind]: string = 'MySqlChar';\n\n\treadonly length: number | undefined = this.config.length;\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `char` : `char(${this.length})`;\n\t}\n}\n\nexport interface MySqlCharConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tlength?: number;\n\tenum?: TEnum;\n}\n\nexport function char<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: MySqlCharConfig<T | Writable<T>> = {},\n): MySqlCharBuilderInitial<TName, Writable<T>> {\n\treturn new MySqlCharBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAgD;AAWzC,MAAM,yBAAmF,iCAG9F;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAA0C;AACtE,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OAC+E;AAC/E,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,kBACJ,0BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,SAA6B,KAAK,OAAO;AAAA,EAChC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,SAAS,QAAQ,KAAK,MAAM;AAAA,EAChE;AACD;AAOO,SAAS,KACf,MACA,SAA2C,CAAC,GACE;AAC9C,SAAO,IAAI,iBAAiB,MAAM,MAAM;AACzC;", "names": []}