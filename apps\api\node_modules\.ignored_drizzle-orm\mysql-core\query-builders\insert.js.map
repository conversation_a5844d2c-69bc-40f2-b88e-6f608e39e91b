{"version": 3, "sources": ["../../../src/mysql-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type {\n\tAnyQueryResultHKT,\n\tMySqlSession,\n\tPreparedQueryConfig,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n\tQueryResultHKT,\n\tQueryResultKind,\n} from '~/mysql-core/session.ts';\nimport type { MySqlTable } from '~/mysql-core/table.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport { Table } from '~/table.ts';\nimport { mapUpdateSet } from '~/utils.ts';\nimport type { MySqlUpdateSetSource } from './update.ts';\n\nexport interface MySqlInsertConfig<TTable extends MySqlTable = MySqlTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[];\n\tignore: boolean;\n\tonConflict?: SQL;\n}\n\nexport type AnyMySqlInsertConfig = MySqlInsertConfig<MySqlTable>;\n\nexport type MySqlInsertValue<TTable extends MySqlTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]: TTable['$inferInsert'][Key] | SQL | Placeholder;\n\t}\n\t& {};\n\nexport class MySqlInsertBuilder<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n> {\n\tstatic readonly [entityKind]: string = 'MySqlInsertBuilder';\n\n\tprivate shouldIgnore = false;\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: MySqlSession,\n\t\tprivate dialect: MySqlDialect,\n\t) {}\n\n\tignore(): this {\n\t\tthis.shouldIgnore = true;\n\t\treturn this;\n\t}\n\n\tvalues(value: MySqlInsertValue<TTable>): MySqlInsertBase<TTable, TQueryResult, TPreparedQueryHKT>;\n\tvalues(values: MySqlInsertValue<TTable>[]): MySqlInsertBase<TTable, TQueryResult, TPreparedQueryHKT>;\n\tvalues(\n\t\tvalues: MySqlInsertValue<TTable> | MySqlInsertValue<TTable>[],\n\t): MySqlInsertBase<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new MySqlInsertBase(this.table, mappedValues, this.shouldIgnore, this.session, this.dialect);\n\t}\n}\n\nexport type MySqlInsertWithout<T extends AnyMySqlInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tMySqlInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['preparedQueryHKT'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type MySqlInsertDynamic<T extends AnyMySqlInsert> = MySqlInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT']\n>;\n\nexport type MySqlInsertPrepare<T extends AnyMySqlInsert> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tPreparedQueryConfig & {\n\t\texecute: QueryResultKind<T['_']['queryResult'], never>;\n\t\titerator: never;\n\t},\n\ttrue\n>;\n\nexport type MySqlInsertOnDuplicateKeyUpdateConfig<T extends AnyMySqlInsert> = {\n\tset: MySqlUpdateSetSource<T['_']['table']>;\n};\n\nexport type MySqlInsert<\n\tTTable extends MySqlTable = MySqlTable,\n\tTQueryResult extends QueryResultHKT = AnyQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n> = MySqlInsertBase<TTable, TQueryResult, TPreparedQueryHKT, true, never>;\n\nexport type AnyMySqlInsert = MySqlInsertBase<any, any, any, any, any>;\n\nexport interface MySqlInsertBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<QueryResultKind<TQueryResult, never>>, SQLWrapper {\n\treadonly _: {\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t};\n}\n\nexport class MySqlInsertBase<\n\tTTable extends MySqlTable,\n\tTQueryResult extends QueryResultHKT,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<QueryResultKind<TQueryResult, never>> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'MySqlInsert';\n\n\tdeclare protected $table: TTable;\n\n\tprivate config: MySqlInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: MySqlInsertConfig['values'],\n\t\tignore: boolean,\n\t\tprivate session: MySqlSession,\n\t\tprivate dialect: MySqlDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values, ignore };\n\t}\n\n\t/**\n\t * Adds an `on duplicate key update` clause to the query.\n\t * \n\t * Calling this method will update update the row if any unique index conflicts. MySQL will automatically determine the conflict target based on the primary key and unique indexes.\n\t * \n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-duplicate-key-update}\n\t * \n\t * @param config The `set` clause\n\t * \n\t * @example\n\t * ```ts\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW'})\n\t *   .onDuplicateKeyUpdate({ set: { brand: 'Porsche' }});\n\t * ```\n\t * \n\t * While MySQL does not directly support doing nothing on conflict, you can perform a no-op by setting any column's value to itself and achieve the same effect:\n\t * \n\t * ```ts\n\t * import { sql } from 'drizzle-orm';\n\t * \n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onDuplicateKeyUpdate({ set: { id: sql`id` } });\n\t * ```\n\t */\n\tonDuplicateKeyUpdate(\n\t\tconfig: MySqlInsertOnDuplicateKeyUpdateConfig<this>,\n\t): MySqlInsertWithout<this, TDynamic, 'onDuplicateKeyUpdate'> {\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tthis.config.onConflict = sql`update ${setSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tprepare(): MySqlInsertPrepare<this> {\n\t\treturn this.session.prepareQuery(\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tundefined,\n\t\t) as MySqlInsertPrepare<this>;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this.prepare().execute(placeholderValues);\n\t};\n\n\tprivate createIterator = (): ReturnType<this['prepare']>['iterator'] => {\n\t\tconst self = this;\n\t\treturn async function*(placeholderValues) {\n\t\t\tyield* self.prepare().iterator(placeholderValues);\n\t\t};\n\t};\n\n\titerator = this.createIterator();\n\n\t$dynamic(): MySqlInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAY/B,SAAS,oBAAoB;AAE7B,SAAS,OAAO,KAAK,WAAW;AAChC,SAAS,aAAa;AACtB,SAAS,oBAAoB;AAkBtB,MAAM,mBAIX;AAAA,EAKD,YACS,OACA,SACA,SACP;AAHO;AACA;AACA;AAAA,EACN;AAAA,EARH,QAAiB,UAAU,IAAY;AAAA,EAE/B,eAAe;AAAA,EAQvB,SAAe;AACd,SAAK,eAAe;AACpB,WAAO;AAAA,EACR;AAAA,EAIA,OACC,QAC2D;AAC3D,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAED,WAAO,IAAI,gBAAgB,KAAK,OAAO,cAAc,KAAK,cAAc,KAAK,SAAS,KAAK,OAAO;AAAA,EACnG;AACD;AA0DO,MAAM,wBASH,aAAyE;AAAA,EAOlF,YACC,OACA,QACA,QACQ,SACA,SACP;AACD,UAAM;AAHE;AACA;AAGR,SAAK,SAAS,EAAE,OAAO,QAAQ,OAAO;AAAA,EACvC;AAAA,EAfA,QAAiB,UAAU,IAAY;AAAA,EAI/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCR,qBACC,QAC6D;AAC7D,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,SAAK,OAAO,aAAa,aAAa,MAAM;AAC5C,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,UAAoC;AACnC,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC;AAAA,IACD;AAAA,EACD;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+C;AACvE,UAAM,OAAO;AACb,WAAO,iBAAgB,mBAAmB;AACzC,aAAO,KAAK,QAAQ,EAAE,SAAS,iBAAiB;AAAA,IACjD;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,eAAe;AAAA,EAE/B,WAAqC;AACpC,WAAO;AAAA,EACR;AACD;", "names": []}