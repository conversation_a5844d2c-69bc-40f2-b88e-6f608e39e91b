{"name": "@netlify/functions", "main": "./dist/main.js", "types": "./dist/main.d.ts", "type": "module", "exports": {".": {"require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}, "import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}}, "./dev": {"import": {"types": "./dist-dev/main.d.ts", "default": "./dist-dev/main.js"}}, "./internal": {"require": {"types": "./dist/internal.d.cts", "default": "./dist/internal.cjs"}, "import": {"types": "./dist/internal.d.ts", "default": "./dist/internal.js"}}}, "version": "3.1.10", "description": "TypeScript utilities for interacting with Netlify Functions", "files": ["dist/**/*.js", "dist/**/*.cjs", "dist/**/*.mjs", "dist/**/*.d.ts", "dist/**/*.d.cts", "dist/**/*.d.mts", "dist-dev/**/*.js", "dist-dev/**/*.cjs", "dist-dev/**/*.mjs", "dist-dev/**/*.d.ts", "dist-dev/**/*.d.cts", "dist-dev/**/*.d.mts", "internal.d.ts"], "scripts": {"dev": "tsup-node --watch", "build": "tsup-node", "prepack": "npm run build", "test": "run-s test:ci", "test:dev": "run-s build test:dev:*", "test:dev:tsd": "tsd", "test:dev:vitest": "vitest", "test:ci": "run-s test:ci:*", "test:ci:vitest": "npm run build && vitest run", "publint": "npx -y publint --strict"}, "ava": {"files": ["test/unit/*.js"], "verbose": true}, "tsd": {"directory": "test/types/"}, "keywords": [], "license": "MIT", "repository": "netlify/primitives", "bugs": {"url": "https://github.com/netlify/primitives/issues"}, "author": "Netlify Inc.", "directories": {"test": "test"}, "dependencies": {"@netlify/blobs": "9.1.2", "@netlify/dev-utils": "2.2.0", "@netlify/serverless-functions-api": "1.41.2", "@netlify/zip-it-and-ship-it": "^12.1.0", "cron-parser": "^4.9.0", "decache": "^4.6.2", "extract-zip": "^2.0.1", "is-stream": "^4.0.1", "jwt-decode": "^4.0.0", "lambda-local": "^2.2.0", "read-package-up": "^11.0.0", "source-map-support": "^0.5.21"}, "devDependencies": {"@netlify/eslint-config-node": "^7.0.1", "@types/semver": "^7.5.8", "@types/source-map-support": "^0.5.10", "npm-run-all2": "^5.0.0", "semver": "^7.6.3", "tsd": "^0.31.0", "tsup": "^8.0.2", "vitest": "^3.0.0"}, "engines": {"node": ">=14.0.0"}}