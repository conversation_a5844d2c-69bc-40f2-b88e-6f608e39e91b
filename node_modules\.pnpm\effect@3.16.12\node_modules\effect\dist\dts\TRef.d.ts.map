{"version": 3, "file": "TRef.d.ts", "sourceRoot": "", "sources": ["../../src/TRef.ts"], "names": [], "mappings": "AAAA;;GAEG;AAMH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AACpC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,OAAO,MAA4B,CAAA;AAE5D;;;GAGG;AACH,MAAM,MAAM,UAAU,GAAG,OAAO,UAAU,CAAA;AAE1C;;;;;;;;;;;GAWG;AACH,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ;IAChE;;OAEG;IACH,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACpD;AAYD;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B;;OAEG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAChC,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;YACrB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAgB,CAAA;AAEjE;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC5C;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACpB,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAClD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACvB,CAAA;AAEzB;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACjE;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CAClC,CAAA;AAE7B;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAiB,CAAA;AAEpE;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACnE;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CAC9C,CAAA;AAEnB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC/F;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACtE,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC/C;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CAC7B,CAAA;AAEhB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAC5C;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACpB,CAAA;AAEtB;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACrD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CAChC,CAAA;AAEnB;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAClD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CACvB,CAAA;AAEzB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACpE;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CAC3C,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE;IAC7B;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACjE;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;CAClC,CAAA"}