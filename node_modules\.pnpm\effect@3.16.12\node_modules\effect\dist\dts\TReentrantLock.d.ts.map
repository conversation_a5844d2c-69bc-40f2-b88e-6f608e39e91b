{"version": 3, "file": "TReentrantLock.d.ts", "sourceRoot": "", "sources": ["../../src/TReentrantLock.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAE1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AAGpC;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,WAAW,cAAe,SAAQ,cAAc,CAAC,KAAK;CAAG;AAU/D;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC;;;OAGG;IACH,UAAiB,KAAK;QACpB,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;KACtD;CACF;AAED;;;;;;;GAOG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAwB,CAAA;AAE1F;;;;;;;GAOG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAyB,CAAA;AAE5F;;;;;GAKG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAA2B,CAAA;AAEhG;;;;;GAKG;AACH,eAAO,MAAM,eAAe,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAA4B,CAAA;AAElG;;;;;;;;GAQG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAiB,CAAA;AAEtG;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAmB,CAAA;AAEjF;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,cAAc,CAAiB,CAAA;AAE1D;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAqB,CAAA;AAE9G;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAsB,CAAA;AAEtF;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAuB,CAAA;AAEzF;;;;;;GAMG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAwB,CAAA;AAE1F;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAyB,CAAA;AAE5F;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3F;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACpE,CAAA;AAErB;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3F;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAChE,CAAA;AAEzB;;;;;GAKG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;;;OAKG;IACH,CAAC,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3F;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAC/D,CAAA;AAE1B;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAsB,CAAA;AAEhH;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,CAAwB,CAAA;AAE3F;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAuB,CAAA"}