var Q=function(G){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},Q(G)},W=function(G,C){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);C&&(Z=Z.filter(function(K){return Object.getOwnPropertyDescriptor(G,K).enumerable})),J.push.apply(J,Z)}return J},A=function(G){for(var C=1;C<arguments.length;C++){var J=arguments[C]!=null?arguments[C]:{};C%2?W(Object(J),!0).forEach(function(Z){EH(G,Z,J[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):W(Object(J)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(J,Z))})}return G},EH=function(G,C,J){if(C=UH(C),C in G)Object.defineProperty(G,C,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[C]=J;return G},UH=function(G){var C=BH(G,"string");return Q(C)=="symbol"?C:String(C)},BH=function(G,C){if(Q(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var Z=J.call(G,C||"default");if(Q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(G)};(function(G){var C=Object.defineProperty,J=function H(U,E){for(var B in E)C(U,B,{get:E[B],enumerable:!0,configurable:!0,set:function X(Y){return E[B]=function(){return Y}}})},Z={lessThanXSeconds:{one:"menos dun segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"medio minuto",lessThanXMinutes:{one:"menos dun minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"arredor dunha hora",other:"arredor de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 d\xEDa",other:"{{count}} d\xEDas"},aboutXWeeks:{one:"arredor dunha semana",other:"arredor de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"arredor de 1 mes",other:"arredor de {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"arredor dun ano",other:"arredor de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"m\xE1is dun ano",other:"m\xE1is de {{count}} anos"},almostXYears:{one:"case un ano",other:"case {{count}} anos"}},K=function H(U,E,B){var X,Y=Z[U];if(typeof Y==="string")X=Y;else if(E===1)X=Y.one;else X=Y.other.replace("{{count}}",String(E));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"en "+X;else return"hai "+X;return X};function N(H){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=U.width?String(U.width):H.defaultWidth,B=H.formats[E]||H.formats[H.defaultWidth];return B}}var $={full:"EEEE, d 'de' MMMM y",long:"d 'de' MMMM y",medium:"d MMM y",short:"dd/MM/y"},D={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} '\xE1s' {{time}}",long:"{{date}} '\xE1s' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:$,defaultWidth:"full"}),time:N({formats:D,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'o' eeee 'pasado \xE1' LT",yesterday:"'onte \xE1' p",today:"'hoxe \xE1' p",tomorrow:"'ma\xF1\xE1 \xE1' p",nextWeek:"eeee '\xE1' p",other:"P"},V={lastWeek:"'o' eeee 'pasado \xE1s' p",yesterday:"'onte \xE1s' p",today:"'hoxe \xE1s' p",tomorrow:"'ma\xF1\xE1 \xE1s' p",nextWeek:"eeee '\xE1s' p",other:"P"},f=function H(U,E,B,X){if(E.getHours()!==1)return V[U];return L[U]};function M(H){return function(U,E){var B=E!==null&&E!==void 0&&E.context?String(E.context):"standalone",X;if(B==="formatting"&&H.formattingValues){var Y=H.defaultFormattingWidth||H.defaultWidth,x=E!==null&&E!==void 0&&E.width?String(E.width):Y;X=H.formattingValues[x]||H.formattingValues[Y]}else{var I=H.defaultWidth,q=E!==null&&E!==void 0&&E.width?String(E.width):H.defaultWidth;X=H.values[q]||H.values[I]}var T=H.argumentCallback?H.argumentCallback(U):U;return X[T]}}var j={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","despois de cristo"]},w={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},v={narrow:["e","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["xan","feb","mar","abr","mai","xun","xul","ago","set","out","nov","dec"],wide:["xaneiro","febreiro","marzo","abril","maio","xu\xF1o","xullo","agosto","setembro","outubro","novembro","decembro"]},F={narrow:["d","l","m","m","j","v","s"],short:["do","lu","ma","me","xo","ve","sa"],abbreviated:["dom","lun","mar","mer","xov","ven","sab"],wide:["domingo","luns","martes","m\xE9rcores","xoves","venres","s\xE1bado"]},_={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"ma\xF1\xE1",afternoon:"tarde",evening:"tarde",night:"noite"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoite",noon:"mediod\xEDa",morning:"ma\xF1\xE1",afternoon:"tarde",evening:"tardi\xF1a",night:"noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoite",noon:"mediod\xEDa",morning:"ma\xF1\xE1",afternoon:"tarde",evening:"tardi\xF1a",night:"noite"}},P={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"da ma\xF1\xE1",afternoon:"da tarde",evening:"da tardi\xF1a",night:"da noite"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoite",noon:"mediod\xEDa",morning:"da ma\xF1\xE1",afternoon:"da tarde",evening:"da tardi\xF1a",night:"da noite"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoite",noon:"mediod\xEDa",morning:"da ma\xF1\xE1",afternoon:"da tarde",evening:"da tardi\xF1a",night:"da noite"}},k=function H(U,E){var B=Number(U);return B+"\xBA"},m={ordinalNumber:k,era:M({values:j,defaultWidth:"wide"}),quarter:M({values:w,defaultWidth:"wide",argumentCallback:function H(U){return U-1}}),month:M({values:v,defaultWidth:"wide"}),day:M({values:F,defaultWidth:"wide"}),dayPeriod:M({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function O(H){return function(U){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=E.width,X=B&&H.matchPatterns[B]||H.matchPatterns[H.defaultMatchWidth],Y=U.match(X);if(!Y)return null;var x=Y[0],I=B&&H.parsePatterns[B]||H.parsePatterns[H.defaultParseWidth],q=Array.isArray(I)?b(I,function(z){return z.test(x)}):h(I,function(z){return z.test(x)}),T;T=H.valueCallback?H.valueCallback(q):q,T=E.valueCallback?E.valueCallback(T):T;var HH=U.slice(x.length);return{value:T,rest:HH}}}var h=function H(U,E){for(var B in U)if(Object.prototype.hasOwnProperty.call(U,B)&&E(U[B]))return B;return},b=function H(U,E){for(var B=0;B<U.length;B++)if(E(U[B]))return B;return};function c(H){return function(U){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.match(H.matchPattern);if(!B)return null;var X=B[0],Y=U.match(H.parsePattern);if(!Y)return null;var x=H.valueCallback?H.valueCallback(Y[0]):Y[0];x=E.valueCallback?E.valueCallback(x):x;var I=U.slice(X.length);return{value:x,rest:I}}}var y=/^(\d+)(º)?/i,p=/\d+/i,g={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|a\.?\s?e\.?\s?c\.?|d\.?\s?c\.?|e\.?\s?c\.?)/i,wide:/^(antes de cristo|antes da era com[uú]n|despois de cristo|era com[uú]n)/i},u={any:[/^ac/i,/^dc/i],wide:[/^(antes de cristo|antes da era com[uú]n)/i,/^(despois de cristo|era com[uú]n)/i]},d={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[xfmasond]/i,abbreviated:/^(xan|feb|mar|abr|mai|xun|xul|ago|set|out|nov|dec)/i,wide:/^(xaneiro|febreiro|marzo|abril|maio|xuño|xullo|agosto|setembro|outubro|novembro|decembro)/i},n={narrow:[/^x/i,/^f/i,/^m/i,/^a/i,/^m/i,/^x/i,/^x/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^xan/i,/^feb/i,/^mar/i,/^abr/i,/^mai/i,/^xun/i,/^xul/i,/^ago/i,/^set/i,/^out/i,/^nov/i,/^dec/i]},s={narrow:/^[dlmxvs]/i,short:/^(do|lu|ma|me|xo|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|xov|ven|sab)/i,wide:/^(domingo|luns|martes|m[eé]rcores|xoves|venres|s[áa]bado)/i},o={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^x/i,/^v/i,/^s/i],any:[/^do/i,/^lu/i,/^ma/i,/^me/i,/^xo/i,/^ve/i,/^sa/i]},r={narrow:/^(a|p|mn|md|(da|[aá]s) (mañ[aá]|tarde|noite))/i,any:/^([ap]\.?\s?m\.?|medianoite|mediod[ií]a|(da|[aá]s) (mañ[aá]|tarde|noite))/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/^md/i,morning:/mañ[aá]/i,afternoon:/tarde/i,evening:/tardiña/i,night:/noite/i}},e={ordinalNumber:c({matchPattern:y,parsePattern:p,valueCallback:function H(U){return parseInt(U,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function H(U){return U+1}}),month:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},t={code:"gl",formatDistance:K,formatLong:R,formatRelative:f,localize:m,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{gl:t})})})();

//# debugId=16E82A71C30787A564756e2164756e21
