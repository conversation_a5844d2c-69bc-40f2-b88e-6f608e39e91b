{"version": 3, "file": "MetricState.d.ts", "sourceRoot": "", "sources": ["../../src/MetricState.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,KAAK,aAAa,MAAM,oBAAoB,CAAA;AACxD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE,OAAO,MAAmC,CAAA;AAE1E;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,OAAO,iBAAiB,CAAA;AAExD;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,OAAO,MAAoC,CAAA;AAE5E;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,kBAAkB,CAAA;AAE1D;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,OAAO,MAAkC,CAAA;AAExE;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,gBAAgB,CAAA;AAEtD;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAsC,CAAA;AAEhF;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,OAAO,MAAoC,CAAA;AAE5E;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,kBAAkB,CAAA;AAE1D;;;;;;;GAOG;AACH,MAAM,WAAW,WAAW,CAAC,EAAE,CAAC,CAAC,CAAE,SAAQ,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ;CAAG;AAE5F;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC;;;OAGG;IACH,UAAiB,OAAQ,SAAQ,WAAW,CAAC,GAAG,CAAC;KAAG;IAEpD;;;OAGG;IACH,UAAiB,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,CACzD,SAAQ,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE3D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE,kBAAkB,CAAA;QACjD,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;KAClB;IAED;;;OAGG;IACH,UAAiB,SAAU,SAAQ,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC;QACnF,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;QACrD,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;KAClD;IAED;;;OAGG;IACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,CAAE,SAAQ,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClH,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,CAAA;QAC7C,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;KAClB;IAED;;;OAGG;IACH,UAAiB,SAAU,SAAQ,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC;QACnF,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;QACrD,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;QAC1D,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;QACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;QACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;KACrB;IAED;;;OAGG;IACH,UAAiB,OAAQ,SAAQ,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC;QAC/E,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE,kBAAkB,CAAA;QACjD,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC3E,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;QACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;QACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;KACrB;IAED;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC5B,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;YAC5B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;SACpC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAC5C;;;OAGG;IACH,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;CAC1B,CAAA;AAEpB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,WAAW,CAAC,SAA8B,CAAA;AAEhH;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;OAGG;IACH,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;IAC1C;;;OAGG;IACH,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;CAC1B,CAAA;AAElB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CACtB,OAAO,EAAE;IACP,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;IAC1D,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;CACrB,KACE,WAAW,CAAC,SAA8B,CAAA;AAE/C;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CACpB,OAAO,EAAE;IACP,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAC3E,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;CACrB,KACE,WAAW,CAAC,OAA0B,CAAA;AAE3C;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAA0B,CAAA;AAE9G;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAA2B,CAAA;AAEhH;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,SAAqC,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAyB,CAAA;AAE1G;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,SAAqC,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,WAAW,CAAC,OAAiC,CAAA"}