{"version": 3, "sources": ["../../src/mysql-proxy/driver.ts"], "sourcesContent": ["import { DefaultLogger } from '~/logger.ts';\nimport { MySqlDatabase } from '~/mysql-core/db.ts';\nimport { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { type MySqlRemotePreparedQueryHKT, type MySqlRemoteQueryResultHKT, MySqlRemoteSession } from './session.ts';\n\nexport type MySqlRemoteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = MySqlDatabase<MySqlRemoteQueryResultHKT, MySqlRemotePreparedQueryHKT, TSchema>;\n\nexport type RemoteCallback = (\n\tsql: string,\n\tparams: any[],\n\tmethod: 'all' | 'execute',\n) => Promise<{ rows: any[] }>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tconfig: DrizzleConfig<TSchema> = {},\n): MySqlRemoteDatabase<TSchema> {\n\tconst dialect = new MySqlDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new MySqlRemoteSession(callback, dialect, schema, { logger });\n\treturn new MySqlDatabase(dialect, session, schema, 'default') as MySqlRemoteDatabase<TSchema>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA8B;AAC9B,gBAA8B;AAC9B,qBAA6B;AAC7B,uBAKO;AAEP,qBAAqG;AAY9F,SAAS,QACf,UACA,SAAiC,CAAC,GACH;AAC/B,QAAM,UAAU,IAAI,4BAAa;AACjC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,kCAAmB,UAAU,SAAS,QAAQ,EAAE,OAAO,CAAC;AAC5E,SAAO,IAAI,wBAAc,SAAS,SAAS,QAAQ,SAAS;AAC7D;", "names": []}