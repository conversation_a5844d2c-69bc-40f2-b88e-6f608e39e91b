{"version": 3, "file": "Queue.d.ts", "sourceRoot": "", "sources": ["../../src/Queue.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAE1C,OAAO,KAAK,KAAK,YAAY,MAAM,mBAAmB,CAAA;AACtD,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAClD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,OAAO,MAA+B,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,OAAO,aAAa,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,OAAO,MAA+B,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,OAAO,aAAa,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,OAAO,MAAqC,CAAA;AAE9E;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,OAAO,mBAAmB,CAAA;AAE5D;;;GAGG;AACH,eAAO,MAAM,kBAAkB,EAAE,OAAO,MAAoC,CAAA;AAE5E;;;GAGG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,kBAAkB,CAAA;AAE1D;;;GAGG;AACH,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;IAY7D,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAA;IAC/C,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,gBAAgB,CAAA;CACjD;AAED;;;GAGG;AACH,MAAM,WAAW,UAAU,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,YAAY,CAAC,CAAC,CAAC;IACzF,KAAK,CAAC,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;CACvD;AAED;;;GAGG;AACH,MAAM,WAAW,gBAAiB,SAAQ,kBAAkB;IAC1D,OAAO,CAAC,EAAE,IAAI,CAAA;CACf;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO,CAAC,EAAE,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,QAAQ;IAClF;;OAEG;IACH,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAEvC;;OAEG;IACH,WAAW,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,CAAA;IAE9B;;;;;;;;;;;;;;OAcG;IACH,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACxD;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,SAAS;IAC3F;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IAE/B;;;OAGG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAE/C;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEpD;;;;OAIG;IACH,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEpE,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IACjD,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,CAAA;CACnD;AAED;;;GAGG;AACH,MAAM,WAAW,YAAY,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IACjG,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,KAAK,CAAA;CAC9F;AAED;;;GAGG;AACH,MAAM,WAAW,kBAAmB,SAAQ,MAAM,CAAC,iBAAiB;IAClE,MAAM,CAAC,EAAE,IAAI,CAAA;CACd;AAED;;;;;GAKG;AACH,MAAM,WAAW,SAAS;IACxB;;OAEG;IACH,QAAQ,IAAI,MAAM,CAAA;IAElB;;OAEG;IACH,QAAQ,IAAI,OAAO,CAAA;IAEnB;;;;OAIG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAEpC;;;;OAIG;IACH,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAEnC;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAEvC;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAExC;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAEtC;;OAEG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAE3C;;;;OAIG;IACH,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CAC5C;AAED;;;GAGG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACnE;;;OAGG;IACH,WAAW,IAAI,MAAM,CAAA;IAErB;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAEtC;;;;OAIG;IACH,aAAa,CACX,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EACrB,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EACvD,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,GACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAEzB;;;OAGG;IACH,8BAA8B,CAC5B,MAAM,EAAE,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GACtD,IAAI,CAAA;IAEP;;;OAGG;IACH,uBAAuB,CACrB,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GACtD,IAAI,CAAA;CACR;AAED;;;GAGG;AACH,MAAM,WAAW,YAAY,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC3E;;;OAGG;IACH,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAA;IAC5B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACvC;;;;OAIG;IACH,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAC/C;;;;OAIG;IACH,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAA;IAC1B;;;;;OAKG;IACH,QAAQ,IAAI,MAAM,CAAA;IAClB;;OAEG;IACH,MAAM,IAAI,MAAM,CAAA;CACjB;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B;;;OAGG;IACH,UAAiB,eAAe,CAAC,EAAE,CAAC,CAAC;QACnC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;YACxB,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;SACrC,CAAA;KACF;IAED;;;OAGG;IACH,UAAiB,eAAe,CAAC,GAAG,CAAC,CAAC;QACpC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;YACxB,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAClC,CAAA;KACF;IAED;;;OAGG;IACH,UAAiB,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACxC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;YAC9B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;IAED;;;OAGG;IACH,UAAiB,oBAAoB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAC5C,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAC7B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAoB,CAAA;AAE5E;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAsB,CAAA;AAElF;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAsB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,oBAAoB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAiC,CAAA;AAEvF;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAA6B,CAAA;AAE/E;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAA4B,CAAA;AAE7E;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAiB,CAAA;AAEhH;;;;;;;;;;;GAWG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAoB,CAAA;AAElG;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAqB,CAAA;AAEpG;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,iBAAiB,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAoB,CAAA;AAElG;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAsB,CAAA;AAE7E;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAA0B,CAAA;AAEvF;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAiB,CAAA;AAE9F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAoB,CAAA;AAErG;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAmB,CAAA;AAEnG;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAuB,CAAA;AAE3G;;;;;;;GAOG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAA0B,CAAA;AAE9G;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAqB,CAAA;AAEpG;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC3D;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACvC,CAAA;AAElB;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IAC5C;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,OAAO,CAAA;CAClB,CAAA;AAExB;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACxE;;;;;;;;;;;;;;;;;OAiBG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACjD,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAiB,CAAA;AAE3F;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAiB,CAAA;AAE5E;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAoB,CAAA;AAE/F;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;OAKG;IACH,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACrE;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC9C,CAAA;AAErB;;;;;;;GAOG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;;;;;OAOG;IACH,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAClF;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CACxD,CAAA;AAExB;;;;;;;GAOG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IACnE;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC/C,CAAA"}