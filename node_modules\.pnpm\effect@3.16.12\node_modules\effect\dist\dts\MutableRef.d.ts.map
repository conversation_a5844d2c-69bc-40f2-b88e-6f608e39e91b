{"version": 3, "file": "MutableRef.d.ts", "sourceRoot": "", "sources": ["../../src/MutableRef.ts"], "names": [], "mappings": "AAKA,OAAO,EAAU,KAAK,WAAW,EAA6B,MAAM,kBAAkB,CAAA;AACtF,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAG7C,QAAA,MAAM,MAAM,EAAE,OAAO,MAAkD,CAAA;AAEvE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,QAAQ,EAAE,WAAW;IAC9D,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,OAAO,EAAE,CAAC,CAAA;CACX;AAqBD;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,OAAO,CAAC,KAAG,UAAU,CAAC,CAAC,CAI9C,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;OAGG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IAC/D;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAA;CAU3D,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,SAAS,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,UAAU,CAAC,MAAM,CAA+B,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,eAAe,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,MAA0C,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,GAAG,GAAI,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,KAAG,CAAiB,CAAA;AAE9D;;;GAGG;AACH,eAAO,MAAM,eAAe,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,MAA0C,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,eAAe,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,MAA0C,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACzC;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;CAQrC,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACnD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;CAID,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,SAAS,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,UAAU,CAAC,MAAM,CAA+B,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,eAAe,GAAI,MAAM,UAAU,CAAC,MAAM,CAAC,KAAG,MAA0C,CAAA;AAErG;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAA;IACrD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;CAOjD,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACzC;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;CAOrC,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAA;IAC/D;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;CAInB,CAAA;AAE1C;;;GAGG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IACnD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;CAID,CAAA;AAEhD;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,MAAM,UAAU,CAAC,OAAO,CAAC,KAAG,UAAU,CAAC,OAAO,CAA4B,CAAA"}