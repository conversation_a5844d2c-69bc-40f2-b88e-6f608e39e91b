{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestService", "title": "Nest Service Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the service.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the service?"}, "path": {"type": "string", "format": "path", "description": "The path to create the service."}, "language": {"type": "string", "description": "Nest service language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest service source root directory."}, "flat": {"type": "boolean", "default": false, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}