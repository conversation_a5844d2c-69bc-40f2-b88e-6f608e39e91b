{"version": 3, "file": "Deferred.d.ts", "sourceRoot": "", "sources": ["../../src/Deferred.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AACtC,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAI5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,cAAc,EAAE,OAAO,MAAgC,CAAA;AAEpE;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD;;;;;;;;;;;GAWG;AACH,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,SAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAKxG,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,CAAA;IAClD,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,CAAA;CACpD;AAED;;;GAGG;AACH,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IAClG,QAAQ,CAAC,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;CAClE;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAoB,SAAQ,MAAM,CAAC,iBAAiB;IACnE,MAAM,CAAC,EAAE,IAAI,CAAA;CACd;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QAC1C,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACzB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAC/B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAqB,CAAA;AAExF;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAuB,CAAA;AAEpH,QAAA,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAsB,CAAA;AAEtF,OAAO;AACL;;;;;;GAMG;AACH,MAAM,IAAI,KAAK,EAChB,CAAA;AAED;;;;;;;;;GASG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACrF;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAC1D,CAAA;AAEzB;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACrF;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACtD,CAAA;AAE7B;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/E;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACxD,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAClE;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAC3C,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC9E;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACnD,CAAA;AAEzB;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/E;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACnD,CAAA;AAE1B;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC3F;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAC3D,CAAA;AAE9B;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,MAAM,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACzE;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACnD,CAAA;AAEpB;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;OAMG;IACH,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACpF;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAC1D,CAAA;AAExB;;;;;;;GAOG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAA0B,CAAA;AAEvG;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE;IAC1B;;;;;;OAMG;IACH,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAClF;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CAClD,CAAA;AAE9B;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAuB,CAAA;AAEjG;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EACtB,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KACjB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAqB,CAAA;AAE1E;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAClE;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACxC,CAAA;AAExB;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC9E;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;CACvD,CAAA;AAErB;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC,CAA2B,CAAA;AAE7G;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAA8B,CAAA"}