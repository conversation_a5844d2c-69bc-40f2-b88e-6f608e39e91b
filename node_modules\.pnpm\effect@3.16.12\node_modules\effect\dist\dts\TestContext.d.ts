/**
 * @since 2.0.0
 */
import type * as DefaultServices from "./DefaultServices.js";
import type * as Layer from "./Layer.js";
import * as TestServices from "./TestServices.js";
/**
 * @since 2.0.0
 */
export declare const LiveContext: Layer.Layer<DefaultServices.DefaultServices>;
/**
 * @since 2.0.0
 */
export declare const TestContext: Layer.Layer<TestServices.TestServices>;
//# sourceMappingURL=TestContext.d.ts.map