{"version": 3, "file": "Differ.d.ts", "sourceRoot": "", "sources": ["../../src/Differ.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAC3C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACzC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AAEvC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAC3C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAQ3C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAwC,CAAA;AAEpE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,WAAW,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAE,SAAQ,QAAQ;IAClE,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;QACjB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACnC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;KACpC,CAAA;IACD,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAA;IACrB,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAA;IAC7C,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,KAAK,CAAA;IAC3C,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAA;CAC5C;AAED,QAAA,MAAM,gBAAgB,EAAE,OAAO,MAA2D,CAAA;AAC1F,QAAA,MAAM,kBAAkB,EAAE,OAAO,MAAiE,CAAA;AAClG,QAAA,MAAM,kBAAkB,EAAE,OAAO,MAAiE,CAAA;AAClG,QAAA,MAAM,kBAAkB,EAAE,OAAO,MAAiE,CAAA;AAClG,QAAA,MAAM,aAAa,EAAE,OAAO,MAAkD,CAAA;AAC9E,QAAA,MAAM,wBAAwB,EAAE,OAAO,MACmB,CAAA;AAE1D;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B;;OAEG;IACH,UAAiB,OAAO,CAAC;QACvB;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,kBAAkB,CAAA;QAC9C;;;;;;;WAOG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAE,SAAQ,KAAK;YACxD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;gBAC7B,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;gBAC3C,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;aAC1C,CAAA;SACF;KACF;IAED;;OAEG;IACH,UAAiB,KAAK,CAAC;QACrB;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,gBAAgB,CAAA;QAC5C;;;;;WAKG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAE,SAAQ,KAAK;YAC9D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;gBAC3B,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aACxC,CAAA;SACF;KACF;IAED;;OAEG;IACH,UAAiB,OAAO,CAAC;QACvB;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,kBAAkB,CAAA;QAC9C;;;;;WAKG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAE,SAAQ,KAAK;YAC1E,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;gBAC7B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;gBACnC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aACxC,CAAA;SACF;KACF;IAED;;OAEG;IACH,UAAiB,OAAO,CAAC;QACvB;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,kBAAkB,CAAA;QAC9C;;;;;WAKG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAE,SAAQ,KAAK;YAChD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;gBAC7B,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aACxC,CAAA;SACF;KACF;IAED;;OAEG;IACH,UAAiB,EAAE,CAAC;QAClB;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,aAAa,CAAA;QACzC;;;;;WAKG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAE,SAAQ,KAAK;YAC5F,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;gBACxB,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;gBACzC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;aAC1C,CAAA;SACF;KACF;IAED;;OAEG;IACH,UAAiB,aAAa,CAAC;QAC7B;;;WAGG;QACH,KAAY,MAAM,GAAG,OAAO,wBAAwB,CAAA;QACpD;;;;;WAKG;QACH,UAAiB,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAE,SAAQ,KAAK;YAC9D,QAAQ,CAAC,CAAC,wBAAwB,CAAC,EAAE;gBACnC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;aACxC,CAAA;SACF;KACF;CACF;AAED;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,KAEnD,CAAA;AAEf;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;OAGG;IACH,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,CAAC,KAAK,EAC/C,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACvB,KAAK,CAAA;IACV;;;OAGG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAA;CAQpF,CAAA;AAED;;;;;;;;;GASG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;;;;OASG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,KAAK,EAC1C,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACvB,KAAK,CAAA;IACV;;;;;;;;;OASG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,KAAK,CAAA;CAQ/E,CAAA;AAED;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;OAMG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,CAC7C,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACvB,KAAK,CAAA;IACV;;;;;;OAMG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAA;CAQjF,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;IACxC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAA;IACrB,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,KAAK,KAAK,CAAA;IAC1D,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,KAAK,KAAK,CAAA;IACxD,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,KAAK,KAAK,CAAA;CACzD,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAiB,CAAA;AAE1C;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,OAAO,MAAM,CACvC,OAAO,CAAC,CAAC,CAAC,EACV,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACJ,CAAA;AAExB;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAC/B,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACzB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAkB,CAAA;AAE5E;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EACtC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACzB,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAoB,CAAA;AAE5F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,KAAK,OAAO,MAAM,CACvC,OAAO,CAAC,KAAK,CAAC,EACd,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CACT,CAAA;AAEpB;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;OAKG;IACH,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAC3D,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACvB,MAAM,CACT,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EACrB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAC9C,CAAA;IACD;;;;;OAKG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,CAC9F,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,EACrB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAC9C,CAAA;CACsB,CAAA;AAEzB;;;;;GAKG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,KAAK,EAAE,KAAK,EACvC,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACzB,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAA0B,CAAA;AAEpG;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,KAAK,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,MAAM,CAAA;QACxC,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,KAAK,CAAA;KACzC,GACA,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC/D;;;;;OAKG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EACnB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAC1B,OAAO,EAAE;QACP,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,MAAM,CAAA;QACxC,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,KAAK,CAAA;KACzC,GACA,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;CACJ,CAAA;AAEtB;;;;;;;GAOG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAmB,CAAA;AAEtE;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAuB,CAAA;AAElG;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;OAKG;IACH,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAC3D,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KACvB,MAAM,CACT,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,6BAA6B;IACvD,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CACzB,CAAA;IACD;;;;;OAKG;IACH,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,CAC9F,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,6BAA6B;IACvD,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CACzB,CAAA;CACa,CAAA"}