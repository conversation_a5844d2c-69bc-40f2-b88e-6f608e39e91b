{"version": 3, "file": "FiberId.d.ts", "sourceRoot": "", "sources": ["../../src/FiberId.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAEnD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAE1C;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,OAAO,MAA+B,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,OAAO,aAAa,CAAA;AAEhD;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,IAAI,GAAG,OAAO,CAAA;AAEnC;;;GAGG;AACH,MAAM,MAAM,OAAO,GAAG,MAAM,GAAG,SAAS,CAAA;AAExC;;;GAGG;AACH,MAAM,WAAW,IAAK,SAAQ,KAAK,CAAC,KAAK,EAAE,WAAW;IACpD,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,aAAa,CAAA;IACvC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;IACrB,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACf,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;CAC7B;AAED;;;GAGG;AACH,MAAM,WAAW,OAAQ,SAAQ,KAAK,CAAC,KAAK,EAAE,WAAW;IACvD,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,aAAa,CAAA;IACvC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAA;IACxB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAA;IACnB,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAA;CACjC;AAED;;;GAGG;AACH,MAAM,WAAW,SAAU,SAAQ,KAAK,CAAC,KAAK,EAAE,WAAW;IACzD,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE,aAAa,CAAA;IACvC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAA;IAC1B,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAA;CACxB;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,IAAoB,CAAA;AAEvC;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,KAAK,OAA0B,CAAA;AAEzF;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,KAAK,SAA8B,CAAA;AAEzF;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,OAA4B,CAAA;AAE/E;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,IAAsB,CAAA;AAEtE;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,OAA4B,CAAA;AAE/E;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,IAAI,SAAgC,CAAA;AAErF;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;OAKG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CACrB,CAAA;AAEpB;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,OAA6B,CAAA;AAE9F;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;OAKG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CACnB,CAAA;AAEtB;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,MAAM,CAAgB,CAAA;AAE3E;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,KAAK,OAAuB,CAAA;AAEpF;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,MAA4B,CAAA;AAExE;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAqB,CAAA;AAEpF;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,CAAkB,CAAA;AAEhF;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,OAA6B,CAAA"}