{"version": 3, "file": "TestClock.d.ts", "sourceRoot": "", "sources": ["../../src/TestClock.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AACzC,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AACzC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAkB1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAOxC,OAAO,KAAK,WAAW,MAAM,sBAAsB,CAAA;AACnD,OAAO,KAAK,IAAI,MAAM,eAAe,CAAA;AAErC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAM,WAAW,SAAU,SAAQ,KAAK,CAAC,KAAK;IAC5C,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC7D,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACjH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;IACjD,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1C,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;CACpD;AAED;;;;GAIG;AACH,MAAM,WAAW,IAAI;IACnB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IACxB,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;CACzE;AAED;;GAEG;AACH,eAAO,MAAM,QAAQ,GACnB,SAAS,MAAM,EACf,QAAQ,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAC9D,IAGD,CAAA;AAEF;;GAEG;AACH,eAAO,MAAM,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAqD,CAAA;AAuU7G;;GAEG;AACH,eAAO,MAAM,IAAI,GAAI,MAAM,IAAI,KAAG,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAgBxG,CAAA;AAEH;;GAEG;AACH,eAAO,MAAM,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAEvG,CAAA;AAED;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,GAAI,eAAe,QAAQ,CAAC,aAAa,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAGhF,CAAA;AAED;;GAEG;AACH,eAAO,MAAM,UAAU,cAIV,QAAQ,CAAC,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAIxG,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,YAAY,QAAQ,CAAC,aAAa,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAIrG,CAAA;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,QAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAiD,CAAA;AAE1G;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,GAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAOxE,CAAA;AAEH;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,GAAI,eAAe,QAAQ,CAAC,aAAa,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAG/E,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,MAAM,QAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAmD,CAAA;AAE9G;;;;GAIG;AACH,eAAO,MAAM,SAAS,QAAO,MAAM,CAAC,MAAM,CAAC,SAAS,CAAgC,CAAA;AAEpF;;;;;GAKG;AACH,eAAO,MAAM,aAAa,GAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,SAAS,EAAE,SAAS,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAI/G,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,iBAAiB,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAA6D,CAAA"}