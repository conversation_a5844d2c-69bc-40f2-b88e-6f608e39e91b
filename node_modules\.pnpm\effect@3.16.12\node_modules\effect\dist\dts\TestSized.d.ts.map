{"version": 3, "file": "TestSized.d.ts", "sourceRoot": "", "sources": ["../../src/TestSized.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAG9C;;GAEG;AACH,eAAO,MAAM,eAAe,EAAE,OAAO,MAAuC,CAAA;AAE5E;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,OAAO,eAAe,CAAA;AAEpD;;GAEG;AACH,MAAM,WAAW,SAAS;IACxB,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE,eAAe,CAAA;IAC3C,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAC5C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IACpC,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAC5F;AAED;;GAEG;AACH,eAAO,MAAM,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAA0C,CAAA;AAelG;;GAEG;AACH,eAAO,MAAM,IAAI,GAAI,MAAM,MAAM,KAAG,SAAyD,CAAA;AAE7F;;GAEG;AACH,eAAO,MAAM,YAAY,GAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAG,SAAoC,CAAA"}