{"version": 3, "sources": ["../../../src/mysql-core/columns/date.common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnDataType,\n\tHasDefault,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport interface MySqlDateColumnBaseConfig {\n\thasOnUpdateNow: boolean;\n}\n\nexport abstract class MySqlDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends MySqlColumnBuilder<T, TRuntimeConfig & MySqlDateColumnBaseConfig, TExtraConfig> {\n\tstatic readonly [entityKind]: string = 'MySqlDateColumnBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`(now())`);\n\t}\n\n\t// \"on update now\" also adds an implicit default value to the column - https://dev.mysql.com/doc/refman/8.0/en/timestamp-initialization.html\n\tonUpdateNow(): HasDefault<this> {\n\t\tthis.config.hasOnUpdateNow = true;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as HasDefault<this>;\n\t}\n}\n\nexport abstract class MySqlDateBaseColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends MySqlColumn<T, MySqlDateColumnBaseConfig & TRuntimeConfig> {\n\tstatic readonly [entityKind]: string = 'MySqlDateColumn';\n\n\treadonly hasOnUpdateNow: boolean = this.config.hasOnUpdateNow;\n}\n"], "mappings": "AAOA,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AACpB,SAAS,aAAa,0BAA0B;AAMzC,MAAe,mCAIZ,mBAAgF;AAAA,EACzF,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAa;AACZ,WAAO,KAAK,QAAQ,YAAY;AAAA,EACjC;AAAA;AAAA,EAGA,cAAgC;AAC/B,SAAK,OAAO,iBAAiB;AAC7B,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAe,4BAGZ,YAA2D;AAAA,EACpE,QAAiB,UAAU,IAAY;AAAA,EAE9B,iBAA0B,KAAK,OAAO;AAChD;", "names": []}