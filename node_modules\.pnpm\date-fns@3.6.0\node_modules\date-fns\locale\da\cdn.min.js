var Q=function(U){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},Q(U)},D=function(U,J){var X=Object.keys(U);if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(U);J&&(E=E.filter(function(K){return Object.getOwnPropertyDescriptor(U,K).enumerable})),X.push.apply(X,E)}return X},x=function(U){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?D(Object(X),!0).forEach(function(E){C0(U,E,X[E])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(X)):D(Object(X)).forEach(function(E){Object.defineProperty(U,E,Object.getOwnPropertyDescriptor(X,E))})}return U},C0=function(U,J,X){if(J=H0(J),J in U)Object.defineProperty(U,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else U[J]=X;return U},H0=function(U){var J=B0(U,"string");return Q(J)=="symbol"?J:String(J)},B0=function(U,J){if(Q(U)!="object"||!U)return U;var X=U[Symbol.toPrimitive];if(X!==void 0){var E=X.call(U,J||"default");if(Q(E)!="object")return E;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,X=function C(B,H){for(var G in H)J(B,G,{get:H[G],enumerable:!0,configurable:!0,set:function Y(Z){return H[G]=function(){return Z}}})},E={lessThanXSeconds:{one:"mindre end \xE9t sekund",other:"mindre end {{count}} sekunder"},xSeconds:{one:"1 sekund",other:"{{count}} sekunder"},halfAMinute:"\xE9t halvt minut",lessThanXMinutes:{one:"mindre end \xE9t minut",other:"mindre end {{count}} minutter"},xMinutes:{one:"1 minut",other:"{{count}} minutter"},aboutXHours:{one:"cirka 1 time",other:"cirka {{count}} timer"},xHours:{one:"1 time",other:"{{count}} timer"},xDays:{one:"1 dag",other:"{{count}} dage"},aboutXWeeks:{one:"cirka 1 uge",other:"cirka {{count}} uger"},xWeeks:{one:"1 uge",other:"{{count}} uger"},aboutXMonths:{one:"cirka 1 m\xE5ned",other:"cirka {{count}} m\xE5neder"},xMonths:{one:"1 m\xE5ned",other:"{{count}} m\xE5neder"},aboutXYears:{one:"cirka 1 \xE5r",other:"cirka {{count}} \xE5r"},xYears:{one:"1 \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"over 1 \xE5r",other:"over {{count}} \xE5r"},almostXYears:{one:"n\xE6sten 1 \xE5r",other:"n\xE6sten {{count}} \xE5r"}},K=function C(B,H,G){var Y,Z=E[B];if(typeof Z==="string")Y=Z;else if(H===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(H));if(G!==null&&G!==void 0&&G.addSuffix)if(G.comparison&&G.comparison>0)return"om "+Y;else return Y+" siden";return Y};function W(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=B.width?String(B.width):C.defaultWidth,G=C.formats[H]||C.formats[C.defaultWidth];return G}}var M={full:"EEEE 'den' d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd/MM/y"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} 'kl'. {{time}}",long:"{{date}} 'kl'. {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},L={date:W({formats:M,defaultWidth:"full"}),time:W({formats:$,defaultWidth:"full"}),dateTime:W({formats:S,defaultWidth:"full"})},R={lastWeek:"'sidste' eeee 'kl.' p",yesterday:"'i g\xE5r kl.' p",today:"'i dag kl.' p",tomorrow:"'i morgen kl.' p",nextWeek:"'p\xE5' eeee 'kl.' p",other:"P"},V=function C(B,H,G,Y){return R[B]};function A(C){return function(B,H){var G=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",Y;if(G==="formatting"&&C.formattingValues){var Z=C.defaultFormattingWidth||C.defaultWidth,I=H!==null&&H!==void 0&&H.width?String(H.width):Z;Y=C.formattingValues[I]||C.formattingValues[Z]}else{var T=C.defaultWidth,N=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;Y=C.values[N]||C.values[T]}var q=C.argumentCallback?C.argumentCallback(B):B;return Y[q]}}var f={narrow:["fvt","vt"],abbreviated:["f.v.t.","v.t."],wide:["f\xF8r vesterlandsk tidsregning","vesterlandsk tidsregning"]},j={narrow:["1","2","3","4"],abbreviated:["1. kvt.","2. kvt.","3. kvt.","4. kvt."],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mar.","apr.","maj","jun.","jul.","aug.","sep.","okt.","nov.","dec."],wide:["januar","februar","marts","april","maj","juni","juli","august","september","oktober","november","december"]},w={narrow:["S","M","T","O","T","F","L"],short:["s\xF8","ma","ti","on","to","fr","l\xF8"],abbreviated:["s\xF8n.","man.","tir.","ons.","tor.","fre.","l\xF8r."],wide:["s\xF8ndag","mandag","tirsdag","onsdag","torsdag","fredag","l\xF8rdag"]},_={narrow:{am:"a",pm:"p",midnight:"midnat",noon:"middag",morning:"morgen",afternoon:"eftermiddag",evening:"aften",night:"nat"},abbreviated:{am:"AM",pm:"PM",midnight:"midnat",noon:"middag",morning:"morgen",afternoon:"eftermiddag",evening:"aften",night:"nat"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnat",noon:"middag",morning:"morgen",afternoon:"eftermiddag",evening:"aften",night:"nat"}},F={narrow:{am:"a",pm:"p",midnight:"midnat",noon:"middag",morning:"om morgenen",afternoon:"om eftermiddagen",evening:"om aftenen",night:"om natten"},abbreviated:{am:"AM",pm:"PM",midnight:"midnat",noon:"middag",morning:"om morgenen",afternoon:"om eftermiddagen",evening:"om aftenen",night:"om natten"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnat",noon:"middag",morning:"om morgenen",afternoon:"om eftermiddagen",evening:"om aftenen",night:"om natten"}},P=function C(B,H){var G=Number(B);return G+"."},b={ordinalNumber:P,era:A({values:f,defaultWidth:"wide"}),quarter:A({values:j,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:A({values:v,defaultWidth:"wide"}),day:A({values:w,defaultWidth:"wide"}),dayPeriod:A({values:_,defaultWidth:"wide",formattingValues:F,defaultFormattingWidth:"wide"})};function O(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=H.width,Y=G&&C.matchPatterns[G]||C.matchPatterns[C.defaultMatchWidth],Z=B.match(Y);if(!Z)return null;var I=Z[0],T=G&&C.parsePatterns[G]||C.parsePatterns[C.defaultParseWidth],N=Array.isArray(T)?h(T,function(z){return z.test(I)}):k(T,function(z){return z.test(I)}),q;q=C.valueCallback?C.valueCallback(N):N,q=H.valueCallback?H.valueCallback(q):q;var t=B.slice(I.length);return{value:q,rest:t}}}var k=function C(B,H){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&H(B[G]))return G;return},h=function C(B,H){for(var G=0;G<B.length;G++)if(H(B[G]))return G;return};function m(C){return function(B){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},G=B.match(C.matchPattern);if(!G)return null;var Y=G[0],Z=B.match(C.parsePattern);if(!Z)return null;var I=C.valueCallback?C.valueCallback(Z[0]):Z[0];I=H.valueCallback?H.valueCallback(I):I;var T=B.slice(Y.length);return{value:I,rest:T}}}var c=/^(\d+)(\.)?/i,y=/\d+/i,p={narrow:/^(fKr|fvt|eKr|vt)/i,abbreviated:/^(f\.Kr\.?|f\.v\.t\.?|e\.Kr\.?|v\.t\.)/i,wide:/^(f.Kr.|før vesterlandsk tidsregning|e.Kr.|vesterlandsk tidsregning)/i},g={any:[/^f/i,/^(v|e)/i]},d={narrow:/^[1234]/i,abbreviated:/^[1234]. kvt\./i,wide:/^[1234]\.? kvartal/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(jan.|feb.|mar.|apr.|maj|jun.|jul.|aug.|sep.|okt.|nov.|dec.)/i,wide:/^(januar|februar|marts|april|maj|juni|juli|august|september|oktober|november|december)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[smtofl]/i,short:/^(søn.|man.|tir.|ons.|tor.|fre.|lør.)/i,abbreviated:/^(søn|man|tir|ons|tor|fre|lør)/i,wide:/^(søndag|mandag|tirsdag|onsdag|torsdag|fredag|lørdag)/i},s={narrow:[/^s/i,/^m/i,/^t/i,/^o/i,/^t/i,/^f/i,/^l/i],any:[/^s/i,/^m/i,/^ti/i,/^o/i,/^to/i,/^f/i,/^l/i]},o={narrow:/^(a|p|midnat|middag|(om) (morgenen|eftermiddagen|aftenen|natten))/i,any:/^([ap]\.?\s?m\.?|midnat|middag|(om) (morgenen|eftermiddagen|aftenen|natten))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/midnat/i,noon:/middag/i,morning:/morgen/i,afternoon:/eftermiddag/i,evening:/aften/i,night:/nat/i}},e={ordinalNumber:m({matchPattern:c,parsePattern:y,valueCallback:function C(B){return parseInt(B,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"da",formatDistance:K,formatLong:L,formatRelative:V,localize:b,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{da:a})})})();

//# debugId=D95CA1286E57DF8464756e2164756e21
