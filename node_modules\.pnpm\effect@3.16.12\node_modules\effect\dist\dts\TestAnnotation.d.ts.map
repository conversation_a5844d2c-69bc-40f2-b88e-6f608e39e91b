{"version": 3, "file": "TestAnnotation.d.ts", "sourceRoot": "", "sources": ["../../src/TestAnnotation.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAGxC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAEvC,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAElD,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAKxC;;GAEG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAA4C,CAAA;AAEtF;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;GAEG;AACH,MAAM,WAAW,cAAc,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,KAAK;IAC3D,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAC/B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;KAChC,CAAA;IACD,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAA;IAC3B,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAA;IACnB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAA;CACvB;AAyBD;;GAEG;AACH,eAAO,MAAM,gBAAgB,GAAI,GAAG,OAAO,KAAG,CAAC,IAAI,cAAc,CAAC,OAAO,CAAyC,CAAA;AAElH;;GAEG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EACpB,YAAY,MAAM,EAClB,SAAS,CAAC,EACV,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KACzB,cAAc,CAAC,CAAC,CAElB,CAAA;AAED;;GAEG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,EACvB,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAC3C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CActC,CAAA;AAED;;GAEG;AACH,eAAO,MAAM,MAAM,EAAE,cAAc,CACjC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAOrH,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,OAAO,EAAE,cAAc,CAAC,MAAM,CAI1C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,QAAQ,EAAE,cAAc,CAAC,MAAM,CAI3C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,OAAO,EAAE,cAAc,CAAC,MAAM,CAI1C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,MAAM,EAAE,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAI1D,CAAA"}