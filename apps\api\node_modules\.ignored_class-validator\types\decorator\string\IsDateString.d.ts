import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_DATE_STRING = "isDateString";
/**
 * Alias for IsISO8601 validator
 */
export declare function isDateString(value: unknown, options?: ValidatorJS.IsISO8601Options): boolean;
/**
 * Alias for IsISO8601 validator
 */
export declare function IsDateString(options?: ValidatorJS.IsISO8601Options, validationOptions?: ValidationOptions): PropertyDecorator;
