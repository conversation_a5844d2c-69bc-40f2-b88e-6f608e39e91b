{"version": 3, "file": "MetricPolling.d.ts", "sourceRoot": "", "sources": ["../../src/MetricPolling.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAA;AAC9C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,OAAO,MAAqC,CAAA;AAE9E;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,OAAO,mBAAmB,CAAA;AAE5D;;;;;;GAMG;AACH,MAAM,WAAW,aAAa,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAE,SAAQ,QAAQ;IAC5F,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE,mBAAmB,CAAA;IACnD;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAA;IAC7C;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACvC;AAED;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EACrC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,EACpC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAC1B,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAiB,CAAA;AAEvD;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EACjC,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,KACnD,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAuB,CAAA;AAElF;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;OAMG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAC1E,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KACrC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;IACnE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAC1B,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACxC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,GAC3C,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,CAAA;CAChD,CAAA;AAEnB;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAC7F,CAAA;AAEf;;;;;GAKG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAC9C,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KACrC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAA0B,CAAA;AAEvD;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;IACjK;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EACzB,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACxC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAClC,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;CAC1B,CAAA;AAElB;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;OAKG;IACH,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAC7F,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KACrC,aAAa,CAChB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,6BAA6B;IACrD,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,iCAAiC;IACrD,AADoB,iCAAiC;IACrD,EAAE,GAAG,CAAC,EACN,EAAE,GAAG,CAAC,EACN;QAAC,GAAG;QAAE,IAAI;KAAC,CACZ,CAAA;IACD;;;;;OAKG;IACH,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAC5C,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACxC,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,GAC5C,aAAa,CACd,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,6BAA6B;IACrD,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,iCAAiC;IACrD,AADoB,iCAAiC;IACrD,CAAC,GAAG,EAAE,EACN,CAAC,GAAG,EAAE,EACN;QAAC,GAAG;QAAE,IAAI;KAAC,CACZ,CAAA;CACa,CAAA"}