{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestModule", "title": "Nest Module Options Schema", "type": "object", "properties": {"initApp": {"type": "boolean", "description": "Flag to skip the angular application generation.", "default": false, "x-prompt": "Would you like to initialize Angular application?"}, "name": {"type": "string", "description": "The name of the application.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to (or do you) use for Angular application?"}}, "required": ["name"]}