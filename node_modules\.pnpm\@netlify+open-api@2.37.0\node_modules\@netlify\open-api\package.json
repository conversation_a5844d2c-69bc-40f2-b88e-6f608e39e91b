{"name": "@netlify/open-api", "description": "Netlify's open-api definition as a module", "version": "2.37.0", "author": "Netlify", "ava": {"files": ["src/**/*.test.js"]}, "bugs": {"url": "https://github.com/netlify/open-api/issues"}, "engines": {"node": ">=14.8.0"}, "devDependencies": {"@redocly/cli": "^1.0.0-beta.128", "analytics": "^0.2.0", "analytics-plugin-ga": "^0.1.5", "ava": "^5.2.0", "cp-file": "^7.0.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.7.0", "eslint-plugin-prettier": "^4.2.1", "execa": "^2.0.4", "is-plain-obj": "^2.1.0", "npm-run-all": "^4.1.5", "openapi-typescript": "^5.4.2", "prettier": "^2.8.7", "swagger-parser": "^8.0.1", "swagger2openapi": "^7.0.8"}, "files": ["dist/index.d.ts", "dist/swagger.json"], "homepage": "https://github.com/netlify/open-api#readme", "keywords": ["netlify", "open-api", "swagger"], "license": "MIT", "main": "dist/swagger.json", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/netlify/open-api.git"}, "scripts": {"prepublishOnly": "npm test", "test": "run-s lint build unit", "start": "run-s lint build", "bundle": "redocly bundle external@latest --output external.yml", "build": "run-s convert bundle redoc", "postbuild": "swagger2openapi external.yml -o dist/openapi.json && node src/typegen.js", "convert": "node src/convert.js", "bump-swagger": "node src/bump-swagger.js", "redoc": "node src/docs/build.js", "lint": "run-s eslint prettier", "eslint": "eslint --fix \"src/**/*.js\"", "prettier": "prettier --write --loglevel warn \"src/**/*.js\" \"*.{js,md,yml,json}\" \"!CHANGELOG.md\"", "unit": "ava"}}