import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_CURRENCY = "isCurrency";
/**
 * Checks if the string is a valid currency amount.
 * If given value is not a string, then it returns false.
 */
export declare function isCurrency(value: unknown, options?: ValidatorJS.IsCurrencyOptions): boolean;
/**
 * Checks if the string is a valid currency amount.
 * If given value is not a string, then it returns false.
 */
export declare function IsCurrency(options?: ValidatorJS.IsCurrencyOptions, validationOptions?: ValidationOptions): PropertyDecorator;
