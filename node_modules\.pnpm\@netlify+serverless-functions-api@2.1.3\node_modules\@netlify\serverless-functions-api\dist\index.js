import{createRequire}from"module";const require=createRequire(import.meta.url);
var TS=Object.create;var Gr=Object.defineProperty;var pS=Object.getOwnPropertyDescriptor;var uS=Object.getOwnPropertyNames;var SS=Object.getPrototypeOf,lS=Object.prototype.hasOwnProperty;var Ha=e=>{throw TypeError(e)};var Lo=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var c=(e,t)=>()=>(e&&(t=e(e=0)),t);var R=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),te=(e,t)=>{for(var r in t)Gr(e,r,{get:t[r],enumerable:!0})},Ya=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of uS(t))!lS.call(e,o)&&o!==r&&Gr(e,o,{get:()=>t[o],enumerable:!(n=pS(t,o))||n.enumerable});return e};var AS=(e,t,r)=>(r=e!=null?TS(SS(e)):{},Ya(t||!e||!e.__esModule?Gr(r,"default",{value:e,enumerable:!0}):r,e)),Te=e=>Ya(Gr({},"__esModule",{value:!0}),e);var Fa=(e,t,r)=>t.has(e)||Ha("Cannot "+r);var Hr=(e,t,r)=>(Fa(e,t,"read from private field"),r?r.call(e):t.get(e)),go=(e,t,r)=>t.has(e)?Ha("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Co=(e,t,r,n)=>(Fa(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);var qa,ja=c(()=>{qa=typeof globalThis=="object"?globalThis:global});var Ka=c(()=>{ja()});var Xa=c(()=>{Ka()});var Ne,Io=c(()=>{Ne="1.9.0"});function dS(e){var t=new Set([e]),r=new Set,n=e.match(Wa);if(!n)return function(){return!1};var o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(o.prerelease!=null)return function(E){return E===e};function s(i){return r.add(i),!1}function a(i){return t.add(i),!0}return function(E){if(t.has(E))return!0;if(r.has(E))return!1;var _=E.match(Wa);if(!_)return s(E);var T={major:+_[1],minor:+_[2],patch:+_[3],prerelease:_[4]};return T.prerelease!=null||o.major!==T.major?s(E):o.major===0?o.minor===T.minor&&o.patch<=T.patch?a(E):s(E):o.minor<=T.minor?a(E):s(E)}}var Wa,za,Ja=c(()=>{Io();Wa=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;za=dS(Ne)});function Ae(e,t,r,n){var o;n===void 0&&(n=!1);var s=wt[Vt]=(o=wt[Vt])!==null&&o!==void 0?o:{version:Ne};if(!n&&s[e]){var a=new Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(s.version!==Ne){var a=new Error("@opentelemetry/api: Registration of version v"+s.version+" for "+e+" does not match previously registered API v"+Ne);return r.error(a.stack||a.message),!1}return s[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+Ne+"."),!0}function ee(e){var t,r,n=(t=wt[Vt])===null||t===void 0?void 0:t.version;if(!(!n||!za(n)))return(r=wt[Vt])===null||r===void 0?void 0:r[e]}function fe(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+Ne+".");var r=wt[Vt];r&&delete r[e]}var hS,Vt,wt,He=c(()=>{Xa();Io();Ja();hS=Ne.split(".")[0],Vt=Symbol.for("opentelemetry.js.api."+hS),wt=qa});function Bt(e,t,r){var n=ee("diag");if(n)return r.unshift(t),n[e].apply(n,OS([],mS(r),!1))}var mS,OS,Qa,Za=c(()=>{He();mS=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},OS=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,s;n<o;n++)(s||!(n in t))&&(s||(s=Array.prototype.slice.call(t,0,n)),s[n]=t[n]);return e.concat(s||Array.prototype.slice.call(t))},Qa=function(){function e(t){this._namespace=t.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Bt("debug",this._namespace,t)},e.prototype.error=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Bt("error",this._namespace,t)},e.prototype.info=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Bt("info",this._namespace,t)},e.prototype.warn=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Bt("warn",this._namespace,t)},e.prototype.verbose=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return Bt("verbose",this._namespace,t)},e}()});var g,Yr=c(()=>{(function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"})(g||(g={}))});function ei(e,t){e<g.NONE?e=g.NONE:e>g.ALL&&(e=g.ALL),t=t||{};function r(n,o){var s=t[n];return typeof s=="function"&&e>=o?s.bind(t):function(){}}return{error:r("error",g.ERROR),warn:r("warn",g.WARN),info:r("info",g.INFO),debug:r("debug",g.DEBUG),verbose:r("verbose",g.VERBOSE)}}var ti=c(()=>{Yr()});var NS,vS,PS,b,Ye=c(()=>{Za();ti();Yr();He();NS=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},vS=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,s;n<o;n++)(s||!(n in t))&&(s||(s=Array.prototype.slice.call(t,0,n)),s[n]=t[n]);return e.concat(s||Array.prototype.slice.call(t))},PS="diag",b=function(){function e(){function t(o){return function(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var i=ee("diag");if(i)return i[o].apply(i,vS([],NS(s),!1))}}var r=this,n=function(o,s){var a,i,E;if(s===void 0&&(s={logLevel:g.INFO}),o===r){var _=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return r.error((a=_.stack)!==null&&a!==void 0?a:_.message),!1}typeof s=="number"&&(s={logLevel:s});var T=ee("diag"),p=ei((i=s.logLevel)!==null&&i!==void 0?i:g.INFO,o);if(T&&!s.suppressOverrideMessage){var f=(E=new Error().stack)!==null&&E!==void 0?E:"<failed to generate stacktrace>";T.warn("Current logger will be overwritten from "+f),p.warn("Current logger will overwrite one already registered from "+f)}return Ae("diag",p,r,!0)};r.setLogger=n,r.disable=function(){fe(PS,r)},r.createComponentLogger=function(o){return new Qa(o)},r.verbose=t("verbose"),r.debug=t("debug"),r.info=t("info"),r.warn=t("warn"),r.error=t("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}()});var LS,gS,ri,ni=c(()=>{LS=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},gS=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},ri=function(){function e(t){this._entries=t?new Map(t):new Map}return e.prototype.getEntry=function(t){var r=this._entries.get(t);if(r)return Object.assign({},r)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(t){var r=LS(t,2),n=r[0],o=r[1];return[n,o]})},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var s=new e(this._entries);try{for(var a=gS(n),i=a.next();!i.done;i=a.next()){var E=i.value;s._entries.delete(E)}}catch(_){t={error:_}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return s},e.prototype.clear=function(){return new e},e}()});var oi,si=c(()=>{oi=Symbol("BaggageEntryMetadata")});function ai(e){return e===void 0&&(e={}),new ri(new Map(Object.entries(e)))}function Fr(e){return typeof e!="string"&&(CS.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:oi,toString:function(){return e}}}var CS,Mo=c(()=>{Ye();ni();si();CS=b.instance()});function re(e){return Symbol.for(e)}var IS,$r,Gt=c(()=>{IS=function(){function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(n){return r._currentContext.get(n)},r.setValue=function(n,o){var s=new e(r._currentContext);return s._currentContext.set(n,o),s},r.deleteValue=function(n){var o=new e(r._currentContext);return o._currentContext.delete(n),o}}return e}(),$r=new IS});var xo,ii,Ei=c(()=>{xo=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],ii=function(){function e(){function t(n){return function(){for(var o=[],s=0;s<arguments.length;s++)o[s]=arguments[s];if(console){var a=console[n];if(typeof a!="function"&&(a=console.log),typeof a=="function")return a.apply(console,o)}}}for(var r=0;r<xo.length;r++)this[xo[r].n]=t(xo[r].c)}return e}()});function _i(){return Uo}var Fe,MS,kr,xS,DS,US,yS,Do,bS,VS,wS,Uo,BS,GS,HS,YS,FS,$S,kS,yo=c(()=>{Fe=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,o){n.__proto__=o}||function(n,o){for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(n[s]=o[s])},e(t,r)};return function(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),MS=function(){function e(){}return e.prototype.createGauge=function(t,r){return GS},e.prototype.createHistogram=function(t,r){return HS},e.prototype.createCounter=function(t,r){return BS},e.prototype.createUpDownCounter=function(t,r){return YS},e.prototype.createObservableGauge=function(t,r){return $S},e.prototype.createObservableCounter=function(t,r){return FS},e.prototype.createObservableUpDownCounter=function(t,r){return kS},e.prototype.addBatchObservableCallback=function(t,r){},e.prototype.removeBatchObservableCallback=function(t){},e}(),kr=function(){function e(){}return e}(),xS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.add=function(r,n){},t}(kr),DS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.add=function(r,n){},t}(kr),US=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.record=function(r,n){},t}(kr),yS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.record=function(r,n){},t}(kr),Do=function(){function e(){}return e.prototype.addCallback=function(t){},e.prototype.removeCallback=function(t){},e}(),bS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(Do),VS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(Do),wS=function(e){Fe(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(Do),Uo=new MS,BS=new xS,GS=new US,HS=new yS,YS=new DS,FS=new bS,$S=new VS,kS=new wS});var qr,ci=c(()=>{(function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"})(qr||(qr={}))});var jr,Kr,bo=c(()=>{jr={get:function(e,t){if(e!=null)return e[t]},keys:function(e){return e==null?[]:Object.keys(e)}},Kr={set:function(e,t,r){e!=null&&(e[t]=r)}}});var qS,jS,Ti,pi=c(()=>{Gt();qS=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},jS=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,s;n<o;n++)(s||!(n in t))&&(s||(s=Array.prototype.slice.call(t,0,n)),s[n]=t[n]);return e.concat(s||Array.prototype.slice.call(t))},Ti=function(){function e(){}return e.prototype.active=function(){return $r},e.prototype.with=function(t,r,n){for(var o=[],s=3;s<arguments.length;s++)o[s-3]=arguments[s];return r.call.apply(r,jS([n],qS(o),!1))},e.prototype.bind=function(t,r){return r},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}()});var KS,XS,Vo,WS,ge,Ht=c(()=>{pi();He();Ye();KS=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},XS=function(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,s;n<o;n++)(s||!(n in t))&&(s||(s=Array.prototype.slice.call(t,0,n)),s[n]=t[n]);return e.concat(s||Array.prototype.slice.call(t))},Vo="context",WS=new Ti,ge=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(t){return Ae(Vo,t,b.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(t,r,n){for(var o,s=[],a=3;a<arguments.length;a++)s[a-3]=arguments[a];return(o=this._getContextManager()).with.apply(o,XS([t,r,n],KS(s),!1))},e.prototype.bind=function(t,r){return this._getContextManager().bind(t,r)},e.prototype._getContextManager=function(){return ee(Vo)||WS},e.prototype.disable=function(){this._getContextManager().disable(),fe(Vo,b.instance())},e}()});var O,wo=c(()=>{(function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"})(O||(O={}))});var Yt,Ft,et,Xr=c(()=>{wo();Yt="0000000000000000",Ft="00000000000000000000000000000000",et={traceId:Ft,spanId:Yt,traceFlags:O.NONE}});var Ce,Wr=c(()=>{Xr();Ce=function(){function e(t){t===void 0&&(t=et),this._spanContext=t}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(t,r){return this},e.prototype.setAttributes=function(t){return this},e.prototype.addEvent=function(t,r){return this},e.prototype.addLink=function(t){return this},e.prototype.addLinks=function(t){return this},e.prototype.setStatus=function(t){return this},e.prototype.updateName=function(t){return this},e.prototype.end=function(t){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(t,r){},e}()});function zr(e){return e.getValue(Bo)||void 0}function ui(){return zr(ge.getInstance().active())}function $t(e,t){return e.setValue(Bo,t)}function Si(e){return e.deleteValue(Bo)}function li(e,t){return $t(e,new Ce(t))}function Jr(e){var t;return(t=zr(e))===null||t===void 0?void 0:t.spanContext()}var Bo,Go=c(()=>{Gt();Wr();Ht();Bo=re("OpenTelemetry Context Key SPAN")});function pe(e){return zS.test(e)&&e!==Ft}function $e(e){return JS.test(e)&&e!==Yt}function V(e){return pe(e.traceId)&&$e(e.spanId)}function Ai(e){return new Ce(e)}var zS,JS,Qr=c(()=>{Xr();Wr();zS=/^([0-9a-f]{32})$/i,JS=/^[0-9a-f]{16}$/i});function QS(e){return typeof e=="object"&&typeof e.spanId=="string"&&typeof e.traceId=="string"&&typeof e.traceFlags=="number"}var Ho,Zr,Yo=c(()=>{Ht();Go();Wr();Qr();Ho=ge.getInstance(),Zr=function(){function e(){}return e.prototype.startSpan=function(t,r,n){n===void 0&&(n=Ho.active());var o=!!(r!=null&&r.root);if(o)return new Ce;var s=n&&Jr(n);return QS(s)&&V(s)?new Ce(s):new Ce},e.prototype.startActiveSpan=function(t,r,n,o){var s,a,i;if(!(arguments.length<2)){arguments.length===2?i=r:arguments.length===3?(s=r,i=n):(s=r,a=n,i=o);var E=a??Ho.active(),_=this.startSpan(t,s,E),T=$t(E,_);return Ho.with(T,i,void 0,_)}},e}()});var ZS,en,Fo=c(()=>{Yo();ZS=new Zr,en=function(){function e(t,r,n,o){this._provider=t,this.name=r,this.version=n,this.options=o}return e.prototype.startSpan=function(t,r,n){return this._getTracer().startSpan(t,r,n)},e.prototype.startActiveSpan=function(t,r,n,o){var s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):ZS},e}()});var fi,Ri=c(()=>{Yo();fi=function(){function e(){}return e.prototype.getTracer=function(t,r,n){return new Zr},e}()});var el,kt,$o=c(()=>{Fo();Ri();el=new fi,kt=function(){function e(){}return e.prototype.getTracer=function(t,r,n){var o;return(o=this.getDelegateTracer(t,r,n))!==null&&o!==void 0?o:new en(this,t,r,n)},e.prototype.getDelegate=function(){var t;return(t=this._delegate)!==null&&t!==void 0?t:el},e.prototype.setDelegate=function(t){this._delegate=t},e.prototype.getDelegateTracer=function(t,r,n){var o;return(o=this._delegate)===null||o===void 0?void 0:o.getTracer(t,r,n)},e}()});var X,di=c(()=>{(function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(X||(X={}))});var tt,hi=c(()=>{(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(tt||(tt={}))});var Ie,mi=c(()=>{(function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"})(Ie||(Ie={}))});function Oi(e){return nl.test(e)}function Ni(e){return ol.test(e)&&!sl.test(e)}var ko,tl,rl,nl,ol,sl,vi=c(()=>{ko="[_0-9a-z-*/]",tl="[a-z]"+ko+"{0,255}",rl="[a-z0-9]"+ko+"{0,240}@[a-z]"+ko+"{0,13}",nl=new RegExp("^(?:"+tl+"|"+rl+")$"),ol=/^[ -~]{0,255}[!-~]$/,sl=/,|=/});var Pi,al,Li,gi,Ci,Ii=c(()=>{vi();Pi=32,al=512,Li=",",gi="=",Ci=function(){function e(t){this._internalState=new Map,t&&this._parse(t)}return e.prototype.set=function(t,r){var n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,r),n},e.prototype.unset=function(t){var r=this._clone();return r._internalState.delete(t),r},e.prototype.get=function(t){return this._internalState.get(t)},e.prototype.serialize=function(){var t=this;return this._keys().reduce(function(r,n){return r.push(n+gi+t.get(n)),r},[]).join(Li)},e.prototype._parse=function(t){t.length>al||(this._internalState=t.split(Li).reverse().reduce(function(r,n){var o=n.trim(),s=o.indexOf(gi);if(s!==-1){var a=o.slice(0,s),i=o.slice(s+1,n.length);Oi(a)&&Ni(i)&&r.set(a,i)}return r},new Map),this._internalState.size>Pi&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,Pi))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}()});function Mi(e){return new Ci(e)}var xi=c(()=>{Ii()});var y,Di=c(()=>{Ht();y=ge.getInstance()});var S,Ui=c(()=>{Ye();S=b.instance()});var il,yi,bi=c(()=>{yo();il=function(){function e(){}return e.prototype.getMeter=function(t,r,n){return Uo},e}(),yi=new il});var qo,Vi,wi=c(()=>{bi();He();Ye();qo="metrics",Vi=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(t){return Ae(qo,t,b.instance())},e.prototype.getMeterProvider=function(){return ee(qo)||yi},e.prototype.getMeter=function(t,r,n){return this.getMeterProvider().getMeter(t,r,n)},e.prototype.disable=function(){fe(qo,b.instance())},e}()});var jo,Bi=c(()=>{wi();jo=Vi.getInstance()});var Gi,Hi=c(()=>{Gi=function(){function e(){}return e.prototype.inject=function(t,r){},e.prototype.extract=function(t,r){return t},e.prototype.fields=function(){return[]},e}()});function Xo(e){return e.getValue(Ko)||void 0}function Yi(){return Xo(ge.getInstance().active())}function Fi(e,t){return e.setValue(Ko,t)}function $i(e){return e.deleteValue(Ko)}var Ko,ki=c(()=>{Ht();Gt();Ko=re("OpenTelemetry Baggage Key")});var Wo,El,qi,ji=c(()=>{He();Hi();bo();ki();Mo();Ye();Wo="propagation",El=new Gi,qi=function(){function e(){this.createBaggage=ai,this.getBaggage=Xo,this.getActiveBaggage=Yi,this.setBaggage=Fi,this.deleteBaggage=$i}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(t){return Ae(Wo,t,b.instance())},e.prototype.inject=function(t,r,n){return n===void 0&&(n=Kr),this._getGlobalPropagator().inject(t,r,n)},e.prototype.extract=function(t,r,n){return n===void 0&&(n=jr),this._getGlobalPropagator().extract(t,r,n)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){fe(Wo,b.instance())},e.prototype._getGlobalPropagator=function(){return ee(Wo)||El},e}()});var W,Ki=c(()=>{ji();W=qi.getInstance()});var zo,Xi,Wi=c(()=>{He();$o();Qr();Go();Ye();zo="trace",Xi=function(){function e(){this._proxyTracerProvider=new kt,this.wrapSpanContext=Ai,this.isSpanContextValid=V,this.deleteSpan=Si,this.getSpan=zr,this.getActiveSpan=ui,this.getSpanContext=Jr,this.setSpan=$t,this.setSpanContext=li}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(t){var r=Ae(zo,this._proxyTracerProvider,b.instance());return r&&this._proxyTracerProvider.setDelegate(t),r},e.prototype.getTracerProvider=function(){return ee(zo)||this._proxyTracerProvider},e.prototype.getTracer=function(t,r){return this.getTracerProvider().getTracer(t,r)},e.prototype.disable=function(){fe(zo,b.instance()),this._proxyTracerProvider=new kt},e}()});var L,zi=c(()=>{Wi();L=Xi.getInstance()});var rt={};te(rt,{DiagConsoleLogger:()=>ii,DiagLogLevel:()=>g,INVALID_SPANID:()=>Yt,INVALID_SPAN_CONTEXT:()=>et,INVALID_TRACEID:()=>Ft,ProxyTracer:()=>en,ProxyTracerProvider:()=>kt,ROOT_CONTEXT:()=>$r,SamplingDecision:()=>X,SpanKind:()=>tt,SpanStatusCode:()=>Ie,TraceFlags:()=>O,ValueType:()=>qr,baggageEntryMetadataFromString:()=>Fr,context:()=>y,createContextKey:()=>re,createNoopMeter:()=>_i,createTraceState:()=>Mi,default:()=>_l,defaultTextMapGetter:()=>jr,defaultTextMapSetter:()=>Kr,diag:()=>S,isSpanContextValid:()=>V,isValidSpanId:()=>$e,isValidTraceId:()=>pe,metrics:()=>jo,propagation:()=>W,trace:()=>L});var _l,d=c(()=>{Mo();Gt();Ei();Yr();yo();ci();bo();Fo();$o();di();hi();mi();wo();xi();Qr();Xr();Di();Ui();Bi();Ki();zi();_l={context:y,diag:S,metrics:jo,propagation:W,trace:L}});function nt(e){return e.setValue(Jo,!0)}function Ji(e){return e.deleteValue(Jo)}function w(e){return e.getValue(Jo)===!0}var Jo,qt=c(()=>{d();Jo=re("OpenTelemetry SDK Context Key SUPPRESS_TRACING")});var Qi,tn,ot,rn,Qo=c(()=>{Qi="=",tn=";",ot=",",rn="baggage"});function nn(e){return e.reduce((t,r)=>{let n=`${t}${t!==""?ot:""}${r}`;return n.length>8192?t:n},"")}function on(e){return e.getAllEntries().map(([t,r])=>{let n=`${encodeURIComponent(t)}=${encodeURIComponent(r.value)}`;return r.metadata!==void 0&&(n+=tn+r.metadata.toString()),n})}function jt(e){let t=e.split(tn);if(t.length<=0)return;let r=t.shift();if(!r)return;let n=r.indexOf(Qi);if(n<=0)return;let o=decodeURIComponent(r.substring(0,n).trim()),s=decodeURIComponent(r.substring(n+1).trim()),a;return t.length>0&&(a=Fr(t.join(tn))),{key:o,value:s,metadata:a}}function Zi(e){return typeof e!="string"||e.length===0?{}:e.split(ot).map(t=>jt(t)).filter(t=>t!==void 0&&t.value.length>0).reduce((t,r)=>(t[r.key]=r.value,t),{})}var Zo=c(()=>{d();Qo()});var st,eE=c(()=>{d();qt();Qo();Zo();st=class{inject(t,r,n){let o=W.getBaggage(t);if(!o||w(t))return;let s=on(o).filter(i=>i.length<=4096).slice(0,180),a=nn(s);a.length>0&&n.set(r,rn,a)}extract(t,r,n){let o=n.get(r,rn),s=Array.isArray(o)?o.join(ot):o;if(!s)return t;let a={};return s.length===0||(s.split(ot).forEach(E=>{let _=jt(E);if(_){let T={value:_.value};_.metadata&&(T.metadata=_.metadata),a[_.key]=T}}),Object.entries(a).length===0)?t:W.setBaggage(t,W.createBaggage(a))}fields(){return[rn]}}});var sn,tE=c(()=>{sn=class{constructor(t,r){this._monotonicClock=r,this._epochMillis=t.now(),this._performanceMillis=r.now()}now(){let t=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+t}}});function Me(e){let t={};if(typeof e!="object"||e==null)return t;for(let[r,n]of Object.entries(e)){if(!es(r)){S.warn(`Invalid attribute key: ${r}`);continue}if(!Kt(n)){S.warn(`Invalid attribute value set for key: ${r}`);continue}Array.isArray(n)?t[r]=n.slice():t[r]=n}return t}function es(e){return typeof e=="string"&&e.length>0}function Kt(e){return e==null?!0:Array.isArray(e)?ul(e):rE(e)}function ul(e){let t;for(let r of e)if(r!=null){if(!t){if(rE(r)){t=typeof r;continue}return!1}if(typeof r!==t)return!1}return!0}function rE(e){switch(typeof e){case"number":case"boolean":case"string":return!0}return!1}var nE=c(()=>{d()});function an(){return e=>{S.error(Sl(e))}}function Sl(e){return typeof e=="string"?e:JSON.stringify(ll(e))}function ll(e){let t={},r=e;for(;r!==null;)Object.getOwnPropertyNames(r).forEach(n=>{if(t[n])return;let o=r[n];o&&(t[n]=String(o))}),r=Object.getPrototypeOf(r);return t}var ts=c(()=>{d()});function sE(e){oE=e}function F(e){try{oE(e)}catch{}}var oE,rs=c(()=>{ts();oE=an()});var z,ns=c(()=>{(function(e){e.AlwaysOff="always_off",e.AlwaysOn="always_on",e.ParentBasedAlwaysOff="parentbased_always_off",e.ParentBasedAlwaysOn="parentbased_always_on",e.ParentBasedTraceIdRatio="parentbased_traceidratio",e.TraceIdRatio="traceidratio"})(z||(z={}))});function Rl(e){return fl.indexOf(e)>-1}function hl(e){return dl.indexOf(e)>-1}function Ol(e){return ml.indexOf(e)>-1}function Nl(e,t,r){if(typeof r[e]>"u")return;let n=String(r[e]);t[e]=n.toLowerCase()==="true"}function vl(e,t,r,n=-1/0,o=1/0){if(typeof r[e]<"u"){let s=Number(r[e]);isNaN(s)||(s<n?t[e]=n:s>o?t[e]=o:t[e]=s)}}function Pl(e,t,r,n=Al){let o=r[e];typeof o=="string"&&(t[e]=o.split(n).map(s=>s.trim()))}function gl(e,t,r){let n=r[e];if(typeof n=="string"){let o=Ll[n.toUpperCase()];o!=null&&(t[e]=o)}}function Wt(e){let t={};for(let r in Xt){let n=r;switch(n){case"OTEL_LOG_LEVEL":gl(n,t,e);break;default:if(Rl(n))Nl(n,t,e);else if(hl(n))vl(n,t,e);else if(Ol(n))Pl(n,t,e);else{let o=e[n];typeof o<"u"&&o!==null&&(t[n]=String(o))}}}return t}var Al,fl,dl,ml,ke,qe,os,ss,Xt,Ll,as=c(()=>{d();ns();Al=",",fl=["OTEL_SDK_DISABLED"];dl=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"];ml=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"];ke=1/0,qe=128,os=128,ss=128,Xt={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:g.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:ke,OTEL_ATTRIBUTE_COUNT_LIMIT:qe,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:ke,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:qe,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:ke,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:qe,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:os,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:ss,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:z.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]};Ll={ALL:g.ALL,VERBOSE:g.VERBOSE,DEBUG:g.DEBUG,INFO:g.INFO,WARN:g.WARN,ERROR:g.ERROR,NONE:g.NONE}});function J(){let e=Wt(process.env);return Object.assign({},Xt,e)}function at(){return Wt(process.env)}var aE=c(()=>{as()});var En,iE=c(()=>{En=typeof globalThis=="object"?globalThis:global});function EE(e){return e>=48&&e<=57?e-48:e>=97&&e<=102?e-87:e-55}function xe(e){let t=new Uint8Array(e.length/2),r=0;for(let n=0;n<e.length;n+=2){let o=EE(e.charCodeAt(n)),s=EE(e.charCodeAt(n+1));t[r++]=o<<4|s}return t}var is=c(()=>{});function _n(e){return Buffer.from(xe(e)).toString("base64")}var _E=c(()=>{is()});function cE(e){return function(){for(let r=0;r<e/4;r++)cn.writeUInt32BE(Math.random()*2**32>>>0,r*4);for(let r=0;r<e&&!(cn[r]>0);r++)r===e-1&&(cn[e-1]=1);return cn.toString("hex",0,e)}}var it,cn,TE=c(()=>{it=class{constructor(){this.generateTraceId=cE(16),this.generateSpanId=cE(8)}},cn=Buffer.allocUnsafe(16)});import{performance as Cl}from"perf_hooks";var ne,pE=c(()=>{ne=Cl});var Tn,Es=c(()=>{Tn="1.30.1"});var Il,Ml,xl,pn,un,uE,SE=c(()=>{Il="exception.type",Ml="exception.message",xl="exception.stacktrace",pn=Il,un=Ml,uE=xl});var lE=c(()=>{SE()});var Dl,Ul,yl,bl,Vl,wl,Bl,Gl,Hl,Yl,Fl,$l,kl,ql,jl,Kl,Xl,Wl,zl,AE,fE,RE,dE,hE,mE,OE,NE,vE,PE,LE,Et,zt,Sn,ln,gE,Jt,Qt,Zt,Jl,CE,IE=c(()=>{Dl="host.id",Ul="host.name",yl="host.arch",bl="os.type",Vl="os.version",wl="process.pid",Bl="process.executable.name",Gl="process.executable.path",Hl="process.command",Yl="process.command_args",Fl="process.owner",$l="process.runtime.name",kl="process.runtime.version",ql="process.runtime.description",jl="service.name",Kl="service.instance.id",Xl="telemetry.sdk.name",Wl="telemetry.sdk.language",zl="telemetry.sdk.version",AE=Dl,fE=Ul,RE=yl,dE=bl,hE=Vl,mE=wl,OE=Bl,NE=Gl,vE=Hl,PE=Yl,LE=Fl,Et=$l,zt=kl,Sn=ql,ln=jl,gE=Kl,Jt=Xl,Qt=Wl,Zt=zl,Jl="nodejs",CE=Jl});var ME=c(()=>{IE()});var xE=c(()=>{});var DE=c(()=>{});var Re=c(()=>{lE();ME();xE();DE()});var De,UE=c(()=>{Es();Re();De={[Jt]:"opentelemetry",[Et]:"node",[Qt]:CE,[Zt]:Tn}});function _t(e){e.unref()}var yE=c(()=>{});var bE=c(()=>{aE();iE();_E();TE();pE();UE();yE()});var _s=c(()=>{bE()});function ue(e){let t=e/1e3,r=Math.trunc(t),n=Math.round(e%1e3*Zl);return[r,n]}function ct(){let e=ne.timeOrigin;if(typeof e!="number"){let t=ne;e=t.timing&&t.timing.fetchStart}return e}function er(e){let t=ue(ct()),r=ue(typeof e=="number"?e:ne.now());return nr(t,r)}function wE(e){if(Tt(e))return e;if(typeof e=="number")return e<ct()?er(e):ue(e);if(e instanceof Date)return ue(e.getTime());throw TypeError("Invalid input type")}function fn(e,t){let r=t[0]-e[0],n=t[1]-e[1];return n<0&&(r-=1,n+=An),[r,n]}function BE(e){let t=VE,r=`${"0".repeat(t)}${e[1]}Z`,n=r.substring(r.length-t-1);return new Date(e[0]*1e3).toISOString().replace("000Z",n)}function Rn(e){return e[0]*An+e[1]}function GE(e){return e[0]*1e3+e[1]/1e6}function tr(e){return e[0]*1e6+e[1]/1e3}function Tt(e){return Array.isArray(e)&&e.length===2&&typeof e[0]=="number"&&typeof e[1]=="number"}function rr(e){return Tt(e)||typeof e=="number"||e instanceof Date}function nr(e,t){let r=[e[0]+t[0],e[1]+t[1]];return r[1]>=An&&(r[1]-=An,r[0]+=1),r}var VE,Ql,Zl,An,HE=c(()=>{_s();VE=9,Ql=6,Zl=Math.pow(10,Ql),An=Math.pow(10,VE)});var B,YE=c(()=>{(function(e){e[e.SUCCESS=0]="SUCCESS",e[e.FAILED=1]="FAILED"})(B||(B={}))});var pt,FE=c(()=>{d();pt=class{constructor(t={}){var r;this._propagators=(r=t.propagators)!==null&&r!==void 0?r:[],this._fields=Array.from(new Set(this._propagators.map(n=>typeof n.fields=="function"?n.fields():[]).reduce((n,o)=>n.concat(o),[])))}inject(t,r,n){for(let o of this._propagators)try{o.inject(t,r,n)}catch(s){S.warn(`Failed to inject with ${o.constructor.name}. Err: ${s.message}`)}}extract(t,r,n){return this._propagators.reduce((o,s)=>{try{return s.extract(o,r,n)}catch(a){S.warn(`Failed to extract with ${s.constructor.name}. Err: ${a.message}`)}return o},t)}fields(){return this._fields.slice()}}});function $E(e){return rA.test(e)}function kE(e){return nA.test(e)&&!oA.test(e)}var cs,eA,tA,rA,nA,oA,qE=c(()=>{cs="[_0-9a-z-*/]",eA=`[a-z]${cs}{0,255}`,tA=`[a-z0-9]${cs}{0,240}@[a-z]${cs}{0,13}`,rA=new RegExp(`^(?:${eA}|${tA})$`),nA=/^[ -~]{0,255}[!-~]$/,oA=/,|=/});var jE,sA,KE,XE,ut,Ts=c(()=>{qE();jE=32,sA=512,KE=",",XE="=",ut=class e{constructor(t){this._internalState=new Map,t&&this._parse(t)}set(t,r){let n=this._clone();return n._internalState.has(t)&&n._internalState.delete(t),n._internalState.set(t,r),n}unset(t){let r=this._clone();return r._internalState.delete(t),r}get(t){return this._internalState.get(t)}serialize(){return this._keys().reduce((t,r)=>(t.push(r+XE+this.get(r)),t),[]).join(KE)}_parse(t){t.length>sA||(this._internalState=t.split(KE).reverse().reduce((r,n)=>{let o=n.trim(),s=o.indexOf(XE);if(s!==-1){let a=o.slice(0,s),i=o.slice(s+1,n.length);$E(a)&&kE(i)&&r.set(a,i)}return r},new Map),this._internalState.size>jE&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,jE))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let t=new e;return t._internalState=new Map(this._internalState),t}}});function ps(e){let t=TA.exec(e);return!t||t[1]==="00"&&t[5]?null:{traceId:t[2],spanId:t[3],traceFlags:parseInt(t[4],16)}}var or,sr,aA,iA,EA,_A,cA,TA,St,WE=c(()=>{d();qt();Ts();or="traceparent",sr="tracestate",aA="00",iA="(?!ff)[\\da-f]{2}",EA="(?![0]{32})[\\da-f]{32}",_A="(?![0]{16})[\\da-f]{16}",cA="[\\da-f]{2}",TA=new RegExp(`^\\s?(${iA})-(${EA})-(${_A})-(${cA})(-.*)?\\s?$`);St=class{inject(t,r,n){let o=L.getSpanContext(t);if(!o||w(t)||!V(o))return;let s=`${aA}-${o.traceId}-${o.spanId}-0${Number(o.traceFlags||O.NONE).toString(16)}`;n.set(r,or,s),o.traceState&&n.set(r,sr,o.traceState.serialize())}extract(t,r,n){let o=n.get(r,or);if(!o)return t;let s=Array.isArray(o)?o[0]:o;if(typeof s!="string")return t;let a=ps(s);if(!a)return t;a.isRemote=!0;let i=n.get(r,sr);if(i){let E=Array.isArray(i)?i.join(","):i;a.traceState=new ut(typeof E=="string"?E:void 0)}return L.setSpanContext(t,a)}fields(){return[or,sr]}}});function zE(e,t){return e.setValue(us,t)}function JE(e){return e.deleteValue(us)}function QE(e){return e.getValue(us)}var us,dn,ZE=c(()=>{d();us=re("OpenTelemetry SDK Context Key RPC_METADATA");(function(e){e.HTTP="http"})(dn||(dn={}))});var je,Ss=c(()=>{d();je=class{shouldSample(){return{decision:X.NOT_RECORD}}toString(){return"AlwaysOffSampler"}}});var Ue,ls=c(()=>{d();Ue=class{shouldSample(){return{decision:X.RECORD_AND_SAMPLED}}toString(){return"AlwaysOnSampler"}}});var hn,e_=c(()=>{d();rs();Ss();ls();hn=class{constructor(t){var r,n,o,s;this._root=t.root,this._root||(F(new Error("ParentBasedSampler must have a root sampler configured")),this._root=new Ue),this._remoteParentSampled=(r=t.remoteParentSampled)!==null&&r!==void 0?r:new Ue,this._remoteParentNotSampled=(n=t.remoteParentNotSampled)!==null&&n!==void 0?n:new je,this._localParentSampled=(o=t.localParentSampled)!==null&&o!==void 0?o:new Ue,this._localParentNotSampled=(s=t.localParentNotSampled)!==null&&s!==void 0?s:new je}shouldSample(t,r,n,o,s,a){let i=L.getSpanContext(t);return!i||!V(i)?this._root.shouldSample(t,r,n,o,s,a):i.isRemote?i.traceFlags&O.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,s,a):this._remoteParentNotSampled.shouldSample(t,r,n,o,s,a):i.traceFlags&O.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,s,a):this._localParentNotSampled.shouldSample(t,r,n,o,s,a)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}}});var mn,t_=c(()=>{d();mn=class{constructor(t=0){this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(this._ratio*4294967295)}shouldSample(t,r){return{decision:pe(r)&&this._accumulate(r)<this._upperBound?X.RECORD_AND_SAMPLED:X.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(t){return typeof t!="number"||isNaN(t)?0:t>=1?1:t<=0?0:t}_accumulate(t){let r=0;for(let n=0;n<t.length/8;n++){let o=n*8,s=parseInt(t.slice(o,o+8),16);r=(r^s)>>>0}return r}}});function RA(e,t){return function(r){return e(t(r))}}function As(e){if(!dA(e)||hA(e)!==pA)return!1;let t=fA(e);if(t===null)return!0;let r=o_.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&r_.call(r)===AA}function dA(e){return e!=null&&typeof e=="object"}function hA(e){return e==null?e===void 0?SA:uA:Ke&&Ke in Object(e)?mA(e):OA(e)}function mA(e){let t=o_.call(e,Ke),r=e[Ke],n=!1;try{e[Ke]=void 0,n=!0}catch{}let o=s_.call(e);return n&&(t?e[Ke]=r:delete e[Ke]),o}function OA(e){return s_.call(e)}var pA,uA,SA,lA,r_,AA,fA,n_,o_,Ke,s_,a_=c(()=>{pA="[object Object]",uA="[object Null]",SA="[object Undefined]",lA=Function.prototype,r_=lA.toString,AA=r_.call(Object),fA=RA(Object.getPrototypeOf,Object),n_=Object.prototype,o_=n_.hasOwnProperty,Ke=Symbol?Symbol.toStringTag:void 0,s_=n_.toString});function vn(...e){let t=e.shift(),r=new WeakMap;for(;e.length>0;)t=E_(t,e.shift(),0,r);return t}function fs(e){return Nn(e)?e.slice():e}function E_(e,t,r=0,n){let o;if(!(r>NA)){if(r++,On(e)||On(t)||__(t))o=fs(t);else if(Nn(e)){if(o=e.slice(),Nn(t))for(let s=0,a=t.length;s<a;s++)o.push(fs(t[s]));else if(ar(t)){let s=Object.keys(t);for(let a=0,i=s.length;a<i;a++){let E=s[a];o[E]=fs(t[E])}}}else if(ar(e))if(ar(t)){if(!vA(e,t))return t;o=Object.assign({},e);let s=Object.keys(t);for(let a=0,i=s.length;a<i;a++){let E=s[a],_=t[E];if(On(_))typeof _>"u"?delete o[E]:o[E]=_;else{let T=o[E],p=_;if(i_(e,E,n)||i_(t,E,n))delete o[E];else{if(ar(T)&&ar(p)){let f=n.get(T)||[],u=n.get(p)||[];f.push({obj:e,key:E}),u.push({obj:t,key:E}),n.set(T,f),n.set(p,u)}o[E]=E_(o[E],_,r,n)}}}}else o=t;return o}}function i_(e,t,r){let n=r.get(e[t])||[];for(let o=0,s=n.length;o<s;o++){let a=n[o];if(a.key===t&&a.obj===e)return!0}return!1}function Nn(e){return Array.isArray(e)}function __(e){return typeof e=="function"}function ar(e){return!On(e)&&!Nn(e)&&!__(e)&&typeof e=="object"}function On(e){return typeof e=="string"||typeof e=="number"||typeof e=="boolean"||typeof e>"u"||e instanceof Date||e instanceof RegExp||e===null}function vA(e,t){return!(!As(e)||!As(t))}var NA,c_=c(()=>{a_();NA=20});function T_(e,t){let r,n=new Promise(function(s,a){r=setTimeout(function(){a(new ir("Operation timed out."))},t)});return Promise.race([e,n]).then(o=>(clearTimeout(r),o),o=>{throw clearTimeout(r),o})}var ir,p_=c(()=>{ir=class e extends Error{constructor(t){super(t),Object.setPrototypeOf(this,e.prototype)}}});function Rs(e,t){return typeof t=="string"?e===t:!!e.match(t)}function u_(e,t){if(!t)return!1;for(let r of t)if(Rs(e,r))return!0;return!1}var S_=c(()=>{});function l_(e){return typeof e=="function"&&typeof e.__original=="function"&&typeof e.__unwrap=="function"&&e.__wrapped===!0}var A_=c(()=>{});var Pn,f_=c(()=>{Pn=class{constructor(){this._promise=new Promise((t,r)=>{this._resolve=t,this._reject=r})}get promise(){return this._promise}resolve(t){this._resolve(t)}reject(t){this._reject(t)}}});var de,R_=c(()=>{f_();de=class{constructor(t,r){this._callback=t,this._that=r,this._isCalled=!1,this._deferred=new Pn}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...t){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...t)).then(r=>this._deferred.resolve(r),r=>this._deferred.reject(r))}catch(r){this._deferred.reject(r)}}return this._deferred.promise}}});function d_(e,t){return new Promise(r=>{y.with(nt(y.active()),()=>{e.export(t,n=>{r(n)})})})}var h_=c(()=>{d();qt()});var m_={};te(m_,{AlwaysOffSampler:()=>je,AlwaysOnSampler:()=>Ue,AnchoredClock:()=>sn,BindOnceFuture:()=>de,CompositePropagator:()=>pt,DEFAULT_ATTRIBUTE_COUNT_LIMIT:()=>qe,DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT:()=>ke,DEFAULT_ENVIRONMENT:()=>Xt,DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:()=>os,DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:()=>ss,ExportResultCode:()=>B,ParentBasedSampler:()=>hn,RPCType:()=>dn,RandomIdGenerator:()=>it,SDK_INFO:()=>De,TRACE_PARENT_HEADER:()=>or,TRACE_STATE_HEADER:()=>sr,TimeoutError:()=>ir,TraceIdRatioBasedSampler:()=>mn,TraceState:()=>ut,TracesSamplerValues:()=>z,VERSION:()=>Tn,W3CBaggagePropagator:()=>st,W3CTraceContextPropagator:()=>St,_globalThis:()=>En,addHrTimes:()=>nr,baggageUtils:()=>PA,callWithTimeout:()=>T_,deleteRPCMetadata:()=>JE,getEnv:()=>J,getEnvWithoutDefaults:()=>at,getRPCMetadata:()=>QE,getTimeOrigin:()=>ct,globalErrorHandler:()=>F,hexToBase64:()=>_n,hexToBinary:()=>xe,hrTime:()=>er,hrTimeDuration:()=>fn,hrTimeToMicroseconds:()=>tr,hrTimeToMilliseconds:()=>GE,hrTimeToNanoseconds:()=>Rn,hrTimeToTimeStamp:()=>BE,internal:()=>ds,isAttributeKey:()=>es,isAttributeValue:()=>Kt,isTimeInput:()=>rr,isTimeInputHrTime:()=>Tt,isTracingSuppressed:()=>w,isUrlIgnored:()=>u_,isWrapped:()=>l_,loggingErrorHandler:()=>an,merge:()=>vn,millisToHrTime:()=>ue,otperformance:()=>ne,parseEnvironment:()=>Wt,parseTraceParent:()=>ps,sanitizeAttributes:()=>Me,setGlobalErrorHandler:()=>sE,setRPCMetadata:()=>zE,suppressTracing:()=>nt,timeInputToHrTime:()=>wE,unrefTimer:()=>_t,unsuppressTracing:()=>Ji,urlMatches:()=>Rs});var PA,ds,M=c(()=>{eE();tE();nE();rs();ts();HE();is();YE();Zo();_s();FE();WE();ZE();Ss();ls();e_();t_();qt();Ts();as();c_();ns();p_();S_();A_();R_();Es();h_();PA={getKeyPairs:on,serializeKeyPairs:nn,parseKeyPairsIntoRecord:Zi,parsePairKeyValue:jt},ds={_export:d_}});function lt(){return`unknown_service:${process.argv0}`}var x_=c(()=>{});var D_=c(()=>{x_()});var hs=c(()=>{D_()});var v,ve=c(()=>{d();Re();M();hs();v=class e{constructor(t,r){var n;this._attributes=t,this.asyncAttributesPending=r!=null,this._syncAttributes=(n=this._attributes)!==null&&n!==void 0?n:{},this._asyncAttributesPromise=r==null?void 0:r.then(o=>(this._attributes=Object.assign({},this._attributes,o),this.asyncAttributesPending=!1,o),o=>(S.debug("a resource's async attributes promise rejected: %s",o),this.asyncAttributesPending=!1,{}))}static empty(){return e.EMPTY}static default(){return new e({[ln]:lt(),[Qt]:De[Qt],[Jt]:De[Jt],[Zt]:De[Zt]})}get attributes(){var t;return this.asyncAttributesPending&&S.error("Accessing resource attributes before async attributes settled"),(t=this._attributes)!==null&&t!==void 0?t:{}}async waitForAsyncAttributes(){this.asyncAttributesPending&&await this._asyncAttributesPromise}merge(t){var r;if(!t)return this;let n=Object.assign(Object.assign({},this._syncAttributes),(r=t._syncAttributes)!==null&&r!==void 0?r:t.attributes);if(!this._asyncAttributesPromise&&!t._asyncAttributesPromise)return new e(n);let o=Promise.all([this._asyncAttributesPromise,t._asyncAttributesPromise]).then(([s,a])=>{var i;return Object.assign(Object.assign(Object.assign(Object.assign({},this._syncAttributes),s),(i=t._syncAttributes)!==null&&i!==void 0?i:t.attributes),a)});return new e(n,o)}};v.EMPTY=new v({})});var U_,y_,ms=c(()=>{U_=e=>{switch(e){case"arm":return"arm32";case"ppc":return"ppc32";case"x64":return"amd64";default:return e}},y_=e=>{switch(e){case"sunos":return"solaris";case"win32":return"windows";default:return e}}});import*as b_ from"child_process";import*as V_ from"util";var At,Ln=c(()=>{At=V_.promisify(b_.exec)});var w_={};te(w_,{getMachineId:()=>MA});async function MA(){try{let t=(await At('ioreg -rd1 -c "IOPlatformExpertDevice"')).stdout.split(`
`).find(n=>n.includes("IOPlatformUUID"));if(!t)return"";let r=t.split('" = "');if(r.length===2)return r[1].slice(0,-1)}catch(e){S.debug(`error reading machine id: ${e}`)}return""}var B_=c(()=>{Ln();d()});var G_={};te(G_,{getMachineId:()=>DA});import{promises as xA}from"fs";async function DA(){let e=["/etc/machine-id","/var/lib/dbus/machine-id"];for(let t of e)try{return(await xA.readFile(t,{encoding:"utf8"})).trim()}catch(r){S.debug(`error reading machine id: ${r}`)}return""}var H_=c(()=>{d()});var Y_={};te(Y_,{getMachineId:()=>yA});import{promises as UA}from"fs";async function yA(){try{return(await UA.readFile("/etc/hostid",{encoding:"utf8"})).trim()}catch(e){S.debug(`error reading machine id: ${e}`)}try{return(await At("kenv -q smbios.system.uuid")).stdout.trim()}catch(e){S.debug(`error reading machine id: ${e}`)}return""}var F_=c(()=>{Ln();d()});var $_={};te($_,{getMachineId:()=>bA});import*as gn from"process";async function bA(){let e="QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid",t="%windir%\\System32\\REG.exe";gn.arch==="ia32"&&"PROCESSOR_ARCHITEW6432"in gn.env&&(t="%windir%\\sysnative\\cmd.exe /c "+t);try{let n=(await At(`${t} ${e}`)).stdout.split("REG_SZ");if(n.length===2)return n[1].trim()}catch(r){S.debug(`error reading machine id: ${r}`)}return""}var k_=c(()=>{Ln();d()});var q_={};te(q_,{getMachineId:()=>VA});async function VA(){return S.debug("could not read machine-id: unsupported platform"),""}var j_=c(()=>{d()});import*as K_ from"process";var Xe,X_=c(()=>{switch(K_.platform){case"darwin":({getMachineId:Xe}=(B_(),Te(w_)));break;case"linux":({getMachineId:Xe}=(H_(),Te(G_)));break;case"freebsd":({getMachineId:Xe}=(F_(),Te(Y_)));break;case"win32":({getMachineId:Xe}=(k_(),Te($_)));break;default:({getMachineId:Xe}=(j_(),Te(q_)))}});import{arch as wA,hostname as BA}from"os";var Os,We,Ns=c(()=>{Re();ve();ms();X_();Os=class{detect(t){let r={[fE]:BA(),[RE]:U_(wA())};return new v(r,this._getAsyncAttributes())}_getAsyncAttributes(){return Xe().then(t=>{let r={};return t&&(r[AE]=t),r})}},We=new Os});var vs,_r,W_=c(()=>{Ns();vs=class{detect(t){return Promise.resolve(We.detect(t))}},_r=new vs});import{platform as GA,release as HA}from"os";var Ps,ze,Ls=c(()=>{Re();ve();ms();Ps=class{detect(t){let r={[dE]:y_(GA()),[hE]:HA()};return new v(r)}},ze=new Ps});var gs,cr,z_=c(()=>{Ls();gs=class{detect(t){return Promise.resolve(ze.detect(t))}},cr=new gs});import*as J_ from"os";var Cs,Je,Is=c(()=>{d();Re();ve();Cs=class{detect(t){let r={[mE]:process.pid,[OE]:process.title,[NE]:process.execPath,[PE]:[process.argv[0],...process.execArgv,...process.argv.slice(1)],[zt]:process.versions.node,[Et]:"nodejs",[Sn]:"Node.js"};process.argv.length>1&&(r[vE]=process.argv[1]);try{let n=J_.userInfo();r[LE]=n.username}catch(n){S.debug(`error obtaining process owner: ${n}`)}return new v(r)}},Je=new Cs});var Ms,Tr,Q_=c(()=>{Is();Ms=class{detect(t){return Promise.resolve(Je.detect(t))}},Tr=new Ms});import{randomUUID as YA}from"crypto";var xs,pr,Z_=c(()=>{Re();ve();xs=class{detect(t){let r={[gE]:YA()};return new v(r)}},pr=new xs});var ec=c(()=>{W_();Ns();z_();Ls();Q_();Is();Z_()});var tc=c(()=>{ec()});var Ds,ur,Us=c(()=>{Re();d();ve();Ds=class{detect(t){var r,n,o;if(!(typeof navigator<"u"&&((n=(r=global.process)===null||r===void 0?void 0:r.versions)===null||n===void 0?void 0:n.node)===void 0&&((o=global.Bun)===null||o===void 0?void 0:o.version)===void 0))return v.empty();let a={[Et]:"browser",[Sn]:"Web Browser",[zt]:navigator.userAgent};return this._getResourceAttributes(a,t)}_getResourceAttributes(t,r){return t[zt]===""?(S.debug("BrowserDetector failed: Unable to find required browser resources. "),v.empty()):new v(Object.assign({},t))}},ur=new Ds});var ys,bs,rc=c(()=>{Us();ys=class{detect(t){return Promise.resolve(ur.detect(t))}},bs=new ys});var Vs,Sr,ws=c(()=>{d();M();Re();ve();Vs=class{constructor(){this._MAX_LENGTH=255,this._COMMA_SEPARATOR=",",this._LABEL_KEY_VALUE_SPLITTER="=",this._ERROR_MESSAGE_INVALID_CHARS="should be a ASCII string with a length greater than 0 and not exceed "+this._MAX_LENGTH+" characters.",this._ERROR_MESSAGE_INVALID_VALUE="should be a ASCII string with a length not exceed "+this._MAX_LENGTH+" characters."}detect(t){let r={},n=J(),o=n.OTEL_RESOURCE_ATTRIBUTES,s=n.OTEL_SERVICE_NAME;if(o)try{let a=this._parseResourceAttributes(o);Object.assign(r,a)}catch(a){S.debug(`EnvDetector failed: ${a.message}`)}return s&&(r[ln]=s),new v(r)}_parseResourceAttributes(t){if(!t)return{};let r={},n=t.split(this._COMMA_SEPARATOR,-1);for(let o of n){let s=o.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(s.length!==2)continue;let[a,i]=s;if(a=a.trim(),i=i.trim().split(/^"|"$/).join(""),!this._isValidAndNotEmpty(a))throw new Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);if(!this._isValid(i))throw new Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);r[a]=decodeURIComponent(i)}return r}_isValid(t){return t.length<=this._MAX_LENGTH&&this._isBaggageOctetString(t)}_isBaggageOctetString(t){for(let r=0;r<t.length;r++){let n=t.charCodeAt(r);if(n<33||n===44||n===59||n===92||n>126)return!1}return!0}_isValidAndNotEmpty(t){return t.length>0&&this._isValid(t)}},Sr=new Vs});var Bs,Gs,nc=c(()=>{ws();Bs=class{detect(t){return Promise.resolve(Sr.detect(t))}},Gs=new Bs});var oc=c(()=>{tc();rc();nc();Us();ws()});var sc,ac=c(()=>{sc=e=>e!==null&&typeof e=="object"&&typeof e.then=="function"});var ic,Ec,_c,cc=c(()=>{ve();d();ac();ic=async(e={})=>{let t=await Promise.all((e.detectors||[]).map(async r=>{try{let n=await r.detect(e);return S.debug(`${r.constructor.name} found resource.`,n),n}catch(n){return S.debug(`${r.constructor.name} failed: ${n.message}`),v.empty()}}));return _c(t),t.reduce((r,n)=>r.merge(n),v.empty())},Ec=(e={})=>{var t;let r=((t=e.detectors)!==null&&t!==void 0?t:[]).map(o=>{try{let s=o.detect(e),a;if(sc(s)){let i=async()=>{var E;let _=await s;return await((E=_.waitForAsyncAttributes)===null||E===void 0?void 0:E.call(_)),_.attributes};a=new v({},i())}else a=s;return a.waitForAsyncAttributes?a.waitForAsyncAttributes().then(()=>S.debug(`${o.constructor.name} found resource.`,a)):S.debug(`${o.constructor.name} found resource.`,a),a}catch(s){return S.error(`${o.constructor.name} failed: ${s.message}`),v.empty()}}),n=r.reduce((o,s)=>o.merge(s),v.empty());return n.waitForAsyncAttributes&&n.waitForAsyncAttributes().then(()=>{_c(r)}),n},_c=e=>{e.forEach(t=>{if(Object.keys(t.attributes).length>0){let r=JSON.stringify(t.attributes,null,4);S.verbose(r)}})}});var Tc={};te(Tc,{Resource:()=>v,browserDetector:()=>bs,browserDetectorSync:()=>ur,defaultServiceName:()=>lt,detectResources:()=>ic,detectResourcesSync:()=>Ec,envDetector:()=>Gs,envDetectorSync:()=>Sr,hostDetector:()=>_r,hostDetectorSync:()=>We,osDetector:()=>cr,osDetectorSync:()=>ze,processDetector:()=>Tr,processDetectorSync:()=>Je,serviceInstanceIdDetectorSync:()=>pr});var Hs=c(()=>{ve();hs();oc();cc()});var Fs=R(In=>{"use strict";Object.defineProperty(In,"__esModule",{value:!0});In.AbstractAsyncHooksContextManager=void 0;var KA=Lo("events"),XA=["addListener","on","once","prependListener","prependOnceListener"],Ys=class{constructor(){this._kOtListeners=Symbol("OtListeners"),this._wrapped=!1}bind(t,r){return r instanceof KA.EventEmitter?this._bindEventEmitter(t,r):typeof r=="function"?this._bindFunction(t,r):r}_bindFunction(t,r){let n=this,o=function(...s){return n.with(t,()=>r.apply(this,s))};return Object.defineProperty(o,"length",{enumerable:!1,configurable:!0,writable:!1,value:r.length}),o}_bindEventEmitter(t,r){return this._getPatchMap(r)!==void 0||(this._createPatchMap(r),XA.forEach(o=>{r[o]!==void 0&&(r[o]=this._patchAddListener(r,r[o],t))}),typeof r.removeListener=="function"&&(r.removeListener=this._patchRemoveListener(r,r.removeListener)),typeof r.off=="function"&&(r.off=this._patchRemoveListener(r,r.off)),typeof r.removeAllListeners=="function"&&(r.removeAllListeners=this._patchRemoveAllListeners(r,r.removeAllListeners))),r}_patchRemoveListener(t,r){let n=this;return function(o,s){var a;let i=(a=n._getPatchMap(t))===null||a===void 0?void 0:a[o];if(i===void 0)return r.call(this,o,s);let E=i.get(s);return r.call(this,o,E||s)}}_patchRemoveAllListeners(t,r){let n=this;return function(o){let s=n._getPatchMap(t);return s!==void 0&&(arguments.length===0?n._createPatchMap(t):s[o]!==void 0&&delete s[o]),r.apply(this,arguments)}}_patchAddListener(t,r,n){let o=this;return function(s,a){if(o._wrapped)return r.call(this,s,a);let i=o._getPatchMap(t);i===void 0&&(i=o._createPatchMap(t));let E=i[s];E===void 0&&(E=new WeakMap,i[s]=E);let _=o.bind(n,a);E.set(a,_),o._wrapped=!0;try{return r.call(this,s,_)}finally{o._wrapped=!1}}}_createPatchMap(t){let r=Object.create(null);return t[this._kOtListeners]=r,r}_getPatchMap(t){return t[this._kOtListeners]}};In.AbstractAsyncHooksContextManager=Ys});var uc=R(Mn=>{"use strict";Object.defineProperty(Mn,"__esModule",{value:!0});Mn.AsyncHooksContextManager=void 0;var WA=(d(),Te(rt)),zA=Lo("async_hooks"),JA=Fs(),$s=class extends JA.AbstractAsyncHooksContextManager{constructor(){super(),this._contexts=new Map,this._stack=[],this._asyncHook=zA.createHook({init:this._init.bind(this),before:this._before.bind(this),after:this._after.bind(this),destroy:this._destroy.bind(this),promiseResolve:this._destroy.bind(this)})}active(){var t;return(t=this._stack[this._stack.length-1])!==null&&t!==void 0?t:WA.ROOT_CONTEXT}with(t,r,n,...o){this._enterContext(t);try{return r.call(n,...o)}finally{this._exitContext()}}enable(){return this._asyncHook.enable(),this}disable(){return this._asyncHook.disable(),this._contexts.clear(),this._stack=[],this}_init(t,r){if(r==="TIMERWRAP")return;let n=this._stack[this._stack.length-1];n!==void 0&&this._contexts.set(t,n)}_destroy(t){this._contexts.delete(t)}_before(t){let r=this._contexts.get(t);r!==void 0&&this._enterContext(r)}_after(){this._exitContext()}_enterContext(t){this._stack.push(t)}_exitContext(){this._stack.pop()}};Mn.AsyncHooksContextManager=$s});var Sc=R(xn=>{"use strict";Object.defineProperty(xn,"__esModule",{value:!0});xn.AsyncLocalStorageContextManager=void 0;var QA=(d(),Te(rt)),ZA=Lo("async_hooks"),ef=Fs(),ks=class extends ef.AbstractAsyncHooksContextManager{constructor(){super(),this._asyncLocalStorage=new ZA.AsyncLocalStorage}active(){var t;return(t=this._asyncLocalStorage.getStore())!==null&&t!==void 0?t:QA.ROOT_CONTEXT}with(t,r,n,...o){let s=n==null?r:r.bind(n);return this._asyncLocalStorage.run(t,s,...o)}enable(){return this}disable(){return this._asyncLocalStorage.disable(),this}};xn.AsyncLocalStorageContextManager=ks});var lc=R(ft=>{"use strict";Object.defineProperty(ft,"__esModule",{value:!0});ft.AsyncLocalStorageContextManager=ft.AsyncHooksContextManager=void 0;var tf=uc();Object.defineProperty(ft,"AsyncHooksContextManager",{enumerable:!0,get:function(){return tf.AsyncHooksContextManager}});var rf=Sc();Object.defineProperty(ft,"AsyncLocalStorageContextManager",{enumerable:!0,get:function(){return rf.AsyncLocalStorageContextManager}})});var Rt,qs=c(()=>{d();Rt=re("OpenTelemetry Context Key B3 Debug Flag")});var ye,dt,ht,mt,Dn,Ot,lr=c(()=>{ye="b3",dt="x-b3-traceid",ht="x-b3-spanid",mt="x-b3-sampled",Dn="x-b3-parentspanid",Ot="x-b3-flags"});function sf(e){return e===O.SAMPLED||e===O.NONE}function af(e){return Array.isArray(e)?e[0]:e}function yn(e,t,r){let n=t.get(e,r);return af(n)}function Ef(e,t){let r=yn(e,t,dt);return typeof r=="string"?r.padStart(32,"0"):""}function _f(e,t){let r=yn(e,t,ht);return typeof r=="string"?r:""}function Ac(e,t){return yn(e,t,Ot)==="1"?"1":void 0}function cf(e,t){let r=yn(e,t,mt);if(Ac(e,t)==="1"||nf.has(r))return O.SAMPLED;if(r===void 0||of.has(r))return O.NONE}var nf,of,Un,fc=c(()=>{d();M();qs();lr();nf=new Set([!0,"true","True","1",1]),of=new Set([!1,"false","False","0",0]);Un=class{inject(t,r,n){let o=L.getSpanContext(t);if(!o||!V(o)||w(t))return;let s=t.getValue(Rt);n.set(r,dt,o.traceId),n.set(r,ht,o.spanId),s==="1"?n.set(r,Ot,s):o.traceFlags!==void 0&&n.set(r,mt,(O.SAMPLED&o.traceFlags)===O.SAMPLED?"1":"0")}extract(t,r,n){let o=Ef(r,n),s=_f(r,n),a=cf(r,n),i=Ac(r,n);return pe(o)&&$e(s)&&sf(a)?(t=t.setValue(Rt,i),L.setSpanContext(t,{traceId:o,spanId:s,isRemote:!0,traceFlags:a})):t}fields(){return[dt,ht,Ot,mt,Dn]}}});function lf(e){return e.length===32?e:`${pf}${e}`}function Af(e){return e&&uf.has(e)?O.SAMPLED:O.NONE}var Tf,pf,uf,Sf,bn,Rc=c(()=>{d();M();qs();lr();Tf=/((?:[0-9a-f]{16}){1,2})-([0-9a-f]{16})(?:-([01d](?![0-9a-f])))?(?:-([0-9a-f]{16}))?/,pf="0".repeat(16),uf=new Set(["d","1"]),Sf="d";bn=class{inject(t,r,n){let o=L.getSpanContext(t);if(!o||!V(o)||w(t))return;let s=t.getValue(Rt)||o.traceFlags&1,a=`${o.traceId}-${o.spanId}-${s}`;n.set(r,ye,a)}extract(t,r,n){let o=n.get(r,ye),s=Array.isArray(o)?o[0]:o;if(typeof s!="string")return t;let a=s.match(Tf);if(!a)return t;let[,i,E,_]=a,T=lf(i);if(!pe(T)||!$e(E))return t;let p=Af(_);return _===Sf&&(t=t.setValue(Rt,_)),L.setSpanContext(t,{traceId:T,spanId:E,isRemote:!0,traceFlags:p})}fields(){return[ye]}}});var Nt,js=c(()=>{(function(e){e[e.SINGLE_HEADER=0]="SINGLE_HEADER",e[e.MULTI_HEADER=1]="MULTI_HEADER"})(Nt||(Nt={}))});var Vn,dc=c(()=>{M();fc();Rc();lr();js();Vn=class{constructor(t={}){this._b3MultiPropagator=new Un,this._b3SinglePropagator=new bn,t.injectEncoding===Nt.MULTI_HEADER?(this._inject=this._b3MultiPropagator.inject,this._fields=this._b3MultiPropagator.fields()):(this._inject=this._b3SinglePropagator.inject,this._fields=this._b3SinglePropagator.fields())}inject(t,r,n){w(t)||this._inject(t,r,n)}extract(t,r,n){let o=n.get(r,ye);return(Array.isArray(o)?o[0]:o)?this._b3SinglePropagator.extract(t,r,n):this._b3MultiPropagator.extract(t,r,n)}fields(){return this._fields}}});var hc={};te(hc,{B3InjectEncoding:()=>Nt,B3Propagator:()=>Vn,B3_CONTEXT_HEADER:()=>ye,X_B3_FLAGS:()=>Ot,X_B3_PARENT_SPAN_ID:()=>Dn,X_B3_SAMPLED:()=>mt,X_B3_SPAN_ID:()=>ht,X_B3_TRACE_ID:()=>dt});var mc=c(()=>{dc();lr();js()});var Oc,Nc=c(()=>{Oc="exception"});var vt,Ks=c(()=>{d();M();Re();Nc();vt=class{constructor(t,r,n,o,s,a,i=[],E,_,T){this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:Ie.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=n,this._spanContext=o,this.parentSpanId=a,this.kind=s,this.links=i;let p=Date.now();this._performanceStartTime=ne.now(),this._performanceOffset=p-(this._performanceStartTime+ct()),this._startTimeProvided=E!=null,this.startTime=this._getTime(E??p),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,T!=null&&this.setAttributes(T),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,r)}spanContext(){return this._spanContext}setAttribute(t,r){return r==null||this._isSpanEnded()?this:t.length===0?(S.warn(`Invalid attribute key: ${t}`),this):Kt(r)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?(this._droppedAttributesCount++,this):(this.attributes[t]=this._truncateToSize(r),this):(S.warn(`Invalid attribute value set for key: ${t}`),this)}setAttributes(t){for(let[r,n]of Object.entries(t))this.setAttribute(r,n);return this}addEvent(t,r,n){if(this._isSpanEnded())return this;if(this._spanLimits.eventCountLimit===0)return S.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(this._droppedEventsCount===0&&S.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),rr(r)&&(rr(n)||(n=r),r=void 0);let o=Me(r);return this.events.push({name:t,attributes:o,time:this._getTime(n),droppedAttributesCount:0}),this}addLink(t){return this.links.push(t),this}addLinks(t){return this.links.push(...t),this}setStatus(t){return this._isSpanEnded()?this:(this.status=Object.assign({},t),this.status.message!=null&&typeof t.message!="string"&&(S.warn(`Dropping invalid status.message of type '${typeof t.message}', expected 'string'`),delete this.status.message),this)}updateName(t){return this._isSpanEnded()?this:(this.name=t,this)}end(t){if(this._isSpanEnded()){S.error(`${this.name} ${this._spanContext.traceId}-${this._spanContext.spanId} - You can only call end() on a span once.`);return}this._ended=!0,this.endTime=this._getTime(t),this._duration=fn(this.startTime,this.endTime),this._duration[0]<0&&(S.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&S.warn(`Dropped ${this._droppedEventsCount} events because eventCountLimit reached`),this._spanProcessor.onEnd(this)}_getTime(t){if(typeof t=="number"&&t<=ne.now())return er(t+this._performanceOffset);if(typeof t=="number")return ue(t);if(t instanceof Date)return ue(t.getTime());if(Tt(t))return t;if(this._startTimeProvided)return ue(Date.now());let r=ne.now()-this._performanceStartTime;return nr(this.startTime,ue(r))}isRecording(){return this._ended===!1}recordException(t,r){let n={};typeof t=="string"?n[un]=t:t&&(t.code?n[pn]=t.code.toString():t.name&&(n[pn]=t.name),t.message&&(n[un]=t.message),t.stack&&(n[uE]=t.stack)),n[pn]||n[un]?this.addEvent(Oc,n,r):S.warn(`Failed to record an exception ${t}`)}get duration(){return this._duration}get ended(){return this._ended}get droppedAttributesCount(){return this._droppedAttributesCount}get droppedEventsCount(){return this._droppedEventsCount}get droppedLinksCount(){return this._droppedLinksCount}_isSpanEnded(){return this._ended&&S.warn(`Can not execute the operation on ended Span {traceId: ${this._spanContext.traceId}, spanId: ${this._spanContext.spanId}}`),this._ended}_truncateToLimitUtil(t,r){return t.length<=r?t:t.substring(0,r)}_truncateToSize(t){let r=this._attributeValueLengthLimit;return r<=0?(S.warn(`Attribute value limit must be positive, got ${r}`),t):typeof t=="string"?this._truncateToLimitUtil(t,r):Array.isArray(t)?t.map(n=>typeof n=="string"?this._truncateToLimitUtil(n,r):n):t}}});var Se,Ar=c(()=>{(function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(Se||(Se={}))});var he,wn=c(()=>{Ar();he=class{shouldSample(){return{decision:Se.NOT_RECORD}}toString(){return"AlwaysOffSampler"}}});var oe,Bn=c(()=>{Ar();oe=class{shouldSample(){return{decision:Se.RECORD_AND_SAMPLED}}toString(){return"AlwaysOnSampler"}}});var be,Xs=c(()=>{d();M();wn();Bn();be=class{constructor(t){var r,n,o,s;this._root=t.root,this._root||(F(new Error("ParentBasedSampler must have a root sampler configured")),this._root=new oe),this._remoteParentSampled=(r=t.remoteParentSampled)!==null&&r!==void 0?r:new oe,this._remoteParentNotSampled=(n=t.remoteParentNotSampled)!==null&&n!==void 0?n:new he,this._localParentSampled=(o=t.localParentSampled)!==null&&o!==void 0?o:new oe,this._localParentNotSampled=(s=t.localParentNotSampled)!==null&&s!==void 0?s:new he}shouldSample(t,r,n,o,s,a){let i=L.getSpanContext(t);return!i||!V(i)?this._root.shouldSample(t,r,n,o,s,a):i.isRemote?i.traceFlags&O.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,s,a):this._remoteParentNotSampled.shouldSample(t,r,n,o,s,a):i.traceFlags&O.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,s,a):this._localParentNotSampled.shouldSample(t,r,n,o,s,a)}toString(){return`ParentBased{root=${this._root.toString()}, remoteParentSampled=${this._remoteParentSampled.toString()}, remoteParentNotSampled=${this._remoteParentNotSampled.toString()}, localParentSampled=${this._localParentSampled.toString()}, localParentNotSampled=${this._localParentNotSampled.toString()}}`}}});var Qe,Ws=c(()=>{d();Ar();Qe=class{constructor(t=0){this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(this._ratio*4294967295)}shouldSample(t,r){return{decision:pe(r)&&this._accumulate(r)<this._upperBound?Se.RECORD_AND_SAMPLED:Se.NOT_RECORD}}toString(){return`TraceIdRatioBased{${this._ratio}}`}_normalize(t){return typeof t!="number"||isNaN(t)?0:t>=1?1:t<=0?0:t}_accumulate(t){let r=0;for(let n=0;n<t.length/8;n++){let o=n*8,s=parseInt(t.slice(o,o+8),16);r=(r^s)>>>0}return r}}});function Gn(){let e=J();return{sampler:zs(e),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:e.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:e.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:e.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:e.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:e.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:e.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function zs(e=J()){switch(e.OTEL_TRACES_SAMPLER){case z.AlwaysOn:return new oe;case z.AlwaysOff:return new he;case z.ParentBasedAlwaysOn:return new be({root:new oe});case z.ParentBasedAlwaysOff:return new be({root:new he});case z.TraceIdRatio:return new Qe(vc(e));case z.ParentBasedTraceIdRatio:return new be({root:new Qe(vc(e))});default:return S.error(`OTEL_TRACES_SAMPLER value "${e.OTEL_TRACES_SAMPLER} invalid, defaulting to ${ff}".`),new oe}}function vc(e){if(e.OTEL_TRACES_SAMPLER_ARG===void 0||e.OTEL_TRACES_SAMPLER_ARG==="")return S.error(`OTEL_TRACES_SAMPLER_ARG is blank, defaulting to ${Pt}.`),Pt;let t=Number(e.OTEL_TRACES_SAMPLER_ARG);return isNaN(t)?(S.error(`OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is invalid, defaulting to ${Pt}.`),Pt):t<0||t>1?(S.error(`OTEL_TRACES_SAMPLER_ARG=${e.OTEL_TRACES_SAMPLER_ARG} was given, but it is out of range ([0..1]), defaulting to ${Pt}.`),Pt):t}var ff,Pt,Js=c(()=>{d();M();wn();Bn();Xs();Ws();ff=z.AlwaysOn,Pt=1});function Pc(e){let t={sampler:zs()},r=Gn(),n=Object.assign({},r,t,e);return n.generalLimits=Object.assign({},r.generalLimits,e.generalLimits||{}),n.spanLimits=Object.assign({},r.spanLimits,e.spanLimits||{}),n}function Lc(e){var t,r,n,o,s,a,i,E,_,T,p,f;let u=Object.assign({},e.spanLimits),N=at();return u.attributeCountLimit=(a=(s=(o=(r=(t=e.spanLimits)===null||t===void 0?void 0:t.attributeCountLimit)!==null&&r!==void 0?r:(n=e.generalLimits)===null||n===void 0?void 0:n.attributeCountLimit)!==null&&o!==void 0?o:N.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)!==null&&s!==void 0?s:N.OTEL_ATTRIBUTE_COUNT_LIMIT)!==null&&a!==void 0?a:qe,u.attributeValueLengthLimit=(f=(p=(T=(E=(i=e.spanLimits)===null||i===void 0?void 0:i.attributeValueLengthLimit)!==null&&E!==void 0?E:(_=e.generalLimits)===null||_===void 0?void 0:_.attributeValueLengthLimit)!==null&&T!==void 0?T:N.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&p!==void 0?p:N.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)!==null&&f!==void 0?f:ke,Object.assign({},e,{spanLimits:u})}var Qs=c(()=>{Js();M()});var Hn,gc=c(()=>{d();M();Hn=class{constructor(t,r){this._exporter=t,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;let n=J();this._maxExportBatchSize=typeof(r==null?void 0:r.maxExportBatchSize)=="number"?r.maxExportBatchSize:n.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize=typeof(r==null?void 0:r.maxQueueSize)=="number"?r.maxQueueSize:n.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis=typeof(r==null?void 0:r.scheduledDelayMillis)=="number"?r.scheduledDelayMillis:n.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis=typeof(r==null?void 0:r.exportTimeoutMillis)=="number"?r.exportTimeoutMillis:n.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new de(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(S.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}forceFlush(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()}onStart(t,r){}onEnd(t){this._shutdownOnce.isCalled||(t.spanContext().traceFlags&O.SAMPLED)!==0&&this._addToBuffer(t)}shutdown(){return this._shutdownOnce.call()}_shutdown(){return Promise.resolve().then(()=>this.onShutdown()).then(()=>this._flushAll()).then(()=>this._exporter.shutdown())}_addToBuffer(t){if(this._finishedSpans.length>=this._maxQueueSize){this._droppedSpansCount===0&&S.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(S.warn(`Dropped ${this._droppedSpansCount} spans because maxQueueSize reached`),this._droppedSpansCount=0),this._finishedSpans.push(t),this._maybeStartTimer()}_flushAll(){return new Promise((t,r)=>{let n=[],o=Math.ceil(this._finishedSpans.length/this._maxExportBatchSize);for(let s=0,a=o;s<a;s++)n.push(this._flushOneBatch());Promise.all(n).then(()=>{t()}).catch(r)})}_flushOneBatch(){return this._clearTimer(),this._finishedSpans.length===0?Promise.resolve():new Promise((t,r)=>{let n=setTimeout(()=>{r(new Error("Timeout"))},this._exportTimeoutMillis);y.with(nt(y.active()),()=>{let o;this._finishedSpans.length<=this._maxExportBatchSize?(o=this._finishedSpans,this._finishedSpans=[]):o=this._finishedSpans.splice(0,this._maxExportBatchSize);let s=()=>this._exporter.export(o,i=>{var E;clearTimeout(n),i.code===B.SUCCESS?t():r((E=i.error)!==null&&E!==void 0?E:new Error("BatchSpanProcessor: span export failed"))}),a=null;for(let i=0,E=o.length;i<E;i++){let _=o[i];_.resource.asyncAttributesPending&&_.resource.waitForAsyncAttributes&&(a??(a=[]),a.push(_.resource.waitForAsyncAttributes()))}a===null?s():Promise.all(a).then(s,i=>{F(i),r(i)})})})}_maybeStartTimer(){if(this._isExporting)return;let t=()=>{this._isExporting=!0,this._flushOneBatch().finally(()=>{this._isExporting=!1,this._finishedSpans.length>0&&(this._clearTimer(),this._maybeStartTimer())}).catch(r=>{this._isExporting=!1,F(r)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return t();this._timer===void 0&&(this._timer=setTimeout(()=>t(),this._scheduledDelayMillis),_t(this._timer))}_clearTimer(){this._timer!==void 0&&(clearTimeout(this._timer),this._timer=void 0)}}});var Ve,Cc=c(()=>{gc();Ve=class extends Hn{onShutdown(){}}});function Ic(e){return function(){for(let r=0;r<e/4;r++)Yn.writeUInt32BE(Math.random()*2**32>>>0,r*4);for(let r=0;r<e&&!(Yn[r]>0);r++)r===e-1&&(Yn[e-1]=1);return Yn.toString("hex",0,e)}}var we,Yn,Mc=c(()=>{we=class{constructor(){this.generateTraceId=Ic(16),this.generateSpanId=Ic(8)}},Yn=Buffer.allocUnsafe(16)});var xc=c(()=>{Cc();Mc()});var Fn=c(()=>{xc()});var Lt,Zs=c(()=>{d();M();Ks();Qs();Fn();Lt=class{constructor(t,r,n){this._tracerProvider=n;let o=Pc(r);this._sampler=o.sampler,this._generalLimits=o.generalLimits,this._spanLimits=o.spanLimits,this._idGenerator=r.idGenerator||new we,this.resource=n.resource,this.instrumentationLibrary=t}startSpan(t,r={},n=y.active()){var o,s,a;r.root&&(n=L.deleteSpan(n));let i=L.getSpan(n);if(w(n))return S.debug("Instrumentation suppressed, returning Noop Span"),L.wrapSpanContext(et);let E=i==null?void 0:i.spanContext(),_=this._idGenerator.generateSpanId(),T,p,f;!E||!L.isSpanContextValid(E)?T=this._idGenerator.generateTraceId():(T=E.traceId,p=E.traceState,f=E.spanId);let u=(o=r.kind)!==null&&o!==void 0?o:tt.INTERNAL,N=((s=r.links)!==null&&s!==void 0?s:[]).map(I=>({context:I.context,attributes:Me(I.attributes)})),x=Me(r.attributes),D=this._sampler.shouldSample(n,T,t,u,x,N);p=(a=D.traceState)!==null&&a!==void 0?a:p;let j=D.decision===X.RECORD_AND_SAMPLED?O.SAMPLED:O.NONE,_e={traceId:T,spanId:_,traceFlags:j,traceState:p};if(D.decision===X.NOT_RECORD)return S.debug("Recording is off, propagating context in a non-recording span"),L.wrapSpanContext(_e);let P=Me(Object.assign(x,D.attributes));return new vt(this,n,t,_e,u,f,N,r.startTime,void 0,P)}startActiveSpan(t,r,n,o){let s,a,i;if(arguments.length<2)return;arguments.length===2?i=r:arguments.length===3?(s=r,i=n):(s=r,a=n,i=o);let E=a??y.active(),_=this.startSpan(t,s,E),T=L.setSpan(E,_);return y.with(T,i,void 0,_)}getGeneralLimits(){return this._generalLimits}getSpanLimits(){return this._spanLimits}getActiveSpanProcessor(){return this._tracerProvider.getActiveSpanProcessor()}}});var fr,Dc=c(()=>{M();fr=class{constructor(t){this._spanProcessors=t}forceFlush(){let t=[];for(let r of this._spanProcessors)t.push(r.forceFlush());return new Promise(r=>{Promise.all(t).then(()=>{r()}).catch(n=>{F(n||new Error("MultiSpanProcessor: forceFlush failed")),r()})})}onStart(t,r){for(let n of this._spanProcessors)n.onStart(t,r)}onEnd(t){for(let r of this._spanProcessors)r.onEnd(t)}shutdown(){let t=[];for(let r of this._spanProcessors)t.push(r.shutdown());return new Promise((r,n)=>{Promise.all(t).then(()=>{r()},n)})}}});var gt,ea=c(()=>{gt=class{onStart(t,r){}onEnd(t){}shutdown(){return Promise.resolve()}forceFlush(){return Promise.resolve()}}});var Pe,Ct,Uc=c(()=>{d();M();Hs();Zs();Js();Dc();ea();Fn();Qs();(function(e){e[e.resolved=0]="resolved",e[e.timeout=1]="timeout",e[e.error=2]="error",e[e.unresolved=3]="unresolved"})(Pe||(Pe={}));Ct=class{constructor(t={}){var r,n;this._registeredSpanProcessors=[],this._tracers=new Map;let o=vn({},Gn(),Lc(t));if(this.resource=(r=o.resource)!==null&&r!==void 0?r:v.empty(),o.mergeResourceWithDefaults&&(this.resource=v.default().merge(this.resource)),this._config=Object.assign({},o,{resource:this.resource}),!((n=t.spanProcessors)===null||n===void 0)&&n.length)this._registeredSpanProcessors=[...t.spanProcessors],this.activeSpanProcessor=new fr(this._registeredSpanProcessors);else{let s=this._buildExporterFromEnv();if(s!==void 0){let a=new Ve(s);this.activeSpanProcessor=a}else this.activeSpanProcessor=new gt}}getTracer(t,r,n){let o=`${t}@${r||""}:${(n==null?void 0:n.schemaUrl)||""}`;return this._tracers.has(o)||this._tracers.set(o,new Lt({name:t,version:r,schemaUrl:n==null?void 0:n.schemaUrl},this._config,this)),this._tracers.get(o)}addSpanProcessor(t){this._registeredSpanProcessors.length===0&&this.activeSpanProcessor.shutdown().catch(r=>S.error("Error while trying to shutdown current span processor",r)),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new fr(this._registeredSpanProcessors)}getActiveSpanProcessor(){return this.activeSpanProcessor}register(t={}){L.setGlobalTracerProvider(this),t.propagator===void 0&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&y.setGlobalContextManager(t.contextManager),t.propagator&&W.setGlobalPropagator(t.propagator)}forceFlush(){let t=this._config.forceFlushTimeoutMillis,r=this._registeredSpanProcessors.map(n=>new Promise(o=>{let s,a=setTimeout(()=>{o(new Error(`Span processor did not completed within timeout period of ${t} ms`)),s=Pe.timeout},t);n.forceFlush().then(()=>{clearTimeout(a),s!==Pe.timeout&&(s=Pe.resolved,o(s))}).catch(i=>{clearTimeout(a),s=Pe.error,o(i)})}));return new Promise((n,o)=>{Promise.all(r).then(s=>{let a=s.filter(i=>i!==Pe.resolved);a.length>0?o(a):n()}).catch(s=>o([s]))})}shutdown(){return this.activeSpanProcessor.shutdown()}_getPropagator(t){var r;return(r=this.constructor._registeredPropagators.get(t))===null||r===void 0?void 0:r()}_getSpanExporter(t){var r;return(r=this.constructor._registeredExporters.get(t))===null||r===void 0?void 0:r()}_buildPropagatorFromEnv(){let t=Array.from(new Set(J().OTEL_PROPAGATORS)),n=t.map(o=>{let s=this._getPropagator(o);return s||S.warn(`Propagator "${o}" requested through environment variable is unavailable.`),s}).reduce((o,s)=>(s&&o.push(s),o),[]);if(n.length!==0)return t.length===1?n[0]:new pt({propagators:n})}_buildExporterFromEnv(){let t=J().OTEL_TRACES_EXPORTER;if(t==="none"||t==="")return;let r=this._getSpanExporter(t);return r||S.error(`Exporter "${t}" requested through environment variable is unavailable.`),r}};Ct._registeredPropagators=new Map([["tracecontext",()=>new St],["baggage",()=>new st]]);Ct._registeredExporters=new Map});var $n,yc=c(()=>{M();$n=class{export(t,r){return this._sendSpans(t,r)}shutdown(){return this._sendSpans([]),this.forceFlush()}forceFlush(){return Promise.resolve()}_exportInfo(t){var r;return{resource:{attributes:t.resource.attributes},instrumentationScope:t.instrumentationLibrary,traceId:t.spanContext().traceId,parentId:t.parentSpanId,traceState:(r=t.spanContext().traceState)===null||r===void 0?void 0:r.serialize(),name:t.name,id:t.spanContext().spanId,kind:t.kind,timestamp:tr(t.startTime),duration:tr(t.duration),attributes:t.attributes,status:t.status,events:t.events,links:t.links}}_sendSpans(t,r){for(let n of t)console.dir(this._exportInfo(n),{depth:3});if(r)return r({code:B.SUCCESS})}}});var kn,bc=c(()=>{M();kn=class{constructor(){this._finishedSpans=[],this._stopped=!1}export(t,r){if(this._stopped)return r({code:B.FAILED,error:new Error("Exporter has been stopped")});this._finishedSpans.push(...t),setTimeout(()=>r({code:B.SUCCESS}),0)}shutdown(){return this._stopped=!0,this._finishedSpans=[],this.forceFlush()}forceFlush(){return Promise.resolve()}reset(){this._finishedSpans=[]}getFinishedSpans(){return this._finishedSpans}}});var qn,Vc=c(()=>{d();M();qn=class{constructor(t){this._exporter=t,this._shutdownOnce=new de(this._shutdown,this),this._unresolvedExports=new Set}async forceFlush(){await Promise.all(Array.from(this._unresolvedExports)),this._exporter.forceFlush&&await this._exporter.forceFlush()}onStart(t,r){}onEnd(t){var r,n;if(this._shutdownOnce.isCalled||(t.spanContext().traceFlags&O.SAMPLED)===0)return;let o=()=>ds._export(this._exporter,[t]).then(s=>{var a;s.code!==B.SUCCESS&&F((a=s.error)!==null&&a!==void 0?a:new Error(`SimpleSpanProcessor: span export failed (status ${s})`))}).catch(s=>{F(s)});if(t.resource.asyncAttributesPending){let s=(n=(r=t.resource).waitForAsyncAttributes)===null||n===void 0?void 0:n.call(r).then(()=>(s!=null&&this._unresolvedExports.delete(s),o()),a=>F(a));s!=null&&this._unresolvedExports.add(s)}else o()}shutdown(){return this._shutdownOnce.call()}_shutdown(){return this._exporter.shutdown()}}});var ta={};te(ta,{AlwaysOffSampler:()=>he,AlwaysOnSampler:()=>oe,BasicTracerProvider:()=>Ct,BatchSpanProcessor:()=>Ve,ConsoleSpanExporter:()=>$n,ForceFlushState:()=>Pe,InMemorySpanExporter:()=>kn,NoopSpanProcessor:()=>gt,ParentBasedSampler:()=>be,RandomIdGenerator:()=>we,SamplingDecision:()=>Se,SimpleSpanProcessor:()=>qn,Span:()=>vt,TraceIdRatioBasedSampler:()=>Qe,Tracer:()=>Lt});var ra=c(()=>{Zs();Uc();Fn();yc();bc();Vc();ea();wn();Bn();Xs();Ws();Ar();Ks()});var Rr=R((cM,wc)=>{"use strict";var Rf="2.0.0",df=Number.MAX_SAFE_INTEGER||9007199254740991,hf=16,mf=250,Of=["major","premajor","minor","preminor","patch","prepatch","prerelease"];wc.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:hf,MAX_SAFE_BUILD_LENGTH:mf,MAX_SAFE_INTEGER:df,RELEASE_TYPES:Of,SEMVER_SPEC_VERSION:Rf,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var dr=R((TM,Bc)=>{"use strict";var Nf=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};Bc.exports=Nf});var It=R((me,Gc)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:na,MAX_SAFE_BUILD_LENGTH:vf,MAX_LENGTH:Pf}=Rr(),Lf=dr();me=Gc.exports={};var gf=me.re=[],Cf=me.safeRe=[],l=me.src=[],If=me.safeSrc=[],A=me.t={},Mf=0,oa="[a-zA-Z0-9-]",xf=[["\\s",1],["\\d",Pf],[oa,vf]],Df=e=>{for(let[t,r]of xf)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},h=(e,t,r)=>{let n=Df(t),o=Mf++;Lf(e,o,t),A[e]=o,l[o]=t,If[o]=n,gf[o]=new RegExp(t,r?"g":void 0),Cf[o]=new RegExp(n,r?"g":void 0)};h("NUMERICIDENTIFIER","0|[1-9]\\d*");h("NUMERICIDENTIFIERLOOSE","\\d+");h("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${oa}*`);h("MAINVERSION",`(${l[A.NUMERICIDENTIFIER]})\\.(${l[A.NUMERICIDENTIFIER]})\\.(${l[A.NUMERICIDENTIFIER]})`);h("MAINVERSIONLOOSE",`(${l[A.NUMERICIDENTIFIERLOOSE]})\\.(${l[A.NUMERICIDENTIFIERLOOSE]})\\.(${l[A.NUMERICIDENTIFIERLOOSE]})`);h("PRERELEASEIDENTIFIER",`(?:${l[A.NONNUMERICIDENTIFIER]}|${l[A.NUMERICIDENTIFIER]})`);h("PRERELEASEIDENTIFIERLOOSE",`(?:${l[A.NONNUMERICIDENTIFIER]}|${l[A.NUMERICIDENTIFIERLOOSE]})`);h("PRERELEASE",`(?:-(${l[A.PRERELEASEIDENTIFIER]}(?:\\.${l[A.PRERELEASEIDENTIFIER]})*))`);h("PRERELEASELOOSE",`(?:-?(${l[A.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[A.PRERELEASEIDENTIFIERLOOSE]})*))`);h("BUILDIDENTIFIER",`${oa}+`);h("BUILD",`(?:\\+(${l[A.BUILDIDENTIFIER]}(?:\\.${l[A.BUILDIDENTIFIER]})*))`);h("FULLPLAIN",`v?${l[A.MAINVERSION]}${l[A.PRERELEASE]}?${l[A.BUILD]}?`);h("FULL",`^${l[A.FULLPLAIN]}$`);h("LOOSEPLAIN",`[v=\\s]*${l[A.MAINVERSIONLOOSE]}${l[A.PRERELEASELOOSE]}?${l[A.BUILD]}?`);h("LOOSE",`^${l[A.LOOSEPLAIN]}$`);h("GTLT","((?:<|>)?=?)");h("XRANGEIDENTIFIERLOOSE",`${l[A.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);h("XRANGEIDENTIFIER",`${l[A.NUMERICIDENTIFIER]}|x|X|\\*`);h("XRANGEPLAIN",`[v=\\s]*(${l[A.XRANGEIDENTIFIER]})(?:\\.(${l[A.XRANGEIDENTIFIER]})(?:\\.(${l[A.XRANGEIDENTIFIER]})(?:${l[A.PRERELEASE]})?${l[A.BUILD]}?)?)?`);h("XRANGEPLAINLOOSE",`[v=\\s]*(${l[A.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[A.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[A.XRANGEIDENTIFIERLOOSE]})(?:${l[A.PRERELEASELOOSE]})?${l[A.BUILD]}?)?)?`);h("XRANGE",`^${l[A.GTLT]}\\s*${l[A.XRANGEPLAIN]}$`);h("XRANGELOOSE",`^${l[A.GTLT]}\\s*${l[A.XRANGEPLAINLOOSE]}$`);h("COERCEPLAIN",`(^|[^\\d])(\\d{1,${na}})(?:\\.(\\d{1,${na}}))?(?:\\.(\\d{1,${na}}))?`);h("COERCE",`${l[A.COERCEPLAIN]}(?:$|[^\\d])`);h("COERCEFULL",l[A.COERCEPLAIN]+`(?:${l[A.PRERELEASE]})?(?:${l[A.BUILD]})?(?:$|[^\\d])`);h("COERCERTL",l[A.COERCE],!0);h("COERCERTLFULL",l[A.COERCEFULL],!0);h("LONETILDE","(?:~>?)");h("TILDETRIM",`(\\s*)${l[A.LONETILDE]}\\s+`,!0);me.tildeTrimReplace="$1~";h("TILDE",`^${l[A.LONETILDE]}${l[A.XRANGEPLAIN]}$`);h("TILDELOOSE",`^${l[A.LONETILDE]}${l[A.XRANGEPLAINLOOSE]}$`);h("LONECARET","(?:\\^)");h("CARETTRIM",`(\\s*)${l[A.LONECARET]}\\s+`,!0);me.caretTrimReplace="$1^";h("CARET",`^${l[A.LONECARET]}${l[A.XRANGEPLAIN]}$`);h("CARETLOOSE",`^${l[A.LONECARET]}${l[A.XRANGEPLAINLOOSE]}$`);h("COMPARATORLOOSE",`^${l[A.GTLT]}\\s*(${l[A.LOOSEPLAIN]})$|^$`);h("COMPARATOR",`^${l[A.GTLT]}\\s*(${l[A.FULLPLAIN]})$|^$`);h("COMPARATORTRIM",`(\\s*)${l[A.GTLT]}\\s*(${l[A.LOOSEPLAIN]}|${l[A.XRANGEPLAIN]})`,!0);me.comparatorTrimReplace="$1$2$3";h("HYPHENRANGE",`^\\s*(${l[A.XRANGEPLAIN]})\\s+-\\s+(${l[A.XRANGEPLAIN]})\\s*$`);h("HYPHENRANGELOOSE",`^\\s*(${l[A.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[A.XRANGEPLAINLOOSE]})\\s*$`);h("STAR","(<|>)?=?\\s*\\*");h("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");h("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var jn=R((pM,Hc)=>{"use strict";var Uf=Object.freeze({loose:!0}),yf=Object.freeze({}),bf=e=>e?typeof e!="object"?Uf:e:yf;Hc.exports=bf});var sa=R((uM,$c)=>{"use strict";var Yc=/^[0-9]+$/,Fc=(e,t)=>{let r=Yc.test(e),n=Yc.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1},Vf=(e,t)=>Fc(t,e);$c.exports={compareIdentifiers:Fc,rcompareIdentifiers:Vf}});var G=R((SM,qc)=>{"use strict";var Kn=dr(),{MAX_LENGTH:kc,MAX_SAFE_INTEGER:Xn}=Rr(),{safeRe:Wn,t:zn}=It(),wf=jn(),{compareIdentifiers:Mt}=sa(),aa=class e{constructor(t,r){if(r=wf(r),t instanceof e){if(t.loose===!!r.loose&&t.includePrerelease===!!r.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>kc)throw new TypeError(`version is longer than ${kc} characters`);Kn("SemVer",t,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let n=t.trim().match(r.loose?Wn[zn.LOOSE]:Wn[zn.FULL]);if(!n)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>Xn||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Xn||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Xn||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(o=>{if(/^[0-9]+$/.test(o)){let s=+o;if(s>=0&&s<Xn)return s}return o}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(Kn("SemVer.compare",this.version,this.options,t),!(t instanceof e)){if(typeof t=="string"&&t===this.version)return 0;t=new e(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof e||(t=new e(t,this.options)),Mt(this.major,t.major)||Mt(this.minor,t.minor)||Mt(this.patch,t.patch)}comparePre(t){if(t instanceof e||(t=new e(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let r=0;do{let n=this.prerelease[r],o=t.prerelease[r];if(Kn("prerelease compare",r,n,o),n===void 0&&o===void 0)return 0;if(o===void 0)return 1;if(n===void 0)return-1;if(n===o)continue;return Mt(n,o)}while(++r)}compareBuild(t){t instanceof e||(t=new e(t,this.options));let r=0;do{let n=this.build[r],o=t.build[r];if(Kn("build compare",r,n,o),n===void 0&&o===void 0)return 0;if(o===void 0)return 1;if(n===void 0)return-1;if(n===o)continue;return Mt(n,o)}while(++r)}inc(t,r,n){if(t.startsWith("pre")){if(!r&&n===!1)throw new Error("invalid increment argument: identifier is empty");if(r){let o=`-${r}`.match(this.options.loose?Wn[zn.PRERELEASELOOSE]:Wn[zn.PRERELEASE]);if(!o||o[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,n),this.inc("pre",r,n);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,n),this.inc("pre",r,n);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let o=Number(n)?1:0;if(this.prerelease.length===0)this.prerelease=[o];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(r===this.prerelease.join(".")&&n===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(o)}}if(r){let s=[r,o];n===!1&&(s=[r]),Mt(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};qc.exports=aa});var Ze=R((lM,Kc)=>{"use strict";var jc=G(),Bf=(e,t,r=!1)=>{if(e instanceof jc)return e;try{return new jc(e,t)}catch(n){if(!r)return null;throw n}};Kc.exports=Bf});var Wc=R((AM,Xc)=>{"use strict";var Gf=Ze(),Hf=(e,t)=>{let r=Gf(e,t);return r?r.version:null};Xc.exports=Hf});var Jc=R((fM,zc)=>{"use strict";var Yf=Ze(),Ff=(e,t)=>{let r=Yf(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null};zc.exports=Ff});var eT=R((RM,Zc)=>{"use strict";var Qc=G(),$f=(e,t,r,n,o)=>{typeof r=="string"&&(o=n,n=r,r=void 0);try{return new Qc(e instanceof Qc?e.version:e,r).inc(t,n,o).version}catch{return null}};Zc.exports=$f});var nT=R((dM,rT)=>{"use strict";var tT=Ze(),kf=(e,t)=>{let r=tT(e,null,!0),n=tT(t,null,!0),o=r.compare(n);if(o===0)return null;let s=o>0,a=s?r:n,i=s?n:r,E=!!a.prerelease.length;if(!!i.prerelease.length&&!E){if(!i.patch&&!i.minor)return"major";if(i.compareMain(a)===0)return i.minor&&!i.patch?"minor":"patch"}let T=E?"pre":"";return r.major!==n.major?T+"major":r.minor!==n.minor?T+"minor":r.patch!==n.patch?T+"patch":"prerelease"};rT.exports=kf});var sT=R((hM,oT)=>{"use strict";var qf=G(),jf=(e,t)=>new qf(e,t).major;oT.exports=jf});var iT=R((mM,aT)=>{"use strict";var Kf=G(),Xf=(e,t)=>new Kf(e,t).minor;aT.exports=Xf});var _T=R((OM,ET)=>{"use strict";var Wf=G(),zf=(e,t)=>new Wf(e,t).patch;ET.exports=zf});var TT=R((NM,cT)=>{"use strict";var Jf=Ze(),Qf=(e,t)=>{let r=Jf(e,t);return r&&r.prerelease.length?r.prerelease:null};cT.exports=Qf});var se=R((vM,uT)=>{"use strict";var pT=G(),Zf=(e,t,r)=>new pT(e,r).compare(new pT(t,r));uT.exports=Zf});var lT=R((PM,ST)=>{"use strict";var eR=se(),tR=(e,t,r)=>eR(t,e,r);ST.exports=tR});var fT=R((LM,AT)=>{"use strict";var rR=se(),nR=(e,t)=>rR(e,t,!0);AT.exports=nR});var Jn=R((gM,dT)=>{"use strict";var RT=G(),oR=(e,t,r)=>{let n=new RT(e,r),o=new RT(t,r);return n.compare(o)||n.compareBuild(o)};dT.exports=oR});var mT=R((CM,hT)=>{"use strict";var sR=Jn(),aR=(e,t)=>e.sort((r,n)=>sR(r,n,t));hT.exports=aR});var NT=R((IM,OT)=>{"use strict";var iR=Jn(),ER=(e,t)=>e.sort((r,n)=>iR(n,r,t));OT.exports=ER});var hr=R((MM,vT)=>{"use strict";var _R=se(),cR=(e,t,r)=>_R(e,t,r)>0;vT.exports=cR});var Qn=R((xM,PT)=>{"use strict";var TR=se(),pR=(e,t,r)=>TR(e,t,r)<0;PT.exports=pR});var ia=R((DM,LT)=>{"use strict";var uR=se(),SR=(e,t,r)=>uR(e,t,r)===0;LT.exports=SR});var Ea=R((UM,gT)=>{"use strict";var lR=se(),AR=(e,t,r)=>lR(e,t,r)!==0;gT.exports=AR});var Zn=R((yM,CT)=>{"use strict";var fR=se(),RR=(e,t,r)=>fR(e,t,r)>=0;CT.exports=RR});var eo=R((bM,IT)=>{"use strict";var dR=se(),hR=(e,t,r)=>dR(e,t,r)<=0;IT.exports=hR});var _a=R((VM,MT)=>{"use strict";var mR=ia(),OR=Ea(),NR=hr(),vR=Zn(),PR=Qn(),LR=eo(),gR=(e,t,r,n)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e===r;case"!==":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e!==r;case"":case"=":case"==":return mR(e,r,n);case"!=":return OR(e,r,n);case">":return NR(e,r,n);case">=":return vR(e,r,n);case"<":return PR(e,r,n);case"<=":return LR(e,r,n);default:throw new TypeError(`Invalid operator: ${t}`)}};MT.exports=gR});var DT=R((wM,xT)=>{"use strict";var CR=G(),IR=Ze(),{safeRe:to,t:ro}=It(),MR=(e,t)=>{if(e instanceof CR)return e;if(typeof e=="number"&&(e=String(e)),typeof e!="string")return null;t=t||{};let r=null;if(!t.rtl)r=e.match(t.includePrerelease?to[ro.COERCEFULL]:to[ro.COERCE]);else{let E=t.includePrerelease?to[ro.COERCERTLFULL]:to[ro.COERCERTL],_;for(;(_=E.exec(e))&&(!r||r.index+r[0].length!==e.length);)(!r||_.index+_[0].length!==r.index+r[0].length)&&(r=_),E.lastIndex=_.index+_[1].length+_[2].length;E.lastIndex=-1}if(r===null)return null;let n=r[2],o=r[3]||"0",s=r[4]||"0",a=t.includePrerelease&&r[5]?`-${r[5]}`:"",i=t.includePrerelease&&r[6]?`+${r[6]}`:"";return IR(`${n}.${o}.${s}${a}${i}`,t)};xT.exports=MR});var yT=R((BM,UT)=>{"use strict";var ca=class{constructor(){this.max=1e3,this.map=new Map}get(t){let r=this.map.get(t);if(r!==void 0)return this.map.delete(t),this.map.set(t,r),r}delete(t){return this.map.delete(t)}set(t,r){if(!this.delete(t)&&r!==void 0){if(this.map.size>=this.max){let o=this.map.keys().next().value;this.delete(o)}this.map.set(t,r)}return this}};UT.exports=ca});var ae=R((GM,BT)=>{"use strict";var xR=/\s+/g,Ta=class e{constructor(t,r){if(r=UR(r),t instanceof e)return t.loose===!!r.loose&&t.includePrerelease===!!r.includePrerelease?t:new e(t.raw,r);if(t instanceof pa)return this.raw=t.value,this.set=[[t]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=t.trim().replace(xR," "),this.set=this.raw.split("||").map(n=>this.parseRange(n.trim())).filter(n=>n.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let n=this.set[0];if(this.set=this.set.filter(o=>!VT(o[0])),this.set.length===0)this.set=[n];else if(this.set.length>1){for(let o of this.set)if(o.length===1&&HR(o[0])){this.set=[o];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let t=0;t<this.set.length;t++){t>0&&(this.formatted+="||");let r=this.set[t];for(let n=0;n<r.length;n++)n>0&&(this.formatted+=" "),this.formatted+=r[n].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(t){let n=((this.options.includePrerelease&&BR)|(this.options.loose&&GR))+":"+t,o=bT.get(n);if(o)return o;let s=this.options.loose,a=s?Q[$.HYPHENRANGELOOSE]:Q[$.HYPHENRANGE];t=t.replace(a,zR(this.options.includePrerelease)),C("hyphen replace",t),t=t.replace(Q[$.COMPARATORTRIM],bR),C("comparator trim",t),t=t.replace(Q[$.TILDETRIM],VR),C("tilde trim",t),t=t.replace(Q[$.CARETTRIM],wR),C("caret trim",t);let i=t.split(" ").map(p=>YR(p,this.options)).join(" ").split(/\s+/).map(p=>WR(p,this.options));s&&(i=i.filter(p=>(C("loose invalid filter",p,this.options),!!p.match(Q[$.COMPARATORLOOSE])))),C("range list",i);let E=new Map,_=i.map(p=>new pa(p,this.options));for(let p of _){if(VT(p))return[p];E.set(p.value,p)}E.size>1&&E.has("")&&E.delete("");let T=[...E.values()];return bT.set(n,T),T}intersects(t,r){if(!(t instanceof e))throw new TypeError("a Range is required");return this.set.some(n=>wT(n,r)&&t.set.some(o=>wT(o,r)&&n.every(s=>o.every(a=>s.intersects(a,r)))))}test(t){if(!t)return!1;if(typeof t=="string")try{t=new yR(t,this.options)}catch{return!1}for(let r=0;r<this.set.length;r++)if(JR(this.set[r],t,this.options))return!0;return!1}};BT.exports=Ta;var DR=yT(),bT=new DR,UR=jn(),pa=mr(),C=dr(),yR=G(),{safeRe:Q,t:$,comparatorTrimReplace:bR,tildeTrimReplace:VR,caretTrimReplace:wR}=It(),{FLAG_INCLUDE_PRERELEASE:BR,FLAG_LOOSE:GR}=Rr(),VT=e=>e.value==="<0.0.0-0",HR=e=>e.value==="",wT=(e,t)=>{let r=!0,n=e.slice(),o=n.pop();for(;r&&n.length;)r=n.every(s=>o.intersects(s,t)),o=n.pop();return r},YR=(e,t)=>(C("comp",e,t),e=kR(e,t),C("caret",e),e=FR(e,t),C("tildes",e),e=jR(e,t),C("xrange",e),e=XR(e,t),C("stars",e),e),k=e=>!e||e.toLowerCase()==="x"||e==="*",FR=(e,t)=>e.trim().split(/\s+/).map(r=>$R(r,t)).join(" "),$R=(e,t)=>{let r=t.loose?Q[$.TILDELOOSE]:Q[$.TILDE];return e.replace(r,(n,o,s,a,i)=>{C("tilde",e,n,o,s,a,i);let E;return k(o)?E="":k(s)?E=`>=${o}.0.0 <${+o+1}.0.0-0`:k(a)?E=`>=${o}.${s}.0 <${o}.${+s+1}.0-0`:i?(C("replaceTilde pr",i),E=`>=${o}.${s}.${a}-${i} <${o}.${+s+1}.0-0`):E=`>=${o}.${s}.${a} <${o}.${+s+1}.0-0`,C("tilde return",E),E})},kR=(e,t)=>e.trim().split(/\s+/).map(r=>qR(r,t)).join(" "),qR=(e,t)=>{C("caret",e,t);let r=t.loose?Q[$.CARETLOOSE]:Q[$.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,(o,s,a,i,E)=>{C("caret",e,o,s,a,i,E);let _;return k(s)?_="":k(a)?_=`>=${s}.0.0${n} <${+s+1}.0.0-0`:k(i)?s==="0"?_=`>=${s}.${a}.0${n} <${s}.${+a+1}.0-0`:_=`>=${s}.${a}.0${n} <${+s+1}.0.0-0`:E?(C("replaceCaret pr",E),s==="0"?a==="0"?_=`>=${s}.${a}.${i}-${E} <${s}.${a}.${+i+1}-0`:_=`>=${s}.${a}.${i}-${E} <${s}.${+a+1}.0-0`:_=`>=${s}.${a}.${i}-${E} <${+s+1}.0.0-0`):(C("no pr"),s==="0"?a==="0"?_=`>=${s}.${a}.${i}${n} <${s}.${a}.${+i+1}-0`:_=`>=${s}.${a}.${i}${n} <${s}.${+a+1}.0-0`:_=`>=${s}.${a}.${i} <${+s+1}.0.0-0`),C("caret return",_),_})},jR=(e,t)=>(C("replaceXRanges",e,t),e.split(/\s+/).map(r=>KR(r,t)).join(" ")),KR=(e,t)=>{e=e.trim();let r=t.loose?Q[$.XRANGELOOSE]:Q[$.XRANGE];return e.replace(r,(n,o,s,a,i,E)=>{C("xRange",e,n,o,s,a,i,E);let _=k(s),T=_||k(a),p=T||k(i),f=p;return o==="="&&f&&(o=""),E=t.includePrerelease?"-0":"",_?o===">"||o==="<"?n="<0.0.0-0":n="*":o&&f?(T&&(a=0),i=0,o===">"?(o=">=",T?(s=+s+1,a=0,i=0):(a=+a+1,i=0)):o==="<="&&(o="<",T?s=+s+1:a=+a+1),o==="<"&&(E="-0"),n=`${o+s}.${a}.${i}${E}`):T?n=`>=${s}.0.0${E} <${+s+1}.0.0-0`:p&&(n=`>=${s}.${a}.0${E} <${s}.${+a+1}.0-0`),C("xRange return",n),n})},XR=(e,t)=>(C("replaceStars",e,t),e.trim().replace(Q[$.STAR],"")),WR=(e,t)=>(C("replaceGTE0",e,t),e.trim().replace(Q[t.includePrerelease?$.GTE0PRE:$.GTE0],"")),zR=e=>(t,r,n,o,s,a,i,E,_,T,p,f)=>(k(n)?r="":k(o)?r=`>=${n}.0.0${e?"-0":""}`:k(s)?r=`>=${n}.${o}.0${e?"-0":""}`:a?r=`>=${r}`:r=`>=${r}${e?"-0":""}`,k(_)?E="":k(T)?E=`<${+_+1}.0.0-0`:k(p)?E=`<${_}.${+T+1}.0-0`:f?E=`<=${_}.${T}.${p}-${f}`:e?E=`<${_}.${T}.${+p+1}-0`:E=`<=${E}`,`${r} ${E}`.trim()),JR=(e,t,r)=>{for(let n=0;n<e.length;n++)if(!e[n].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let n=0;n<e.length;n++)if(C(e[n].semver),e[n].semver!==pa.ANY&&e[n].semver.prerelease.length>0){let o=e[n].semver;if(o.major===t.major&&o.minor===t.minor&&o.patch===t.patch)return!0}return!1}return!0}});var mr=R((HM,kT)=>{"use strict";var Or=Symbol("SemVer ANY"),la=class e{static get ANY(){return Or}constructor(t,r){if(r=GT(r),t instanceof e){if(t.loose===!!r.loose)return t;t=t.value}t=t.trim().split(/\s+/).join(" "),Sa("comparator",t,r),this.options=r,this.loose=!!r.loose,this.parse(t),this.semver===Or?this.value="":this.value=this.operator+this.semver.version,Sa("comp",this)}parse(t){let r=this.options.loose?HT[YT.COMPARATORLOOSE]:HT[YT.COMPARATOR],n=t.match(r);if(!n)throw new TypeError(`Invalid comparator: ${t}`);this.operator=n[1]!==void 0?n[1]:"",this.operator==="="&&(this.operator=""),n[2]?this.semver=new FT(n[2],this.options.loose):this.semver=Or}toString(){return this.value}test(t){if(Sa("Comparator.test",t,this.options.loose),this.semver===Or||t===Or)return!0;if(typeof t=="string")try{t=new FT(t,this.options)}catch{return!1}return ua(t,this.operator,this.semver,this.options)}intersects(t,r){if(!(t instanceof e))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new $T(t.value,r).test(this.value):t.operator===""?t.value===""?!0:new $T(this.value,r).test(t.semver):(r=GT(r),r.includePrerelease&&(this.value==="<0.0.0-0"||t.value==="<0.0.0-0")||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||t.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&t.operator.startsWith(">")||this.operator.startsWith("<")&&t.operator.startsWith("<")||this.semver.version===t.semver.version&&this.operator.includes("=")&&t.operator.includes("=")||ua(this.semver,"<",t.semver,r)&&this.operator.startsWith(">")&&t.operator.startsWith("<")||ua(this.semver,">",t.semver,r)&&this.operator.startsWith("<")&&t.operator.startsWith(">")))}};kT.exports=la;var GT=jn(),{safeRe:HT,t:YT}=It(),ua=_a(),Sa=dr(),FT=G(),$T=ae()});var Nr=R((YM,qT)=>{"use strict";var QR=ae(),ZR=(e,t,r)=>{try{t=new QR(t,r)}catch{return!1}return t.test(e)};qT.exports=ZR});var KT=R((FM,jT)=>{"use strict";var ed=ae(),td=(e,t)=>new ed(e,t).set.map(r=>r.map(n=>n.value).join(" ").trim().split(" "));jT.exports=td});var WT=R(($M,XT)=>{"use strict";var rd=G(),nd=ae(),od=(e,t,r)=>{let n=null,o=null,s=null;try{s=new nd(t,r)}catch{return null}return e.forEach(a=>{s.test(a)&&(!n||o.compare(a)===-1)&&(n=a,o=new rd(n,r))}),n};XT.exports=od});var JT=R((kM,zT)=>{"use strict";var sd=G(),ad=ae(),id=(e,t,r)=>{let n=null,o=null,s=null;try{s=new ad(t,r)}catch{return null}return e.forEach(a=>{s.test(a)&&(!n||o.compare(a)===1)&&(n=a,o=new sd(n,r))}),n};zT.exports=id});var ep=R((qM,ZT)=>{"use strict";var Aa=G(),Ed=ae(),QT=hr(),_d=(e,t)=>{e=new Ed(e,t);let r=new Aa("0.0.0");if(e.test(r)||(r=new Aa("0.0.0-0"),e.test(r)))return r;r=null;for(let n=0;n<e.set.length;++n){let o=e.set[n],s=null;o.forEach(a=>{let i=new Aa(a.semver.version);switch(a.operator){case">":i.prerelease.length===0?i.patch++:i.prerelease.push(0),i.raw=i.format();case"":case">=":(!s||QT(i,s))&&(s=i);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${a.operator}`)}}),s&&(!r||QT(r,s))&&(r=s)}return r&&e.test(r)?r:null};ZT.exports=_d});var rp=R((jM,tp)=>{"use strict";var cd=ae(),Td=(e,t)=>{try{return new cd(e,t).range||"*"}catch{return null}};tp.exports=Td});var no=R((KM,ap)=>{"use strict";var pd=G(),sp=mr(),{ANY:ud}=sp,Sd=ae(),ld=Nr(),np=hr(),op=Qn(),Ad=eo(),fd=Zn(),Rd=(e,t,r,n)=>{e=new pd(e,n),t=new Sd(t,n);let o,s,a,i,E;switch(r){case">":o=np,s=Ad,a=op,i=">",E=">=";break;case"<":o=op,s=fd,a=np,i="<",E="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(ld(e,t,n))return!1;for(let _=0;_<t.set.length;++_){let T=t.set[_],p=null,f=null;if(T.forEach(u=>{u.semver===ud&&(u=new sp(">=0.0.0")),p=p||u,f=f||u,o(u.semver,p.semver,n)?p=u:a(u.semver,f.semver,n)&&(f=u)}),p.operator===i||p.operator===E||(!f.operator||f.operator===i)&&s(e,f.semver))return!1;if(f.operator===E&&a(e,f.semver))return!1}return!0};ap.exports=Rd});var Ep=R((XM,ip)=>{"use strict";var dd=no(),hd=(e,t,r)=>dd(e,t,">",r);ip.exports=hd});var cp=R((WM,_p)=>{"use strict";var md=no(),Od=(e,t,r)=>md(e,t,"<",r);_p.exports=Od});var up=R((zM,pp)=>{"use strict";var Tp=ae(),Nd=(e,t,r)=>(e=new Tp(e,r),t=new Tp(t,r),e.intersects(t,r));pp.exports=Nd});var lp=R((JM,Sp)=>{"use strict";var vd=Nr(),Pd=se();Sp.exports=(e,t,r)=>{let n=[],o=null,s=null,a=e.sort((T,p)=>Pd(T,p,r));for(let T of a)vd(T,t,r)?(s=T,o||(o=T)):(s&&n.push([o,s]),s=null,o=null);o&&n.push([o,null]);let i=[];for(let[T,p]of n)T===p?i.push(T):!p&&T===a[0]?i.push("*"):p?T===a[0]?i.push(`<=${p}`):i.push(`${T} - ${p}`):i.push(`>=${T}`);let E=i.join(" || "),_=typeof t.raw=="string"?t.raw:String(t);return E.length<_.length?E:t}});var mp=R((QM,hp)=>{"use strict";var Ap=ae(),Ra=mr(),{ANY:fa}=Ra,vr=Nr(),da=se(),Ld=(e,t,r={})=>{if(e===t)return!0;e=new Ap(e,r),t=new Ap(t,r);let n=!1;e:for(let o of e.set){for(let s of t.set){let a=Cd(o,s,r);if(n=n||a!==null,a)continue e}if(n)return!1}return!0},gd=[new Ra(">=0.0.0-0")],fp=[new Ra(">=0.0.0")],Cd=(e,t,r)=>{if(e===t)return!0;if(e.length===1&&e[0].semver===fa){if(t.length===1&&t[0].semver===fa)return!0;r.includePrerelease?e=gd:e=fp}if(t.length===1&&t[0].semver===fa){if(r.includePrerelease)return!0;t=fp}let n=new Set,o,s;for(let u of e)u.operator===">"||u.operator===">="?o=Rp(o,u,r):u.operator==="<"||u.operator==="<="?s=dp(s,u,r):n.add(u.semver);if(n.size>1)return null;let a;if(o&&s){if(a=da(o.semver,s.semver,r),a>0)return null;if(a===0&&(o.operator!==">="||s.operator!=="<="))return null}for(let u of n){if(o&&!vr(u,String(o),r)||s&&!vr(u,String(s),r))return null;for(let N of t)if(!vr(u,String(N),r))return!1;return!0}let i,E,_,T,p=s&&!r.includePrerelease&&s.semver.prerelease.length?s.semver:!1,f=o&&!r.includePrerelease&&o.semver.prerelease.length?o.semver:!1;p&&p.prerelease.length===1&&s.operator==="<"&&p.prerelease[0]===0&&(p=!1);for(let u of t){if(T=T||u.operator===">"||u.operator===">=",_=_||u.operator==="<"||u.operator==="<=",o){if(f&&u.semver.prerelease&&u.semver.prerelease.length&&u.semver.major===f.major&&u.semver.minor===f.minor&&u.semver.patch===f.patch&&(f=!1),u.operator===">"||u.operator===">="){if(i=Rp(o,u,r),i===u&&i!==o)return!1}else if(o.operator===">="&&!vr(o.semver,String(u),r))return!1}if(s){if(p&&u.semver.prerelease&&u.semver.prerelease.length&&u.semver.major===p.major&&u.semver.minor===p.minor&&u.semver.patch===p.patch&&(p=!1),u.operator==="<"||u.operator==="<="){if(E=dp(s,u,r),E===u&&E!==s)return!1}else if(s.operator==="<="&&!vr(s.semver,String(u),r))return!1}if(!u.operator&&(s||o)&&a!==0)return!1}return!(o&&_&&!s&&a!==0||s&&T&&!o&&a!==0||f||p)},Rp=(e,t,r)=>{if(!e)return t;let n=da(e.semver,t.semver,r);return n>0?e:n<0||t.operator===">"&&e.operator===">="?t:e},dp=(e,t,r)=>{if(!e)return t;let n=da(e.semver,t.semver,r);return n<0?e:n>0||t.operator==="<"&&e.operator==="<="?t:e};hp.exports=Ld});var Pp=R((ZM,vp)=>{"use strict";var ha=It(),Op=Rr(),Id=G(),Np=sa(),Md=Ze(),xd=Wc(),Dd=Jc(),Ud=eT(),yd=nT(),bd=sT(),Vd=iT(),wd=_T(),Bd=TT(),Gd=se(),Hd=lT(),Yd=fT(),Fd=Jn(),$d=mT(),kd=NT(),qd=hr(),jd=Qn(),Kd=ia(),Xd=Ea(),Wd=Zn(),zd=eo(),Jd=_a(),Qd=DT(),Zd=mr(),eh=ae(),th=Nr(),rh=KT(),nh=WT(),oh=JT(),sh=ep(),ah=rp(),ih=no(),Eh=Ep(),_h=cp(),ch=up(),Th=lp(),ph=mp();vp.exports={parse:Md,valid:xd,clean:Dd,inc:Ud,diff:yd,major:bd,minor:Vd,patch:wd,prerelease:Bd,compare:Gd,rcompare:Hd,compareLoose:Yd,compareBuild:Fd,sort:$d,rsort:kd,gt:qd,lt:jd,eq:Kd,neq:Xd,gte:Wd,lte:zd,cmp:Jd,coerce:Qd,Comparator:Zd,Range:eh,satisfies:th,toComparators:rh,maxSatisfying:nh,minSatisfying:oh,minVersion:sh,validRange:ah,outside:ih,gtr:Eh,ltr:_h,intersects:ch,simplifyRange:Th,subset:ph,SemVer:Id,re:ha.re,src:ha.src,tokens:ha.t,SEMVER_SPEC_VERSION:Op.SEMVER_SPEC_VERSION,RELEASE_TYPES:Op.RELEASE_TYPES,compareIdentifiers:Np.compareIdentifiers,rcompareIdentifiers:Np.rcompareIdentifiers}});function Sh(e){var t=decodeURIComponent(e).split(":");if(t.length!==4)return null;var r=gp(t,4),n=r[0],o=r[1],s=r[3],a=n.padStart(32,"0"),i=o.padStart(16,"0"),E=uh.test(s)?parseInt(s,16)&1:1;return{traceId:a,spanId:i,isRemote:!0,traceFlags:E}}var Lp,gp,ma,oo,Cp,uh,Ip=c(()=>{d();M();Lp=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},gp=function(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),o,s=[],a;try{for(;(t===void 0||t-- >0)&&!(o=n.next()).done;)s.push(o.value)}catch(i){a={error:i}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(a)throw a.error}}return s},ma="uber-trace-id",oo="uberctx",Cp=function(){function e(t){typeof t=="string"?(this._jaegerTraceHeader=t,this._jaegerBaggageHeaderPrefix=oo):(this._jaegerTraceHeader=(t==null?void 0:t.customTraceHeader)||ma,this._jaegerBaggageHeaderPrefix=(t==null?void 0:t.customBaggageHeaderPrefix)||oo)}return e.prototype.inject=function(t,r,n){var o,s,a=L.getSpanContext(t),i=W.getBaggage(t);if(a&&w(t)===!1){var E="0"+(a.traceFlags||O.NONE).toString(16);n.set(r,this._jaegerTraceHeader,a.traceId+":"+a.spanId+":0:"+E)}if(i)try{for(var _=Lp(i.getAllEntries()),T=_.next();!T.done;T=_.next()){var p=gp(T.value,2),f=p[0],u=p[1];n.set(r,this._jaegerBaggageHeaderPrefix+"-"+f,encodeURIComponent(u.value))}}catch(N){o={error:N}}finally{try{T&&!T.done&&(s=_.return)&&s.call(_)}finally{if(o)throw o.error}}},e.prototype.extract=function(t,r,n){var o,s,a=this,i,E=n.get(r,this._jaegerTraceHeader),_=Array.isArray(E)?E[0]:E,T=n.keys(r).filter(function(j){return j.startsWith(a._jaegerBaggageHeaderPrefix+"-")}).map(function(j){var _e=n.get(r,j);return{key:j.substring(a._jaegerBaggageHeaderPrefix.length+1),value:Array.isArray(_e)?_e[0]:_e}}),p=t;if(typeof _=="string"){var f=Sh(_);f&&(p=L.setSpanContext(p,f))}if(T.length===0)return p;var u=(i=W.getBaggage(t))!==null&&i!==void 0?i:W.createBaggage();try{for(var N=Lp(T),x=N.next();!x.done;x=N.next()){var D=x.value;D.value!==void 0&&(u=u.setEntry(D.key,{value:decodeURIComponent(D.value)}))}}catch(j){o={error:j}}finally{try{x&&!x.done&&(s=N.return)&&s.call(N)}finally{if(o)throw o.error}}return p=W.setBaggage(p,u),p},e.prototype.fields=function(){return[this._jaegerTraceHeader]},e}(),uh=/^[0-9a-f]{1,2}$/i});var Mp={};te(Mp,{JaegerPropagator:()=>Cp,UBER_BAGGAGE_HEADER_PREFIX:()=>oo,UBER_TRACE_ID_HEADER:()=>ma});var xp=c(()=>{Ip()});var yp=R(io=>{"use strict";Object.defineProperty(io,"__esModule",{value:!0});io.NodeTracerProvider=void 0;var Dp=lc(),so=(mc(),Te(hc)),Up=(ra(),Te(ta)),lh=Pp(),Ah=(xp(),Te(Mp)),ao=class extends Up.BasicTracerProvider{constructor(t={}){super(t)}register(t={}){if(t.contextManager===void 0){let r=lh.gte(process.version,"14.8.0")?Dp.AsyncLocalStorageContextManager:Dp.AsyncHooksContextManager;t.contextManager=new r,t.contextManager.enable()}super.register(t)}};io.NodeTracerProvider=ao;ao._registeredPropagators=new Map([...Up.BasicTracerProvider._registeredPropagators,["b3",()=>new so.B3Propagator({injectEncoding:so.B3InjectEncoding.SINGLE_HEADER})],["b3multi",()=>new so.B3Propagator({injectEncoding:so.B3InjectEncoding.MULTI_HEADER})],["jaeger",()=>new Ah.JaegerPropagator]])});var bp=R(m=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0});m.Tracer=m.TraceIdRatioBasedSampler=m.Span=m.SimpleSpanProcessor=m.SamplingDecision=m.RandomIdGenerator=m.ParentBasedSampler=m.NoopSpanProcessor=m.InMemorySpanExporter=m.ForceFlushState=m.ConsoleSpanExporter=m.BatchSpanProcessor=m.BasicTracerProvider=m.AlwaysOnSampler=m.AlwaysOffSampler=m.NodeTracerProvider=void 0;var fh=yp();Object.defineProperty(m,"NodeTracerProvider",{enumerable:!0,get:function(){return fh.NodeTracerProvider}});var q=(ra(),Te(ta));Object.defineProperty(m,"AlwaysOffSampler",{enumerable:!0,get:function(){return q.AlwaysOffSampler}});Object.defineProperty(m,"AlwaysOnSampler",{enumerable:!0,get:function(){return q.AlwaysOnSampler}});Object.defineProperty(m,"BasicTracerProvider",{enumerable:!0,get:function(){return q.BasicTracerProvider}});Object.defineProperty(m,"BatchSpanProcessor",{enumerable:!0,get:function(){return q.BatchSpanProcessor}});Object.defineProperty(m,"ConsoleSpanExporter",{enumerable:!0,get:function(){return q.ConsoleSpanExporter}});Object.defineProperty(m,"ForceFlushState",{enumerable:!0,get:function(){return q.ForceFlushState}});Object.defineProperty(m,"InMemorySpanExporter",{enumerable:!0,get:function(){return q.InMemorySpanExporter}});Object.defineProperty(m,"NoopSpanProcessor",{enumerable:!0,get:function(){return q.NoopSpanProcessor}});Object.defineProperty(m,"ParentBasedSampler",{enumerable:!0,get:function(){return q.ParentBasedSampler}});Object.defineProperty(m,"RandomIdGenerator",{enumerable:!0,get:function(){return q.RandomIdGenerator}});Object.defineProperty(m,"SamplingDecision",{enumerable:!0,get:function(){return q.SamplingDecision}});Object.defineProperty(m,"SimpleSpanProcessor",{enumerable:!0,get:function(){return q.SimpleSpanProcessor}});Object.defineProperty(m,"Span",{enumerable:!0,get:function(){return q.Span}});Object.defineProperty(m,"TraceIdRatioBasedSampler",{enumerable:!0,get:function(){return q.TraceIdRatioBasedSampler}});Object.defineProperty(m,"Tracer",{enumerable:!0,get:function(){return q.Tracer}})});function Bp(e){return new Oa(e)}function Vp(e,t,r){var n,o,s;return!t&&!r?s=e:r?(n=e,o=t,s=r):(n=e,s=t),n=n??{},o=o??y.active(),{opts:n,ctx:o,fn:s}}function wp(e,t,r){var n,o=(n=t.onException)!==null&&n!==void 0?n:Rh,s=function(i){throw o(i,e),e.end(),i};try{var a=r(e);return typeof(a==null?void 0:a.then)=="function"?a.then(function(i){return e.end(),i},s):(e.end(),a)}catch(i){throw s(i)}}var Rh,Oa,Gp=c(()=>{d();Rh=function(e,t){t.recordException(e),t.setStatus({code:Ie.ERROR})};Oa=function(){function e(t){this._tracer=t,this.startSpan=t.startSpan.bind(this._tracer),this.startActiveSpan=t.startActiveSpan.bind(this._tracer)}return e.prototype.withActiveSpan=function(t,r,n,o){var s=Vp(r,n,o),a=s.opts,i=s.ctx,E=s.fn;return this._tracer.startActiveSpan(t,a,i,function(_){return wp(_,a,E)})},e.prototype.withSpan=function(t,r,n,o){var s=Vp(r,n,o),a=s.opts,i=s.ctx,E=s.fn,_=this._tracer.startSpan(t,a,i);return wp(_,a,E)},e}()});var Hp={};te(Hp,{SugaredTracer:()=>Oa,wrapTracer:()=>Bp});var Yp=c(()=>{Gp()});import{fileURLToPath as Ym}from"url";import{Buffer as Im}from"buffer";import Mm from"process";import{pipeline as xm,Readable as Dm}from"stream";import{promisify as Um}from"util";var fS="__netlify__getTracer",RS="__netlify__shutdownTracers",$a=async(e,t)=>{var r;return(r=globalThis[fS])==null?void 0:r.call(globalThis,e,t)},ka=async()=>{var e;return(e=globalThis[RS])==null?void 0:e.call(globalThis)};d();M();M();function N_(e){let t=BigInt(1e9);return BigInt(e[0])*t+BigInt(e[1])}function LA(e){let t=Number(BigInt.asUintN(32,e)),r=Number(BigInt.asUintN(32,e>>BigInt(32)));return{low:t,high:r}}function v_(e){let t=N_(e);return LA(t)}function gA(e){return N_(e).toString()}var CA=typeof BigInt<"u"?gA:Rn;function O_(e){return e}function P_(e){if(e!==void 0)return xe(e)}var IA={encodeHrTime:v_,encodeSpanContext:xe,encodeOptionalSpanContext:P_};function L_(e){var t,r;if(e===void 0)return IA;let n=(t=e.useLongBits)!==null&&t!==void 0?t:!0,o=(r=e.useHex)!==null&&r!==void 0?r:!1;return{encodeHrTime:n?v_:CA,encodeSpanContext:o?O_:xe,encodeOptionalSpanContext:o?O_:P_}}function g_(e){return{attributes:Er(e.attributes),droppedAttributesCount:0}}function C_(e){return{name:e.name,version:e.version}}function Er(e){return Object.keys(e).map(t=>I_(t,e[t]))}function I_(e,t){return{key:e,value:M_(t)}}function M_(e){let t=typeof e;return t==="string"?{stringValue:e}:t==="number"?Number.isInteger(e)?{intValue:e}:{doubleValue:e}:t==="boolean"?{boolValue:e}:e instanceof Uint8Array?{bytesValue:e}:Array.isArray(e)?{arrayValue:{values:e.map(M_)}}:t==="object"&&e!=null?{kvlistValue:{values:Object.entries(e).map(([r,n])=>I_(r,n))}}:{}}function FA(e,t){var r;let n=e.spanContext(),o=e.status;return{traceId:t.encodeSpanContext(n.traceId),spanId:t.encodeSpanContext(n.spanId),parentSpanId:t.encodeOptionalSpanContext(e.parentSpanId),traceState:(r=n.traceState)===null||r===void 0?void 0:r.serialize(),name:e.name,kind:e.kind==null?0:e.kind+1,startTimeUnixNano:t.encodeHrTime(e.startTime),endTimeUnixNano:t.encodeHrTime(e.endTime),attributes:Er(e.attributes),droppedAttributesCount:e.droppedAttributesCount,events:e.events.map(s=>kA(s,t)),droppedEventsCount:e.droppedEventsCount,status:{code:o.code,message:o.message},links:e.links.map(s=>$A(s,t)),droppedLinksCount:e.droppedLinksCount}}function $A(e,t){var r;return{attributes:e.attributes?Er(e.attributes):[],spanId:t.encodeSpanContext(e.context.spanId),traceId:t.encodeSpanContext(e.context.traceId),traceState:(r=e.context.traceState)===null||r===void 0?void 0:r.serialize(),droppedAttributesCount:e.droppedAttributesCount||0}}function kA(e,t){return{attributes:e.attributes?Er(e.attributes):[],name:e.name,timeUnixNano:t.encodeHrTime(e.time),droppedAttributesCount:e.droppedAttributesCount||0}}function pc(e,t){let r=L_(t);return{resourceSpans:jA(e,r)}}function qA(e){let t=new Map;for(let r of e){let n=t.get(r.resource);n||(n=new Map,t.set(r.resource,n));let o=`${r.instrumentationLibrary.name}@${r.instrumentationLibrary.version||""}:${r.instrumentationLibrary.schemaUrl||""}`,s=n.get(o);s||(s=[],n.set(o,s)),s.push(r)}return t}function jA(e,t){let r=qA(e),n=[],o=r.entries(),s=o.next();for(;!s.done;){let[a,i]=s.value,E=[],_=i.values(),T=_.next();for(;!T.done;){let f=T.value;if(f.length>0){let u=f.map(N=>FA(N,t));E.push({scope:C_(f[0].instrumentationLibrary),spans:u,schemaUrl:f[0].instrumentationLibrary.schemaUrl})}T=_.next()}let p={resource:g_(a),scopeSpans:E,schemaUrl:void 0};n.push(p),s=o.next()}return n}var Cn={serializeRequest:e=>{let t=pc(e,{useHex:!0,useLongBits:!1});return new TextEncoder().encode(JSON.stringify(t))},deserializeResponse:e=>{let t=new TextDecoder;return JSON.parse(t.decode(e))}};var dh=Object.defineProperty,hh=Object.getOwnPropertyNames,kp=e=>{throw TypeError(e)},La=(e,t)=>function(){return e&&(t=(0,e[hh(e)[0]])(e=0)),t},qp=(e,t)=>{for(var r in t)dh(e,r,{get:t[r],enumerable:!0})},ga=(e,t,r)=>t.has(e)||kp("Cannot "+r),Pr=(e,t,r)=>(ga(e,t,"read from private field"),r?r.call(e):t.get(e)),Eo=(e,t,r)=>t.has(e)?kp("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Fp=(e,t,r,n)=>(ga(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),mh=(e,t,r)=>(ga(e,t,"access private method"),r),jp,Kp,Xp,Wp=La({"src/constants.ts"(){"use strict";jp="__netlify__getTracer",Kp="__netlify__shutdownTracers",Xp="__nfOTLPTrace"}}),zp={};qp(zp,{NetlifySpanExporter:()=>Jp});var Lr,gr,Na,va,$p,Pa,Jp,Oh=La({"src/bootstrap/netlify_span_exporter.ts"(){"use strict";Wp(),Pa=class Qp{constructor(){Eo(this,va),Eo(this,Lr),Eo(this,gr),Fp(this,Lr,new de(mh(this,va,$p),this)),Fp(this,gr,S.createComponentLogger({namespace:"netlify-span-exporter"}))}export(t,r){if(Pr(this,gr).debug(`export ${t.length} spans`),Pr(this,Lr).isCalled){r({code:B.FAILED,error:new Error("Exporter has been shutdown")});return}return console.log(Xp,Pr(Qp,Na).decode(Cn.serializeRequest(t))),r({code:B.SUCCESS})}shutdown(){return Pr(this,Lr).call()}},Lr=new WeakMap,gr=new WeakMap,Na=new WeakMap,va=new WeakSet,$p=function(){return Pr(this,gr).debug("Shutting down"),Promise.resolve()},Eo(Pa,Na,new TextDecoder),Jp=Pa}}),Zp={};qp(Zp,{default:()=>eu});var eu,Nh=La({"package.json"(){eu={name:"@netlify/otel",version:"3.0.2",type:"module",engines:{node:"^18.14.0 || >=20.6.1"},main:"./dist/main.cjs",module:"./dist/main.js",types:"./dist/main.d.ts",exports:{".":{require:{types:"./dist/main.d.cts",default:"./dist/main.cjs"},import:{types:"./dist/main.d.ts",default:"./dist/main.js"},default:{types:"./dist/main.d.ts",default:"./dist/main.js"}},"./package.json":"./package.json","./bootstrap":{require:{types:"./dist/bootstrap/main.d.cts",default:"./dist/bootstrap/main.cjs"},import:{types:"./dist/bootstrap/main.d.ts",default:"./dist/bootstrap/main.js"},default:{types:"./dist/bootstrap/main.d.ts",default:"./dist/bootstrap/main.js"}}},files:["dist/**/*"],scripts:{build:"tsup-node",dev:"tsup-node --watch",prepack:"npm run build",test:"run-s build test:ci","test:dev":"run-s build test:dev:*","test:ci":"run-s build test:ci:*","test:dev:vitest":"vitest","test:dev:vitest:watch":"vitest watch","test:ci:vitest":"vitest run",publint:"npx -y publint --strict"},keywords:["netlify","cdn"],license:"MIT",repository:"netlify/primitives",bugs:{url:"https://github.com/netlify/primitives/issues"},author:"Netlify Inc.",devDependencies:{"npm-run-all2":"^7.0.2",tsup:"^8.0.0",vitest:"^3.0.0"},dependencies:{"@opentelemetry/api":"1.9.0","@opentelemetry/core":"1.30.1","@opentelemetry/otlp-transformer":"0.57.2","@opentelemetry/resources":"1.30.1","@opentelemetry/sdk-trace-node":"1.30.1"}}}});Wp();var tu=async e=>{if(!e.headers.has("x-nf-enable-tracing"))return;let{version:t}=await import("process"),r=t.slice(1),{Resource:n}=await Promise.resolve().then(()=>(Hs(),Tc)),{NodeTracerProvider:o,BatchSpanProcessor:s}=await Promise.resolve().then(()=>AS(bp(),1)),{NetlifySpanExporter:a}=await Promise.resolve().then(()=>(Oh(),zp)),i=new n({"service.name":e.serviceName,"service.version":e.serviceVersion,"process.runtime.name":"nodejs","process.runtime.version":r,"deployment.environment":e.deploymentEnvironment,"http.url":e.siteUrl,"netlify.site.id":e.siteId,"netlify.site.name":e.siteName}),E=new o({resource:i,spanProcessors:[new s(new a)]});E.register(),Object.defineProperty(globalThis,jp,{enumerable:!1,configurable:!0,writable:!1,value:async function(T,p){let{trace:f}=await Promise.resolve().then(()=>(d(),rt)),{SugaredTracer:u}=await Promise.resolve().then(()=>(Yp(),Hp));if(T)return new u(f.getTracer(T,p));let{default:N}=await Promise.resolve().then(()=>(Nh(),Zp));return new u(f.getTracer(N.name,N.version))}}),Object.defineProperty(globalThis,Kp,{enumerable:!1,configurable:!0,writable:!1,value:async()=>await E.shutdown()})};var ux=globalThis.Buffer?e=>Buffer.from(e,"base64").toString():e=>atob(e),ru=globalThis.Buffer?e=>Buffer.from(e).toString("base64"):e=>btoa(e);var su=e=>{throw TypeError(e)},au=(e,t,r)=>t.has(e)||su("Cannot "+r),Z=(e,t,r)=>(au(e,t,"read from private field"),r?r.call(e):t.get(e)),Ir=(e,t,r)=>t.has(e)?su("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Mr=(e,t,r,n)=>(au(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),po=(e=>(e.Delete="delete",e.Read="read",e.Write="write",e))(po||{}),vh={invalid_vary:"Responses must not use unsupported directives of the `Netlify-Vary` header (https://ntl.fyi/cache_api_invalid_vary).",no_cache:"Responses must not set cache control headers with the `private`, `no-cache` or `no-store` directives (https://ntl.fyi/cache_api_no_cache).",low_ttl:"Responses must have a cache control header with a `max-age` or `s-maxage` directive (https://ntl.fyi/cache_api_low_ttl).",no_directive:"Responses must have a cache control header with caching directives (https://ntl.fyi/cache_api_no_directive).",no_ttl:"Responses must have a cache control header with a `max-age` or `s-maxage` directive (https://ntl.fyi/cache_api_no_ttl).",no_status:"Responses must specify a status code (https://ntl.fyi/cache_api_no_status).",invalid_directive:"Responses must have a cache control header with caching directives (https://ntl.fyi/cache_api_invalid_directive).",status:"Responses must have a status code between 200 and 299 (https://ntl.fyi/cache_api_status)."},Ph="The server has returned an unexpected error (https://ntl.fyi/cache_api_error).",Lh="netlify-programmable-error",gh="netlify-programmable-headers",Ch="netlify-programmable-status",Ih="netlify-programmable-store",Mh="netlify-forwarded-host",xh="user-agent",nu=new Set(["http:","https:"]),Dh=new Set(["cookie","content-encoding","content-length"]),_o=Symbol("getInternalHeaders"),ou=Symbol("serializeResourceHeaders"),xt,co,Cr,Uh=class{constructor({getContext:e,name:t,userAgent:r}){Ir(this,xt),Ir(this,co),Ir(this,Cr),Mr(this,xt,e),Mr(this,co,t),Mr(this,Cr,r)}[_o](e){let{host:t,token:r}=e,n={Authorization:`Bearer ${r}`,[Ih]:Z(this,co)};return t&&(n[Mh]=t),Z(this,Cr)&&(n[xh]=Z(this,Cr)),n}[ou](e){let t={};return e.forEach((r,n)=>{Dh.has(n)||(n==="set-cookie"?(t[n]=t[n]||[],t[n].push(r)):t[n]=r.split(","))}),ru(JSON.stringify(t))}async add(e){await this.put(new Request(e),await fetch(e))}async addAll(e){await Promise.allSettled(e.map(t=>this.add(t)))}async delete(e){let t=Z(this,xt).call(this,{operation:"delete"});if(t){let r=Ca(e);await fetch(`${t.url}/${Ia(r)}`,{headers:this[_o](t),method:"DELETE"})}return!0}async keys(e){return[]}async match(e){try{let t=Z(this,xt).call(this,{operation:"read"});if(!t)return;let r=Ca(e),n=`${t.url}/${Ia(r)}`,o=await fetch(n,{headers:this[_o](t),method:"GET"});return o.ok?o:void 0}catch{}}async matchAll(e,t){if(!e)return[];let r=await this.match(e);return r?[r]:[]}async put(e,t){var s,a,i;if(!t.ok)throw new TypeError(`Cannot cache response with status ${t.status}.`);if(e instanceof Request&&e.method!=="GET")throw new TypeError(`Cannot cache response to ${e.method} request.`);if(t.status===206)throw new TypeError("Cannot cache response to a range request (206 Partial Content).");if((s=t.headers.get("vary"))!=null&&s.includes("*"))throw new TypeError("Cannot cache response with 'Vary: *' header.");let r=Z(this,xt).call(this,{operation:"write"});if(!r)return;let n=Ca(e),o=await fetch(`${r.url}/${Ia(n)}`,{body:t.body,headers:{...this[_o](r),[gh]:this[ou](t.headers),[Ch]:t.status.toString()},duplex:"half",method:"POST"});if(!o.ok){let E=((a=o.headers)==null?void 0:a.get(Lh))??"",_=vh[E]||Ph;(i=r.logger)==null||i.call(r,`Failed to write to the cache: ${_}`)}}};xt=new WeakMap;co=new WeakMap;Cr=new WeakMap;var Ca=e=>{let t;if(e instanceof Request)t=new URL(e.url);else try{t=new URL(String(e))}catch{throw new TypeError(`${e} is not a valid URL.`)}if(!nu.has(t.protocol))throw new TypeError(`Cannot cache response for URL with unsupported protocol (${t.protocol}). Supported protocols are ${[...nu].join(", ")}.`);return t},Ia=e=>encodeURIComponent(e.toString()),To,Oe,iu=class{constructor(e){Ir(this,To),Ir(this,Oe),Mr(this,To,e),Mr(this,Oe,new Map)}open(e){let t=Z(this,Oe).get(e);return t||(t=new Uh({...Z(this,To),name:e}),Z(this,Oe).set(e,t)),Promise.resolve(t)}has(e){return Promise.resolve(Z(this,Oe).has(e))}delete(e){return Promise.resolve(Z(this,Oe).delete(e))}keys(){return Promise.resolve([...Z(this,Oe).keys()])}async match(e,t){var r;if(t!=null&&t.cacheName)return(r=Z(this,Oe).get(t.cacheName))==null?void 0:r.match(e);for(let n of Z(this,Oe).values())if(await n.match(e)===void 0)return}};To=new WeakMap;Oe=new WeakMap;var Dt=class e extends Error{constructor(t){super(t),this.name="NetlifyUserError",Object.setPrototypeOf(this,e.prototype)}};var yh={"cache-api-read":100,"cache-api-write":20},uo=class{constructor(t=yh){this.counts=new Map,this.limits=t}register(t){let r=this.counts.get(t)||0,n=this.limits[t]||0;return r>=n?!1:(this.counts.set(t,r+1),!0)}};import{AsyncLocalStorage as bh}from"async_hooks";var ie=new bh;import{env as Eu}from"process";var So=()=>!!Eu.NETLIFY_DEV||!!Eu.NETLIFY_LOCAL;var _u="__nfSystemLog",Ut=(e,t)=>{So()||console.log(_u,{msg:e,fields:t})},Ma=e=>{console.log(_u,JSON.stringify(e))};var cu=e=>{let{pcToken:t,pcURL:r,rawUrl:n}=e;return!t||!r?null:{host:new URL(n).hostname,token:t,url:new URL("/.netlify/cache",r).toString()}},Vh=()=>{globalThis.caches=new iu({getContext:({operation:e})=>{let t=ie.getStore();if(!(t!=null&&t.operationCounter))throw new Dt("The Cache API must be used within the scope of the request handler. Refer to https://ntl.fyi/cache-api-scope for more information.");let{cacheAPI:r,operationCounter:n}=t,o=e===po.Delete||e===po.Write?"cache-api-write":"cache-api-read";return n.register(o)?r||(Ut("missing Cache API metadata in request"),null):(console.log(`You've exceeded the number of allowed Cache API ${o==="cache-api-write"?"writes":"reads"} for a single invocation. Refer to https://ntl.fyi/cache-api-limits for more information.`),null)},userAgent:"netlify-functions"})};Vh();var Tu="x-nf-client-connection-ip",pu="x-nf-geo",uu="x-nf-account-id",lo="x-nf-deploy-id",Su="x-nf-deploy-context",lu="x-nf-deploy-published",Au="x-nf-site-id",fu="x-nf-request-flags",xa="x-nf-invocation-metadata",Ru="x-nf-request-id",du="x-nf-start-timestamp",hu="x-nf-trace-span-id",mu="x-nf-request-start",wh=255,Ou=e=>{let t=new Headers;return Object.entries(e).forEach(([r,n])=>{if(n!==void 0)try{t.set(r.toLowerCase(),n)}catch(o){if([...n].every(a=>a.codePointAt(0)<=wh))throw o;console.warn(`Discarded request header '${r}' because it contains non-ASCII characters`)}}),t},Nu=e=>{let t={};for(let[r,n]of e.entries())r in t?t[r].push(n):t[r]=[n];return t};var Ao=e=>({id:e.headers.get(uu)??""});var fo=e=>({context:e.get(Su)??"",id:e.get(lo)??"",published:e.get(lu)==="1"});import{Buffer as Bh}from"buffer";var ho=Symbol("Request flags"),xr=Symbol("Request route"),Gh=typeof Request>"u"?class{}:Request,vu,Ro=class extends(vu=Gh,ho,xr,vu){},Pu=(e,t)=>{if(!(e==null||e===""))return t?Bh.from(e,"base64"):e};var yt,Ur,Dr=class{constructor(t){go(this,yt);go(this,Ur);Co(this,yt,new Set),Co(this,Ur,t)}get(t){let r=Hr(this,Ur)[t];return r!==void 0&&Hr(this,yt).add(t),r}get evaluations(){return Hr(this,yt)}};yt=new WeakMap,Ur=new WeakMap;var Lu=e=>e[ho]??new Dr({}),gu=(e,t)=>{e[ho]=t};import{Buffer as Hh}from"buffer";var Cu=e=>{if(e===null)return{};try{let{postal_code:t,...r}=JSON.parse(Hh.from(e,"base64").toString("utf-8"));return Object.fromEntries(Object.entries({...r,postalCode:t}).filter(([,o])=>o!==void 0))}catch{return{}}};var Iu=e=>e??"";var Vr=class{constructor(e,t,r,n,o,s){this.type=3,this.name="",this.prefix="",this.value="",this.suffix="",this.modifier=3,this.type=e,this.name=t,this.prefix=r,this.value=n,this.suffix=o,this.modifier=s}hasCustomName(){return this.name!==""&&typeof this.name!="number"}},Yh=/[$_\p{ID_Start}]/u,Fh=/[$_\u200C\u200D\p{ID_Continue}]/u,Ua=".*";function $h(e,t){return(t?/^[\x00-\xFF]*$/:/^[\x00-\x7F]*$/).test(e)}function Du(e,t=!1){let r=[],n=0;for(;n<e.length;){let o=e[n],s=function(a){if(!t)throw new TypeError(a);r.push({type:"INVALID_CHAR",index:n,value:e[n++]})};if(o==="*"){r.push({type:"ASTERISK",index:n,value:e[n++]});continue}if(o==="+"||o==="?"){r.push({type:"OTHER_MODIFIER",index:n,value:e[n++]});continue}if(o==="\\"){r.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if(o==="{"){r.push({type:"OPEN",index:n,value:e[n++]});continue}if(o==="}"){r.push({type:"CLOSE",index:n,value:e[n++]});continue}if(o===":"){let a="",i=n+1;for(;i<e.length;){let E=e.substr(i,1);if(i===n+1&&Yh.test(E)||i!==n+1&&Fh.test(E)){a+=e[i++];continue}break}if(!a){s(`Missing parameter name at ${n}`);continue}r.push({type:"NAME",index:n,value:a}),n=i;continue}if(o==="("){let a=1,i="",E=n+1,_=!1;if(e[E]==="?"){s(`Pattern cannot start with "?" at ${E}`);continue}for(;E<e.length;){if(!$h(e[E],!1)){s(`Invalid character '${e[E]}' at ${E}.`),_=!0;break}if(e[E]==="\\"){i+=e[E++]+e[E++];continue}if(e[E]===")"){if(a--,a===0){E++;break}}else if(e[E]==="("&&(a++,e[E+1]!=="?")){s(`Capturing groups are not allowed at ${E}`),_=!0;break}i+=e[E++]}if(_)continue;if(a){s(`Unbalanced pattern at ${n}`);continue}if(!i){s(`Missing pattern at ${n}`);continue}r.push({type:"REGEX",index:n,value:i}),n=E;continue}r.push({type:"CHAR",index:n,value:e[n++]})}return r.push({type:"END",index:n,value:""}),r}function Uu(e,t={}){let r=Du(e);t.delimiter??(t.delimiter="/#?"),t.prefixes??(t.prefixes="./");let n=`[^${Ee(t.delimiter)}]+?`,o=[],s=0,a=0,i="",E=new Set,_=P=>{if(a<r.length&&r[a].type===P)return r[a++].value},T=()=>_("OTHER_MODIFIER")??_("ASTERISK"),p=P=>{let U=_(P);if(U!==void 0)return U;let{type:I,index:K}=r[a];throw new TypeError(`Unexpected ${I} at ${K}, expected ${P}`)},f=()=>{let P="",U;for(;U=_("CHAR")??_("ESCAPED_CHAR");)P+=U;return P},u=P=>P,N=t.encodePart||u,x="",D=P=>{x+=P},j=()=>{x.length&&(o.push(new Vr(3,"","",N(x),"",3)),x="")},_e=(P,U,I,K,Br)=>{let H=3;switch(Br){case"?":H=1;break;case"*":H=0;break;case"+":H=2;break}if(!U&&!I&&H===3){D(P);return}if(j(),!U&&!I){if(!P)return;o.push(new Vr(3,"","",N(P),"",H));return}let Y;I?I==="*"?Y=Ua:Y=I:Y=n;let le=2;Y===n?(le=1,Y=""):Y===Ua&&(le=0,Y="");let ce;if(U?ce=U:I&&(ce=s++),E.has(ce))throw new TypeError(`Duplicate name '${ce}'.`);E.add(ce),o.push(new Vr(le,ce,N(P),Y,N(K),H))};for(;a<r.length;){let P=_("CHAR"),U=_("NAME"),I=_("REGEX");if(!U&&!I&&(I=_("ASTERISK")),U||I){let H=P??"";t.prefixes.indexOf(H)===-1&&(D(H),H=""),j();let Y=T();_e(H,U,I,"",Y);continue}let K=P??_("ESCAPED_CHAR");if(K){D(K);continue}if(_("OPEN")){let H=f(),Y=_("NAME"),le=_("REGEX");!Y&&!le&&(le=_("ASTERISK"));let ce=f();p("CLOSE");let vo=T();_e(H,Y,le,ce,vo);continue}j(),p("END")}return o}function Ee(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function Mu(e){return e&&e.ignoreCase?"ui":"u"}function kh(e,t,r){return yu(Uu(e,r),t,r)}function bt(e){switch(e){case 0:return"*";case 1:return"?";case 2:return"+";case 3:return""}}function yu(e,t,r={}){r.delimiter??(r.delimiter="/#?"),r.prefixes??(r.prefixes="./"),r.sensitive??(r.sensitive=!1),r.strict??(r.strict=!1),r.end??(r.end=!0),r.start??(r.start=!0),r.endsWith="";let n=r.start?"^":"";for(let i of e){if(i.type===3){i.modifier===3?n+=Ee(i.value):n+=`(?:${Ee(i.value)})${bt(i.modifier)}`;continue}t&&t.push(i.name);let E=`[^${Ee(r.delimiter)}]+?`,_=i.value;if(i.type===1?_=E:i.type===0&&(_=Ua),!i.prefix.length&&!i.suffix.length){i.modifier===3||i.modifier===1?n+=`(${_})${bt(i.modifier)}`:n+=`((?:${_})${bt(i.modifier)})`;continue}if(i.modifier===3||i.modifier===1){n+=`(?:${Ee(i.prefix)}(${_})${Ee(i.suffix)})`,n+=bt(i.modifier);continue}n+=`(?:${Ee(i.prefix)}`,n+=`((?:${_})(?:`,n+=Ee(i.suffix),n+=Ee(i.prefix),n+=`(?:${_}))*)${Ee(i.suffix)})`,i.modifier===0&&(n+="?")}let o=`[${Ee(r.endsWith)}]|$`,s=`[${Ee(r.delimiter)}]`;if(r.end)return r.strict||(n+=`${s}?`),r.endsWith.length?n+=`(?=${o})`:n+="$",new RegExp(n,Mu(r));r.strict||(n+=`(?:${s}(?=${o}))?`);let a=!1;if(e.length){let i=e[e.length-1];i.type===3&&i.modifier===3&&(a=r.delimiter.indexOf(i)>-1)}return a||(n+=`(?=${s}|${o})`),new RegExp(n,Mu(r))}var Ge={delimiter:"",prefixes:"",sensitive:!0,strict:!0},qh={delimiter:".",prefixes:"",sensitive:!0,strict:!0},jh={delimiter:"/",prefixes:"/",sensitive:!0,strict:!0};function Kh(e,t){return e.length?e[0]==="/"?!0:!t||e.length<2?!1:(e[0]=="\\"||e[0]=="{")&&e[1]=="/":!1}function bu(e,t){return e.startsWith(t)?e.substring(t.length,e.length):e}function Xh(e,t){return e.endsWith(t)?e.substr(0,e.length-t.length):e}function Vu(e){return!e||e.length<2?!1:e[0]==="["||(e[0]==="\\"||e[0]==="{")&&e[1]==="["}var wu=["ftp","file","http","https","ws","wss"];function Bu(e){if(!e)return!0;for(let t of wu)if(e.test(t))return!0;return!1}function Wh(e,t){if(e=bu(e,"#"),t||e==="")return e;let r=new URL("https://example.com");return r.hash=e,r.hash?r.hash.substring(1,r.hash.length):""}function zh(e,t){if(e=bu(e,"?"),t||e==="")return e;let r=new URL("https://example.com");return r.search=e,r.search?r.search.substring(1,r.search.length):""}function Jh(e,t){return t||e===""?e:Vu(e)?Yu(e):Hu(e)}function Qh(e,t){if(t||e==="")return e;let r=new URL("https://example.com");return r.password=e,r.password}function Zh(e,t){if(t||e==="")return e;let r=new URL("https://example.com");return r.username=e,r.username}function em(e,t,r){if(r||e==="")return e;if(t&&!wu.includes(t))return new URL(`${t}:${e}`).pathname;let n=e[0]=="/";return e=new URL(n?e:"/-"+e,"https://example.com").pathname,n||(e=e.substring(2,e.length)),e}function tm(e,t,r){return Gu(t)===e&&(e=""),r||e===""?e:Fu(e)}function rm(e,t){return e=Xh(e,":"),t||e===""?e:ya(e)}function Gu(e){switch(e){case"ws":case"http":return"80";case"wws":case"https":return"443";case"ftp":return"21";default:return""}}function ya(e){if(e==="")return e;if(/^[-+.A-Za-z0-9]*$/.test(e))return e.toLowerCase();throw new TypeError(`Invalid protocol '${e}'.`)}function nm(e){if(e==="")return e;let t=new URL("https://example.com");return t.username=e,t.username}function om(e){if(e==="")return e;let t=new URL("https://example.com");return t.password=e,t.password}function Hu(e){if(e==="")return e;if(/[\t\n\r #%/:<>?@[\]^\\|]/g.test(e))throw new TypeError(`Invalid hostname '${e}'`);let t=new URL("https://example.com");return t.hostname=e,t.hostname}function Yu(e){if(e==="")return e;if(/[^0-9a-fA-F[\]:]/g.test(e))throw new TypeError(`Invalid IPv6 hostname '${e}'`);return e.toLowerCase()}function Fu(e){if(e===""||/^[0-9]*$/.test(e)&&parseInt(e)<=65535)return e;throw new TypeError(`Invalid port '${e}'.`)}function sm(e){if(e==="")return e;let t=new URL("https://example.com");return t.pathname=e[0]!=="/"?"/-"+e:e,e[0]!=="/"?t.pathname.substring(2,t.pathname.length):t.pathname}function am(e){return e===""?e:new URL(`data:${e}`).pathname}function im(e){if(e==="")return e;let t=new URL("https://example.com");return t.search=e,t.search.substring(1,t.search.length)}function Em(e){if(e==="")return e;let t=new URL("https://example.com");return t.hash=e,t.hash.substring(1,t.hash.length)}var _m=class{constructor(e){this.tokenList=[],this.internalResult={},this.tokenIndex=0,this.tokenIncrement=1,this.componentStart=0,this.state=0,this.groupDepth=0,this.hostnameIPv6BracketDepth=0,this.shouldTreatAsStandardURL=!1,this.input=e}get result(){return this.internalResult}parse(){for(this.tokenList=Du(this.input,!0);this.tokenIndex<this.tokenList.length;this.tokenIndex+=this.tokenIncrement){if(this.tokenIncrement=1,this.tokenList[this.tokenIndex].type==="END"){if(this.state===0){this.rewind(),this.isHashPrefix()?this.changeState(9,1):this.isSearchPrefix()?(this.changeState(8,1),this.internalResult.hash=""):(this.changeState(7,0),this.internalResult.search="",this.internalResult.hash="");continue}else if(this.state===2){this.rewindAndSetState(5);continue}this.changeState(10,0);break}if(this.groupDepth>0)if(this.isGroupClose())this.groupDepth-=1;else continue;if(this.isGroupOpen()){this.groupDepth+=1;continue}switch(this.state){case 0:this.isProtocolSuffix()&&(this.internalResult.username="",this.internalResult.password="",this.internalResult.hostname="",this.internalResult.port="",this.internalResult.pathname="",this.internalResult.search="",this.internalResult.hash="",this.rewindAndSetState(1));break;case 1:if(this.isProtocolSuffix()){this.computeShouldTreatAsStandardURL();let e=7,t=1;this.shouldTreatAsStandardURL&&(this.internalResult.pathname="/"),this.nextIsAuthoritySlashes()?(e=2,t=3):this.shouldTreatAsStandardURL&&(e=2),this.changeState(e,t)}break;case 2:this.isIdentityTerminator()?this.rewindAndSetState(3):(this.isPathnameStart()||this.isSearchPrefix()||this.isHashPrefix())&&this.rewindAndSetState(5);break;case 3:this.isPasswordPrefix()?this.changeState(4,1):this.isIdentityTerminator()&&this.changeState(5,1);break;case 4:this.isIdentityTerminator()&&this.changeState(5,1);break;case 5:this.isIPv6Open()?this.hostnameIPv6BracketDepth+=1:this.isIPv6Close()&&(this.hostnameIPv6BracketDepth-=1),this.isPortPrefix()&&!this.hostnameIPv6BracketDepth?this.changeState(6,1):this.isPathnameStart()?this.changeState(7,0):this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 6:this.isPathnameStart()?this.changeState(7,0):this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 7:this.isSearchPrefix()?this.changeState(8,1):this.isHashPrefix()&&this.changeState(9,1);break;case 8:this.isHashPrefix()&&this.changeState(9,1);break;case 9:break;case 10:break}}}changeState(e,t){switch(this.state){case 0:break;case 1:this.internalResult.protocol=this.makeComponentString();break;case 2:break;case 3:this.internalResult.username=this.makeComponentString();break;case 4:this.internalResult.password=this.makeComponentString();break;case 5:this.internalResult.hostname=this.makeComponentString();break;case 6:this.internalResult.port=this.makeComponentString();break;case 7:this.internalResult.pathname=this.makeComponentString();break;case 8:this.internalResult.search=this.makeComponentString();break;case 9:this.internalResult.hash=this.makeComponentString();break;case 10:break}this.changeStateWithoutSettingComponent(e,t)}changeStateWithoutSettingComponent(e,t){this.state=e,this.componentStart=this.tokenIndex+t,this.tokenIndex+=t,this.tokenIncrement=0}rewind(){this.tokenIndex=this.componentStart,this.tokenIncrement=0}rewindAndSetState(e){this.rewind(),this.state=e}safeToken(e){return e<0&&(e=this.tokenList.length-e),e<this.tokenList.length?this.tokenList[e]:this.tokenList[this.tokenList.length-1]}isNonSpecialPatternChar(e,t){let r=this.safeToken(e);return r.value===t&&(r.type==="CHAR"||r.type==="ESCAPED_CHAR"||r.type==="INVALID_CHAR")}isProtocolSuffix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}nextIsAuthoritySlashes(){return this.isNonSpecialPatternChar(this.tokenIndex+1,"/")&&this.isNonSpecialPatternChar(this.tokenIndex+2,"/")}isIdentityTerminator(){return this.isNonSpecialPatternChar(this.tokenIndex,"@")}isPasswordPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}isPortPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,":")}isPathnameStart(){return this.isNonSpecialPatternChar(this.tokenIndex,"/")}isSearchPrefix(){if(this.isNonSpecialPatternChar(this.tokenIndex,"?"))return!0;if(this.tokenList[this.tokenIndex].value!=="?")return!1;let e=this.safeToken(this.tokenIndex-1);return e.type!=="NAME"&&e.type!=="REGEX"&&e.type!=="CLOSE"&&e.type!=="ASTERISK"}isHashPrefix(){return this.isNonSpecialPatternChar(this.tokenIndex,"#")}isGroupOpen(){return this.tokenList[this.tokenIndex].type=="OPEN"}isGroupClose(){return this.tokenList[this.tokenIndex].type=="CLOSE"}isIPv6Open(){return this.isNonSpecialPatternChar(this.tokenIndex,"[")}isIPv6Close(){return this.isNonSpecialPatternChar(this.tokenIndex,"]")}makeComponentString(){let e=this.tokenList[this.tokenIndex],t=this.safeToken(this.componentStart).index;return this.input.substring(t,e.index)}computeShouldTreatAsStandardURL(){let e={};Object.assign(e,Ge),e.encodePart=ya;let t=kh(this.makeComponentString(),void 0,e);this.shouldTreatAsStandardURL=Bu(t)}},Da=["protocol","username","password","hostname","port","pathname","search","hash"],Be="*";function xu(e,t){if(typeof e!="string")throw new TypeError("parameter 1 is not of type 'string'.");let r=new URL(e,t);return{protocol:r.protocol.substring(0,r.protocol.length-1),username:r.username,password:r.password,hostname:r.hostname,port:r.port,pathname:r.pathname,search:r.search!==""?r.search.substring(1,r.search.length):void 0,hash:r.hash!==""?r.hash.substring(1,r.hash.length):void 0}}function Le(e,t){return t?br(e):e}function yr(e,t,r){let n;if(typeof t.baseURL=="string")try{n=new URL(t.baseURL),e.protocol=Le(n.protocol.substring(0,n.protocol.length-1),r),e.username=Le(n.username,r),e.password=Le(n.password,r),e.hostname=Le(n.hostname,r),e.port=Le(n.port,r),e.pathname=Le(n.pathname,r),e.search=Le(n.search.substring(1,n.search.length),r),e.hash=Le(n.hash.substring(1,n.hash.length),r)}catch{throw new TypeError(`invalid baseURL '${t.baseURL}'.`)}if(typeof t.protocol=="string"&&(e.protocol=rm(t.protocol,r)),typeof t.username=="string"&&(e.username=Zh(t.username,r)),typeof t.password=="string"&&(e.password=Qh(t.password,r)),typeof t.hostname=="string"&&(e.hostname=Jh(t.hostname,r)),typeof t.port=="string"&&(e.port=tm(t.port,e.protocol,r)),typeof t.pathname=="string"){if(e.pathname=t.pathname,n&&!Kh(e.pathname,r)){let o=n.pathname.lastIndexOf("/");o>=0&&(e.pathname=Le(n.pathname.substring(0,o+1),r)+e.pathname)}e.pathname=em(e.pathname,e.protocol,r)}return typeof t.search=="string"&&(e.search=zh(t.search,r)),typeof t.hash=="string"&&(e.hash=Wh(t.hash,r)),e}function br(e){return e.replace(/([+*?:{}()\\])/g,"\\$1")}function cm(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function Tm(e,t){t.delimiter??(t.delimiter="/#?"),t.prefixes??(t.prefixes="./"),t.sensitive??(t.sensitive=!1),t.strict??(t.strict=!1),t.end??(t.end=!0),t.start??(t.start=!0),t.endsWith="";let r=".*",n=`[^${cm(t.delimiter)}]+?`,o=/[$_\u200C\u200D\p{ID_Continue}]/u,s="";for(let a=0;a<e.length;++a){let i=e[a];if(i.type===3){if(i.modifier===3){s+=br(i.value);continue}s+=`{${br(i.value)}}${bt(i.modifier)}`;continue}let E=i.hasCustomName(),_=!!i.suffix.length||!!i.prefix.length&&(i.prefix.length!==1||!t.prefixes.includes(i.prefix)),T=a>0?e[a-1]:null,p=a<e.length-1?e[a+1]:null;if(!_&&E&&i.type===1&&i.modifier===3&&p&&!p.prefix.length&&!p.suffix.length)if(p.type===3){let f=p.value.length>0?p.value[0]:"";_=o.test(f)}else _=!p.hasCustomName();if(!_&&!i.prefix.length&&T&&T.type===3){let f=T.value[T.value.length-1];_=t.prefixes.includes(f)}_&&(s+="{"),s+=br(i.prefix),E&&(s+=`:${i.name}`),i.type===2?s+=`(${i.value})`:i.type===1?E||(s+=`(${n})`):i.type===0&&(!E&&(!T||T.type===3||T.modifier!==3||_||i.prefix!=="")?s+="*":s+=`(${r})`),i.type===1&&E&&i.suffix.length&&o.test(i.suffix[0])&&(s+="\\"),s+=br(i.suffix),_&&(s+="}"),i.modifier!==3&&(s+=bt(i.modifier))}return s}var mo=class{constructor(e={},t,r){this.regexp={},this.names={},this.component_pattern={},this.parts={};try{let n;if(typeof t=="string"?n=t:r=t,typeof e=="string"){let i=new _m(e);if(i.parse(),e=i.result,n===void 0&&typeof e.protocol!="string")throw new TypeError("A base URL must be provided for a relative constructor string.");e.baseURL=n}else{if(!e||typeof e!="object")throw new TypeError("parameter 1 is not of type 'string' and cannot convert to dictionary.");if(n)throw new TypeError("parameter 1 is not of type 'string'.")}typeof r>"u"&&(r={ignoreCase:!1});let o={ignoreCase:r.ignoreCase===!0},s={pathname:Be,protocol:Be,username:Be,password:Be,hostname:Be,port:Be,search:Be,hash:Be};this.pattern=yr(s,e,!0),Gu(this.pattern.protocol)===this.pattern.port&&(this.pattern.port="");let a;for(a of Da){if(!(a in this.pattern))continue;let i={},E=this.pattern[a];switch(this.names[a]=[],a){case"protocol":Object.assign(i,Ge),i.encodePart=ya;break;case"username":Object.assign(i,Ge),i.encodePart=nm;break;case"password":Object.assign(i,Ge),i.encodePart=om;break;case"hostname":Object.assign(i,qh),Vu(E)?i.encodePart=Yu:i.encodePart=Hu;break;case"port":Object.assign(i,Ge),i.encodePart=Fu;break;case"pathname":Bu(this.regexp.protocol)?(Object.assign(i,jh,o),i.encodePart=sm):(Object.assign(i,Ge,o),i.encodePart=am);break;case"search":Object.assign(i,Ge,o),i.encodePart=im;break;case"hash":Object.assign(i,Ge,o),i.encodePart=Em;break}try{this.parts[a]=Uu(E,i),this.regexp[a]=yu(this.parts[a],this.names[a],i),this.component_pattern[a]=Tm(this.parts[a],i)}catch{throw new TypeError(`invalid ${a} pattern '${this.pattern[a]}'.`)}}}catch(n){throw new TypeError(`Failed to construct 'URLPattern': ${n.message}`)}}test(e={},t){let r={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if(typeof e!="string"&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return!1;try{typeof e=="object"?r=yr(r,e,!1):r=yr(r,xu(e,t),!1)}catch{return!1}let n;for(n of Da)if(!this.regexp[n].exec(r[n]))return!1;return!0}exec(e={},t){let r={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if(typeof e!="string"&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return;try{typeof e=="object"?r=yr(r,e,!1):r=yr(r,xu(e,t),!1)}catch{return null}let n={};t?n.inputs=[e,t]:n.inputs=[e];let o;for(o of Da){let s=this.regexp[o].exec(r[o]);if(!s)return null;let a={};for(let[i,E]of this.names[o].entries())if(typeof E=="string"||typeof E=="number"){let _=s[i+1];a[E]=_}n[o]={input:r[o]??"",groups:a}}return n}static compareComponent(e,t,r){let n=(i,E)=>{for(let _ of["type","modifier","prefix","value","suffix"]){if(i[_]<E[_])return-1;if(i[_]===E[_])continue;return 1}return 0},o=new Vr(3,"","","","",3),s=new Vr(0,"","","","",3),a=(i,E)=>{let _=0;for(;_<Math.min(i.length,E.length);++_){let T=n(i[_],E[_]);if(T)return T}return i.length===E.length?0:n(i[_]??o,E[_]??o)};return!t.component_pattern[e]&&!r.component_pattern[e]?0:t.component_pattern[e]&&!r.component_pattern[e]?a(t.parts[e],[s]):!t.component_pattern[e]&&r.component_pattern[e]?a([s],r.parts[e]):a(t.parts[e],r.parts[e])}get protocol(){return this.component_pattern.protocol}get username(){return this.component_pattern.username}get password(){return this.component_pattern.password}get hostname(){return this.component_pattern.hostname}get port(){return this.component_pattern.port}get pathname(){return this.component_pattern.pathname}get search(){return this.component_pattern.search}get hash(){return this.component_pattern.hash}};globalThis.URLPattern||(globalThis.URLPattern=mo);var $u=(e,t)=>{var a;if(e===void 0)return{};let r=t.endsWith("/")?t.slice(0,-1):t,o=(a=new mo({pathname:e,baseURL:r}).exec(r))==null?void 0:a.pathname.groups;return o?Object.entries(o).reduce((i,[E,_])=>_===void 0?i:{...i,[E]:_},{}):{}};var Oo=e=>e.headers.get(Ru)||"";import{env as pm}from"process";var ku=()=>({region:pm.AWS_REGION});import{env as ba}from"process";var qu=()=>({id:ba.SITE_ID,name:ba.SITE_NAME,url:ba.URL});var um=typeof Response<"u"&&typeof Response.json=="function"?e=>Response.json(e):e=>new Response(JSON.stringify(e),{headers:{"content-type":"application/json"}}),ju=(e,t)=>{let r={},n=e[xr];try{r=$u(n,e.url)}catch{console.log(`Could not parse function route ${n}.`)}let o={enqueuedPromises:[]};return{context:{account:Ao(e),cookies:t.getPublicInterface(),deploy:fo(e.headers),flags:Lu(e),path:n,geo:Cu(e.headers.get(pu)),ip:Iu(e.headers.get(Tu)),json:um,log:console.log,next:()=>{throw new Error("`context.next` is not implemented for serverless functions")},params:r,requestId:Oo(e),rewrite:a=>{let i=Sm(a,e.url);return lm(i)},server:ku(),site:qu(),url:new URL(e.url),waitUntil:function(i){if(arguments.length===0)throw new TypeError("waitUntil: At least 1 argument required, but only 0 passed");o.enqueuedPromises.push(Promise.resolve(i).catch(E=>console.error(E)))}},state:o}},Sm=(e,t)=>{if(e instanceof URL)return e;if(e.startsWith("/")){let r=new URL(t);return r.pathname=e,r}return new URL(e)},lm=async e=>await fetch(e);import Xu from"assert";function Ku(e){function t(_,T=2){return _.padStart(T,"0")}let r=t(e.getUTCDate().toString()),n=t(e.getUTCHours().toString()),o=t(e.getUTCMinutes().toString()),s=t(e.getUTCSeconds().toString()),a=e.getUTCFullYear(),i=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],E=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];return`${i[e.getUTCDay()]}, ${r} ${E[e.getUTCMonth()]} ${a} ${n}:${o}:${s} GMT`}var Am=/^(?=[\u0020-\u007E]*$)[^()@<>,;:\\"[\]?={}\s]+$/;function fm(e){if(!e.name)return"";let t=[];if(Rm(e.name),hm(e.name,e.value),t.push(`${e.name}=${e.value}`),e.name.startsWith("__Secure")&&(e.secure=!0),e.name.startsWith("__Host")&&(e.path="/",e.secure=!0,delete e.domain),e.secure&&t.push("Secure"),e.httpOnly&&t.push("HttpOnly"),typeof e.maxAge=="number"&&Number.isInteger(e.maxAge)&&(Xu(e.maxAge>=0,"Max-Age must be an integer superior or equal to 0"),t.push(`Max-Age=${e.maxAge}`)),e.domain&&(mm(e.domain),t.push(`Domain=${e.domain}`)),e.sameSite&&t.push(`SameSite=${e.sameSite}`),e.path&&(dm(e.path),t.push(`Path=${e.path}`)),e.expires){let{expires:r}=e,n=Ku(typeof r=="number"?new Date(r):r);t.push(`Expires=${n}`)}return e.unparsed&&t.push(e.unparsed.join("; ")),t.join("; ")}function Rm(e){if(e&&!Am.test(e))throw new TypeError(`Invalid cookie name: "${e}".`)}function dm(e){if(e!=null)for(let t=0;t<e.length;t++){let r=e.charAt(t);if(r<" "||r>"~"||r==";")throw new Error(`${e}: Invalid cookie path char '${r}'`)}}function hm(e,t){if(!(t==null||e==null))for(let r=0;r<t.length;r++){let n=t.charAt(r);if(n<"!"||n=='"'||n==","||n==";"||n=="\\"||n=="\x7F")throw new Error(`RFC2616 cookie '${e}' cannot contain character '${n}'`);if(n>"\x80")throw new Error(`RFC2616 cookie '${e}' can only have US-ASCII chars as value${n.charCodeAt(0).toString(16)}`)}}function mm(e){if(e==null)return;let t=e.charAt(0),r=e.charAt(e.length-1);if(t=="-"||r=="."||r=="-")throw new Error(`Invalid first/last char in cookie domain: ${e}`)}function Wu(e){let t=e.get("Cookie");if(t!=null){let r={},n=t.split(";");for(let o of n){let[s,...a]=o.split("=");Xu(s!=null);let i=s.trim();r[i]=a.join("=")}return r}return{}}function Va(e,t){let r=fm(t);r&&e.append("Set-Cookie",r)}function zu(e,t,r){Va(e,Object.assign({name:t,value:"",expires:new Date(0)},r))}var No=class{constructor(t){this.ops=[],this.request=t}apply(t){this.ops.forEach(r=>{switch(r.type){case"delete":zu(t,r.options.name,{domain:r.options.domain,path:r.options.path});break;case"set":Va(t,r.cookie);break;default:}})}delete(t){let r={path:"/"},n=typeof t=="string"?{name:t}:t;this.ops.push({options:{...r,...n},type:"delete"})}get(t){return Wu(this.request.headers)[t]}getPublicInterface(){return{delete:this.delete.bind(this),get:this.get.bind(this),set:this.set.bind(this)}}set(t,r){let n;if(typeof t=="string"){if(typeof r!="string")throw new TypeError("You must provide the cookie value as a string to 'cookies.set'");n={name:t,value:r}}else n=t;this.ops.push({cookie:n,type:"set"})}};import{Buffer as Ju}from"buffer";import wa from"process";var Om="NETLIFY_PURGE_API_TOKEN",Nm="NETLIFY_BRANCH",vm="NETLIFY_BLOBS_CONTEXT",Ba=e=>typeof e=="string"&&e.length!==0,Qu=(e,t,r,n)=>{var a,i;let{blobs:o}=t,s=(i=(a=r==null?void 0:r.clientContext)==null?void 0:a.custom)==null?void 0:i.purge_api_token;if(Ba(o))try{let E=Ju.from(o,"base64").toString("utf8"),_=JSON.parse(E),T=e.get(Au),p=e.get(lo);wa.env[vm]=Ju.from(JSON.stringify({edgeURL:_.url,uncachedEdgeURL:_.url_uncached,token:_.token,siteID:T,deployID:p,primaryRegion:_.primary_region})).toString("base64")}catch(E){console.error("An internal error occurred while setting up Netlify Blobs:",E)}Ba(s)&&(wa.env[Om]=s),Ba(n==null?void 0:n.branch)&&(wa.env[Nm]=n.branch)};var Zu=e=>e.headers.get(hu)??"";var eS=e=>e.headers.get(mu)??"";var tS=({awsRequestID:e,req:t,branch:r,functionName:n,accountTier:o,logToken:s})=>{let a=new URL(t.url),i=Oo(t),E={aws_req_id:e,aid:Ao(t).id,branch:r??"",account_tier:o??"",did:fo(t.headers).id,fn:n??"",host:a.host,log_token:s,method:t.method,nf_req_id:i,ts:Date.now(),path:a.pathname,span_id:Zu(t),req_start_at:eS(t)};Ma({fields:E,type:"bootstrap/request"})},Ga=({awsRequestID:e,result:t})=>{let r={aws_req_id:e};t instanceof Response?r.status=t.status:t instanceof Error?r.error_message=t.message:r.status=204,Ma({fields:r,type:"bootstrap/response"})};var rS=!1,Pm=e=>(...t)=>{let[r,n]=t;if(typeof r=="string"&&r.startsWith("/"))try{let o=ie.getStore(),s=o==null?void 0:o.context.url;if(!s)throw new Error("Could not find request in context");let a=new URL(r,s);return e(a,n)}catch{Ut("An error occurred in the patched Response.redirect")}return e(...t)},nS=()=>{rS||(rS=!0,"Response"in globalThis&&Response.redirect&&(Response.redirect=Pm(Response.redirect)))};import Lm from"http";import gm from"https";var Cm=globalThis.fetch,oS=e=>{let t=ie.getStore();if(t!=null&&t.cdnLoopHeader){if(e.headersSent){Ut("Headers already sent, cannot add CDN-Loop header");return}e.setHeader("CDN-Loop",t==null?void 0:t.cdnLoopHeader)}};globalThis.fetch=function(t,r){let n=new Request(t,r),o=ie.getStore();return o!=null&&o.cdnLoopHeader&&n.headers.set("CDN-Loop",o.cdnLoopHeader),Cm(n)};for(let e of[Lm,gm]){let t=e.request;e.get=function(...n){let o=t(...n);return oS(o),o.end(),o},e.request=function(...n){let o=t(...n);return oS(o),o}}var ym=Um(xm),bm=20,Vm=e=>awslambda.streamifyResponse(async(t,r,n)=>{let o=Date.now(),s=n.awsRequestId,{body:a,flags:i,headers:E,httpMethod:_,invocationMetadata:T,isBase64Encoded:p,logToken:f,rawUrl:u,route:N}=t,x=new Dr(i||{}),D=Ou(E),j=Pu(a,p),_e=x.get("serverless_functions_abort_signal")===!0?AbortSignal.timeout(n.getRemainingTimeInMillis()-bm):void 0,P=new Ro(u,{body:j,headers:D,method:_,signal:_e}),U=!So()&&x.get("serverless_functions_log_metadata")===!0;x.get("serverless_functions_response_redirect_relative")===!0&&nS(),U&&tS({awsRequestID:s,branch:T==null?void 0:T.branch,functionName:T==null?void 0:T.function_name,accountTier:T==null?void 0:T.account_tier,logToken:f,req:P}),Qu(D,t,n,T),gu(P,x),N&&(P[xr]=N),x.get("serverless_functions_wait_event_loop")===!0&&(n.callbackWaitsForEmptyEventLoop=!1);let I=new No(P),{context:K,state:Br}=ju(P,I);await tu({headers:D,serviceName:K.site.name||"",serviceVersion:K.deploy.id,deploymentEnvironment:K.deploy.context,siteUrl:K.site.url||"",siteId:K.site.id||"",siteName:K.site.name||""});let H=await $a(),Y={cacheAPI:cu(t),cdnLoopHeader:D.get("cdn-loop"),context:K,operationCounter:new uo},le=()=>wm({funcOrFuncPath:e,requestContext:Y,req:P,logMetadata:U,awsRequestID:s,responseStream:r,cookies:I,featureFlags:x,state:Br,startTimestamp:o});if(H)try{let{context:ce}=await Promise.resolve().then(()=>(d(),rt)),{W3CTraceContextPropagator:vo}=await Promise.resolve().then(()=>(M(),m_)),iS=new vo,ES={keys:Po=>[...Po.keys()],get:(Po,cS)=>Po.get(cS)||void 0},_S=iS.extract(ce.active(),D,ES);await ce.with(_S,le)}finally{await ka()}else await le()}),sS=e=>e&&typeof e=="object"&&"default"in e?e.default:e,wm=async({funcOrFuncPath:e,requestContext:t,req:r,logMetadata:n,awsRequestID:o,responseStream:s,cookies:a,featureFlags:i,state:E,startTimestamp:_})=>{try{let T;if(typeof e=="string"){let p=await import(e),f=sS(sS(p));T=await ie.run(t,()=>f(r,t.context))}else T=await ie.run(t,()=>e.default(r,t.context));n&&Ga({awsRequestID:o,result:T}),await Bm(T,s,a,i,_)}catch(T){throw n&&Ga({awsRequestID:o,result:T}),T}if(E.enqueuedPromises.length!==0)try{await Promise.allSettled(E.enqueuedPromises)}catch(T){console.error(T)}},Bm=async(e,t,r,n,o)=>{let s={version:Mm.version},a=Im.from(JSON.stringify(s)).toString("base64");if(e instanceof Response){let i=new Headers(e.headers);r.apply(i);let{body:E,status:_}=e,T=n.evaluations;T.size!==0&&i.set(fu,[...T].join(",")),i.set(xa,a),i.set(du,o.toString());let p={multiValueHeaders:Nu(i),statusCode:_},f=awslambda.HttpResponseStream.from(t,p);if((n.get("serverless_functions_fix_empty_body")===!0||E===null)&&f.write(""),E===null){f.end();return}let u=Dm.fromWeb(E);await ym(u,f);return}if(e===void 0){let i=awslambda.HttpResponseStream.from(t,{statusCode:204,headers:{[xa]:a}});i.write(""),i.end();return}throw new Dt("Function returned an unsupported value. Accepted types are 'Response' or 'undefined'")};import wr from"process";var Gm={delete:e=>{delete wr.env[e]},get:e=>wr.env[e],has:e=>!!wr.env[e],set:(e,t)=>{wr.env[e]=t},toObject:()=>Object.entries(wr.env).reduce((e,[t,r])=>r===void 0?e:{...e,[t]:r},{})},aS={get context(){let e=ie.getStore();return(e==null?void 0:e.context)??null},env:Gm};globalThis.Netlify=aS;var Hm=()=>aS;var gU=()=>Ym(import.meta.url);export{Vm as getLambdaHandler,Hm as getNetlifyGlobal,gU as getPath};
