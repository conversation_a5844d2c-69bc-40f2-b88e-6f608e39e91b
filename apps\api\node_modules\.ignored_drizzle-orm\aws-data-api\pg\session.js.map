{"version": 3, "sources": ["../../../src/aws-data-api/pg/session.ts"], "sourcesContent": ["import type { ColumnMetadata, ExecuteStatementCommandOutput, Field, RDSDataClient } from '@aws-sdk/client-rds-data';\nimport {\n\tBeginTransactionCommand,\n\tCommitTransactionCommand,\n\tExecuteStatementCommand,\n\tRollbackTransactionCommand,\n} from '@aws-sdk/client-rds-data';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport {\n\ttype PgDialect,\n\tPgPreparedQuery,\n\tPgSession,\n\tPgTransaction,\n\ttype PgTransactionConfig,\n\ttype PreparedQueryConfig,\n\ttype QueryResultHKT,\n} from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type QueryTypingsValue, type QueryWithTypings, type SQL, sql } from '~/sql/sql.ts';\nimport { mapResultRow } from '~/utils.ts';\nimport { getValueFromDataApi, toValueParam } from '../common/index.ts';\n\nexport type AwsDataApiClient = RDSDataClient;\n\nexport class AwsDataApiPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'AwsDataApiPreparedQuery';\n\n\tprivate rawQuery: ExecuteStatementCommand;\n\n\tconstructor(\n\t\tprivate client: AwsDataApiClient,\n\t\tqueryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate typings: QueryTypingsValue[],\n\t\tprivate options: AwsDataApiSessionOptions,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\t/** @internal */\n\t\treadonly transactionId: string | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params });\n\t\tthis.rawQuery = new ExecuteStatementCommand({\n\t\t\tsql: queryString,\n\t\t\tparameters: [],\n\t\t\tsecretArn: options.secretArn,\n\t\t\tresourceArn: options.resourceArn,\n\t\t\tdatabase: options.database,\n\t\t\ttransactionId,\n\t\t\tincludeResultMetadata: !fields && !customResultMapper,\n\t\t});\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst { fields, joinsNotNullableMap, customResultMapper } = this;\n\n\t\tconst rows = await this.values(placeholderValues) as unknown[][];\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn rows as T['execute'];\n\t\t}\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(rows)\n\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues?: Record<string, unknown> | undefined): Promise<T['all']> {\n\t\treturn this.execute(placeholderValues);\n\t}\n\n\tasync values(placeholderValues: Record<string, unknown> = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues ?? {});\n\n\t\tthis.rawQuery.input.parameters = params.map((param, index) => ({\n\t\t\tname: `${index + 1}`,\n\t\t\t...toValueParam(param, this.typings[index]),\n\t\t}));\n\n\t\tthis.options.logger?.logQuery(this.rawQuery.input.sql!, this.rawQuery.input.parameters);\n\n\t\tconst { fields, rawQuery, client, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst result = await client.send(rawQuery);\n\t\t\tif (result.columnMetadata && result.columnMetadata.length > 0) {\n\t\t\t\treturn this.mapResultRows(result.records ?? [], result.columnMetadata);\n\t\t\t}\n\t\t\treturn result.records ?? [];\n\t\t}\n\n\t\tconst result = await client.send(rawQuery);\n\n\t\treturn result.records?.map((row: any) => {\n\t\t\treturn row.map((field: Field) => getValueFromDataApi(field));\n\t\t});\n\t}\n\n\t/** @internal */\n\tmapResultRows(records: Field[][], columnMetadata: ColumnMetadata[]) {\n\t\treturn records.map((record) => {\n\t\t\tconst row: Record<string, unknown> = {};\n\t\t\tfor (const [index, field] of record.entries()) {\n\t\t\t\tconst { name } = columnMetadata[index]!;\n\t\t\t\trow[name ?? index] = getValueFromDataApi(field); // not what to default if name is undefined\n\t\t\t}\n\t\t\treturn row;\n\t\t});\n\t}\n}\n\nexport interface AwsDataApiSessionOptions {\n\tlogger?: Logger;\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\ninterface AwsDataApiQueryBase {\n\tresourceArn: string;\n\tsecretArn: string;\n\tdatabase: string;\n}\n\nexport class AwsDataApiSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<AwsDataApiPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'AwsDataApiSession';\n\n\t/** @internal */\n\treadonly rawQuery: AwsDataApiQueryBase;\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly client: AwsDataApiClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: AwsDataApiSessionOptions,\n\t\t/** @internal */\n\t\treadonly transactionId: string | undefined,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.rawQuery = {\n\t\t\tsecretArn: options.secretArn,\n\t\t\tresourceArn: options.resourceArn,\n\t\t\tdatabase: options.database,\n\t\t};\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: QueryWithTypings,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\ttransactionId?: string,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new AwsDataApiPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tquery.typings ?? [],\n\t\t\tthis.options,\n\t\t\tfields,\n\t\t\ttransactionId,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride execute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tthis.transactionId,\n\t\t).execute();\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: AwsDataApiTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst { transactionId } = await this.client.send(new BeginTransactionCommand(this.rawQuery));\n\t\tconst session = new AwsDataApiSession(this.client, this.dialect, this.schema, this.options, transactionId);\n\t\tconst tx = new AwsDataApiTransaction(this.dialect, session, this.schema);\n\t\tif (config) {\n\t\t\tawait tx.setTransaction(config);\n\t\t}\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.client.send(new CommitTransactionCommand({ ...this.rawQuery, transactionId }));\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tawait this.client.send(new RollbackTransactionCommand({ ...this.rawQuery, transactionId }));\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport class AwsDataApiTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<AwsDataApiPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'AwsDataApiTransaction';\n\n\toverride transaction<T>(transaction: (tx: AwsDataApiTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new AwsDataApiTransaction(this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tthis.session.execute(sql`savepoint ${savepointName}`);\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.session.execute(sql`release savepoint ${savepointName}`);\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tthis.session.execute(sql`rollback to savepoint ${savepointName}`);\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport interface AwsDataApiPgQueryResultHKT extends QueryResultHKT {\n\ttype: ExecuteStatementCommandOutput;\n}\n"], "mappings": "AACA;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AACP,SAAS,kBAAkB;AAE3B;AAAA,EAEC;AAAA,EACA;AAAA,EACA;AAAA,OAIM;AAGP,SAAS,kBAA2E,WAAW;AAC/F,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB,oBAAoB;AAI3C,MAAM,gCAA+D,gBAAmB;AAAA,EAK9F,YACS,QACR,aACQ,QACA,SACA,SACA,QAEC,eACD,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,CAAC;AAV1B;AAEA;AACA;AACA;AACA;AAEC;AACD;AAGR,SAAK,WAAW,IAAI,wBAAwB;AAAA,MAC3C,KAAK;AAAA,MACL,YAAY,CAAC;AAAA,MACb,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,MAClB;AAAA,MACA,uBAAuB,CAAC,UAAU,CAAC;AAAA,IACpC,CAAC;AAAA,EACF;AAAA,EAzBA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAyBR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,EAAE,QAAQ,qBAAqB,mBAAmB,IAAI;AAE5D,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAChD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO;AAAA,IACR;AACA,WAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACnF;AAAA,EAEA,IAAI,mBAA4E;AAC/E,WAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtC;AAAA,EAEA,MAAM,OAAO,oBAA6C,CAAC,GAAyB;AACnF,UAAM,SAAS,iBAAiB,KAAK,QAAQ,qBAAqB,CAAC,CAAC;AAEpE,SAAK,SAAS,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO,WAAW;AAAA,MAC9D,MAAM,GAAG,QAAQ,CAAC;AAAA,MAClB,GAAG,aAAa,OAAO,KAAK,QAAQ,KAAK,CAAC;AAAA,IAC3C,EAAE;AAEF,SAAK,QAAQ,QAAQ,SAAS,KAAK,SAAS,MAAM,KAAM,KAAK,SAAS,MAAM,UAAU;AAEtF,UAAM,EAAE,QAAQ,UAAU,QAAQ,mBAAmB,IAAI;AACzD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAMA,UAAS,MAAM,OAAO,KAAK,QAAQ;AACzC,UAAIA,QAAO,kBAAkBA,QAAO,eAAe,SAAS,GAAG;AAC9D,eAAO,KAAK,cAAcA,QAAO,WAAW,CAAC,GAAGA,QAAO,cAAc;AAAA,MACtE;AACA,aAAOA,QAAO,WAAW,CAAC;AAAA,IAC3B;AAEA,UAAM,SAAS,MAAM,OAAO,KAAK,QAAQ;AAEzC,WAAO,OAAO,SAAS,IAAI,CAAC,QAAa;AACxC,aAAO,IAAI,IAAI,CAAC,UAAiB,oBAAoB,KAAK,CAAC;AAAA,IAC5D,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,cAAc,SAAoB,gBAAkC;AACnE,WAAO,QAAQ,IAAI,CAAC,WAAW;AAC9B,YAAM,MAA+B,CAAC;AACtC,iBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC9C,cAAM,EAAE,KAAK,IAAI,eAAe,KAAK;AACrC,YAAI,QAAQ,KAAK,IAAI,oBAAoB,KAAK;AAAA,MAC/C;AACA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AACD;AAeO,MAAM,0BAGH,UAA4D;AAAA,EAMrE,YAEU,QACT,SACQ,QACA,SAEC,eACR;AACD,UAAM,OAAO;AAPJ;AAED;AACA;AAEC;AAGT,SAAK,WAAW;AAAA,MACf,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,IACnB;AAAA,EACD;AAAA,EApBA,QAAiB,UAAU,IAAY;AAAA;AAAA,EAG9B;AAAA,EAmBT,aACC,OACA,QACA,eACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM,WAAW,CAAC;AAAA,MAClB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,QAAW,OAAwB;AAC3C,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,MACA,KAAK;AAAA,IACN,EAAE,QAAQ;AAAA,EACX;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,EAAE,cAAc,IAAI,MAAM,KAAK,OAAO,KAAK,IAAI,wBAAwB,KAAK,QAAQ,CAAC;AAC3F,UAAM,UAAU,IAAI,kBAAkB,KAAK,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,aAAa;AACzG,UAAM,KAAK,IAAI,sBAAsB,KAAK,SAAS,SAAS,KAAK,MAAM;AACvE,QAAI,QAAQ;AACX,YAAM,GAAG,eAAe,MAAM;AAAA,IAC/B;AACA,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,OAAO,KAAK,IAAI,yBAAyB,EAAE,GAAG,KAAK,UAAU,cAAc,CAAC,CAAC;AACxF,aAAO;AAAA,IACR,SAAS,GAAG;AACX,YAAM,KAAK,OAAO,KAAK,IAAI,2BAA2B,EAAE,GAAG,KAAK,UAAU,cAAc,CAAC,CAAC;AAC1F,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,8BAGH,cAAgE;AAAA,EACzE,QAAiB,UAAU,IAAY;AAAA,EAE9B,YAAe,aAA0F;AACjH,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI,sBAAsB,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAClG,SAAK,QAAQ,QAAQ,gBAAgB,aAAa,EAAE;AACpD,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,QAAQ,QAAQ,wBAAwB,aAAa,EAAE;AAC5D,aAAO;AAAA,IACR,SAAS,GAAG;AACX,WAAK,QAAQ,QAAQ,4BAA4B,aAAa,EAAE;AAChE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["result"]}