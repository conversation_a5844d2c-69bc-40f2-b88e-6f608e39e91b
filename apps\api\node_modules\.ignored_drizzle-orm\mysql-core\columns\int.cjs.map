{"version": 3, "sources": ["../../../src/mysql-core/columns/int.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlIntBuilderInitial<TName extends string> = MySqlIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlInt'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlIntBuilder';\n\n\tconstructor(name: T['name'], config?: MySqlIntConfig) {\n\t\tsuper(name, 'number', 'MySqlInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlInt<T extends ColumnBaseConfig<'number', 'MySqlInt'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlInt';\n\n\tgetSQLType(): string {\n\t\treturn `int${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport interface MySqlIntConfig {\n\tunsigned?: boolean;\n}\n\nexport function int<TName extends string>(name: TName, config?: MySqlIntConfig): MySqlIntBuilderInitial<TName> {\n\treturn new MySqlIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkF;AAW3E,MAAM,wBACJ,kDACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,UAAU;AAChC,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI,SAA0C,OAAO,KAAK,MAA8C;AAAA,EAChH;AACD;AAEO,MAAM,iBACJ,2CACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO,MAAM,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACrD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAMO,SAAS,IAA0B,MAAa,QAAwD;AAC9G,SAAO,IAAI,gBAAgB,MAAM,MAAM;AACxC;", "names": []}