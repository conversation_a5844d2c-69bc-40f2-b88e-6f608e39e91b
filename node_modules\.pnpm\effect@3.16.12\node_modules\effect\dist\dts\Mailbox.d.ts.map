{"version": 3, "file": "Mailbox.d.ts", "sourceRoot": "", "sources": ["../../src/Mailbox.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,OAAO,KAAK,EAAE,KAAK,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAC/D,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAC3C,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AACzC,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,WAAW,CAAA;AACrC,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAEnD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAEzC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAEzC;;;;GAIG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAwB,CAAA;AAEpD;;;;GAIG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;;GAIG;AACH,eAAO,MAAM,cAAc,EAAE,OAAO,MAAgC,CAAA;AAEpE;;;;GAIG;AACH,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD;;;;GAIG;AACH,eAAO,MAAM,SAAS,GAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,KAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAA2B,CAAA;AAE7G;;;;GAIG;AACH,eAAO,MAAM,iBAAiB,GAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,KAAG,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAClE,CAAA;AAEhC;;;;;;GAMG;AACH,MAAM,WAAW,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,SAAQ,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;IAChF,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/C;;OAEG;IACH,QAAQ,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,CAAA;IAC7C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC9D;;;OAGG;IACH,QAAQ,CAAC,cAAc,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAA;IAC5D;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;IAC5C;;;OAGG;IACH,QAAQ,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;IACxD;;;OAGG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;IAC7B;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,OAAO,CAAC,CAAA;IACvD;;;OAGG;IACH,QAAQ,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,OAAO,CAAA;IACrD;;;OAGG;IACH,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAA;CACnC;AAED;;;;;;GAMG;AACH,MAAM,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CACnD,SAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW;IAE5E,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE,cAAc,CAAA;IACzC;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACnC;;;;;OAKG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAA;IACzE;;;;;;OAMG;IACH,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAA;IACtF;;;;;;OAMG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,sBAAsB,CAAC,CAAA;IACpD,uCAAuC;IACvC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;IAC/B;;;;OAIG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;IACrC;;;;OAIG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA;CAC1C;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,EAC9B,QAAQ,CAAC,EAAE,MAAM,GAAG;IAClB,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAA;IAC1B,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,GAAG,UAAU,GAAG,SAAS,CAAA;CACvD,GAAG,SAAS,KACV,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAiB,CAAA;AAE1C;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;OAOG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAC7G;;;;;;;OAOG;IACH,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;CAC1F,CAAA;AAEjB;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAsB,CAAA;AAEjH;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAqB,CAAA;AAE9F;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;;;;OAMG;IACH,CACC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACtC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAA;KACnE,GACC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;IACtF;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACrB,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACtC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAA;KACnE,GACC,MAAM,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAA;CAC7B,CAAA"}