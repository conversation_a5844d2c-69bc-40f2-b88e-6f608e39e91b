{"version": 3, "file": "TSemaphore.d.ts", "sourceRoot": "", "sources": ["../../src/TSemaphore.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAE1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AAGpC;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,OAAO,MAAkC,CAAA;AAExE;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,gBAAgB,CAAA;AAEtD;;;GAGG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAUvD;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,UAAU,CAAC;IAClC;;;OAGG;IACH,UAAiB,KAAK;QACpB,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,CAAA;KAC9C;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAoB,CAAA;AAE5E;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAChD;;;OAGG;IACH,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CACzB,CAAA;AAErB;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,CAAsB,CAAA;AAElF;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,UAAU,CAAiB,CAAA;AAE3E;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAoB,CAAA;AAE5E;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAChD;;;OAGG;IACH,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CACzB,CAAA;AAErB;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;OAGG;IACH,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC1F;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACjE,CAAA;AAEvB;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3G;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACjF,CAAA;AAExB;;;GAGG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAA6B,CAAA;AAExH;;;GAGG;AACH,eAAO,MAAM,iBAAiB,EAAE;IAC9B;;;OAGG;IACH,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,IAAI,EAAE,UAAU,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;IAChF;;;OAGG;IACH,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAA;CAChD,CAAA;AAE9B;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,UAAyC,CAAA"}