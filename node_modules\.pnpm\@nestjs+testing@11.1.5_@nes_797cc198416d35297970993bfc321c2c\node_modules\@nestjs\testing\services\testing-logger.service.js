"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestingLogger = void 0;
const common_1 = require("@nestjs/common");
/**
 * @publicApi
 */
class TestingLogger extends common_1.ConsoleLogger {
    constructor() {
        super('Testing');
    }
    log(message) { }
    warn(message) { }
    debug(message) { }
    verbose(message) { }
    error(message, ...optionalParams) {
        return super.error(message, ...optionalParams);
    }
}
exports.TestingLogger = TestingLogger;
