import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ISIN = "isIsin";
/**
 * Checks if the string is an ISIN (stock/security identifier).
 * If given value is not a string, then it returns false.
 */
export declare function isISIN(value: unknown): boolean;
/**
 * Checks if the string is an ISIN (stock/security identifier).
 * If given value is not a string, then it returns false.
 */
export declare function IsISIN(validationOptions?: ValidationOptions): PropertyDecorator;
