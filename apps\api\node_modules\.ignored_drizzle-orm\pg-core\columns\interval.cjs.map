{"version": 3, "sources": ["../../../src/pg-core/columns/interval.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\nimport type { Precision } from './timestamp.ts';\n\nexport type PgIntervalBuilderInitial<TName extends string> = PgIntervalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgInterval';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgIntervalBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgInterval'>>\n\textends PgColumnBuilder<T, { intervalConfig: IntervalConfig }>\n{\n\tstatic readonly [entityKind]: string = 'PgIntervalBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tintervalConfig: IntervalConfig,\n\t) {\n\t\tsuper(name, 'string', 'PgInterval');\n\t\tthis.config.intervalConfig = intervalConfig;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgInterval<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgInterval<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgInterval<T extends ColumnBaseConfig<'string', 'PgInterval'>>\n\textends PgColumn<T, { intervalConfig: IntervalConfig }>\n{\n\tstatic readonly [entityKind]: string = 'PgInterval';\n\n\treadonly fields: IntervalConfig['fields'] = this.config.intervalConfig.fields;\n\treadonly precision: IntervalConfig['precision'] = this.config.intervalConfig.precision;\n\n\tgetSQLType(): string {\n\t\tconst fields = this.fields ? ` ${this.fields}` : '';\n\t\tconst precision = this.precision ? `(${this.precision})` : '';\n\t\treturn `interval${fields}${precision}`;\n\t}\n}\n\nexport interface IntervalConfig {\n\tfields?:\n\t\t| 'year'\n\t\t| 'month'\n\t\t| 'day'\n\t\t| 'hour'\n\t\t| 'minute'\n\t\t| 'second'\n\t\t| 'year to month'\n\t\t| 'day to hour'\n\t\t| 'day to minute'\n\t\t| 'day to second'\n\t\t| 'hour to minute'\n\t\t| 'hour to second'\n\t\t| 'minute to second';\n\tprecision?: Precision;\n}\n\nexport function interval<TName extends string>(\n\tname: TName,\n\tconfig: IntervalConfig = {},\n): PgIntervalBuilderInitial<TName> {\n\treturn new PgIntervalBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,0BACJ,8BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YACC,MACA,gBACC;AACD,UAAM,MAAM,UAAU,YAAY;AAClC,SAAK,OAAO,iBAAiB;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,SAAmC,KAAK,OAAO,eAAe;AAAA,EAC9D,YAAyC,KAAK,OAAO,eAAe;AAAA,EAE7E,aAAqB;AACpB,UAAM,SAAS,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK;AACjD,UAAM,YAAY,KAAK,YAAY,IAAI,KAAK,SAAS,MAAM;AAC3D,WAAO,WAAW,MAAM,GAAG,SAAS;AAAA,EACrC;AACD;AAoBO,SAAS,SACf,MACA,SAAyB,CAAC,GACQ;AAClC,SAAO,IAAI,kBAAkB,MAAM,MAAM;AAC1C;", "names": []}