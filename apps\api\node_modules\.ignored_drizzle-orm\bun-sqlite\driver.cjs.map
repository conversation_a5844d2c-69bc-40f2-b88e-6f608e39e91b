{"version": 3, "sources": ["../../src/bun-sqlite/driver.ts"], "sourcesContent": ["/// <reference types=\"bun-types\" />\n\nimport type { Database } from 'bun:sqlite';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { SQLiteBunSession } from './session.ts';\n\nexport type BunSQLiteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = BaseSQLiteDatabase<'sync', void, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Database,\n\tconfig: DrizzleConfig<TSchema> = {},\n): BunSQLiteDatabase<TSchema> {\n\tconst dialect = new SQLiteSyncDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteBunSession(client, dialect, schema, { logger });\n\treturn new BaseSQLiteDatabase('sync', dialect, session, schema) as BunSQLiteDatabase<TSchema>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA8B;AAC9B,uBAKO;AACP,gBAAmC;AACnC,qBAAkC;AAElC,qBAAiC;AAM1B,SAAS,QACf,QACA,SAAiC,CAAC,GACL;AAC7B,QAAM,UAAU,IAAI,iCAAkB;AACtC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,gCAAiB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACxE,SAAO,IAAI,6BAAmB,QAAQ,SAAS,SAAS,MAAM;AAC/D;", "names": []}