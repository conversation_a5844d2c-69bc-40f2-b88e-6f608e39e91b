{"version": 3, "sources": ["../../../src/mysql-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlBooleanBuilderInitial<TName extends string> = MySqlBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'MySqlBoolean';\n\tdata: boolean;\n\tdriverParam: number | boolean;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'MySqlBoolean'>>\n\textends MySqlColumnBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'MySqlBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'MySqlBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBoolean<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlBoolean<T extends ColumnBaseConfig<'boolean', 'MySqlBoolean'>> extends MySqlColumn<T> {\n\tstatic readonly [entityKind]: string = 'MySqlBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n\n\toverride mapFromDriverValue(value: number | boolean): boolean {\n\t\tif (typeof value === 'boolean') {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === 1;\n\t}\n}\n\nexport function boolean<TName extends string>(name: TName): MySqlBooleanBuilderInitial<TName> {\n\treturn new MySqlBooleanBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,aAAa,0BAA0B;AAWzC,MAAM,4BACJ,mBACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,WAAW,cAAc;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBAA4E,YAAe;AAAA,EACvG,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAkC;AAC7D,QAAI,OAAO,UAAU,WAAW;AAC/B,aAAO;AAAA,IACR;AACA,WAAO,UAAU;AAAA,EAClB;AACD;AAEO,SAAS,QAA8B,MAAgD;AAC7F,SAAO,IAAI,oBAAoB,IAAI;AACpC;", "names": []}