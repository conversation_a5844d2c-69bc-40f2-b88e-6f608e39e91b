{"version": 3, "file": "MutableQueue.d.ts", "sourceRoot": "", "sources": ["../../src/MutableQueue.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAEnC,OAAO,EAAU,KAAK,WAAW,EAA6B,MAAM,kBAAkB,CAAA;AAEtF,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAG7C,QAAA,MAAM,MAAM,EAAE,OAAO,MAAoD,CAAA;AAEzE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,eAAO,MAAM,iBAAiB,eAAkD,CAAA;AAEhF;;;GAGG;AACH,MAAM,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW;IAC7E,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;CAM1B;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC;;OAEG;IACH,KAAY,KAAK,GAAG,OAAO,iBAAiB,CAAA;CAC7C;AA+BD;;;;;GAKG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,EAAE,UAAU,MAAM,KAAG,YAAY,CAAC,CAAC,CAAmB,CAAA;AAE/E;;;;;GAKG;AACH,eAAO,MAAM,SAAS,GAAI,CAAC,OAAK,YAAY,CAAC,CAAC,CAAoB,CAAA;AAElE;;;;;GAKG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC,KAAG,MAAwC,CAAA;AAE1F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC,KAAG,OAA0C,CAAA;AAE7F;;;;;GAKG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC,KAAG,OACsC,CAAA;AAExF;;;;;;;;GAQG;AACH,eAAO,MAAM,QAAQ,GAAI,CAAC,EAAE,MAAM,YAAY,CAAC,CAAC,CAAC,KAAG,MAAgE,CAAA;AAEpH;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,OAAO,CAAA;IAC7C;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;CAWjD,CAAA;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACnE;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;CAiB/D,CAAA;AAEF;;;;;;;;;GASG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAChD;;;;;;;;;OASG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;CAS5C,CAAA;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACzD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;CAgBrD,CAAA"}