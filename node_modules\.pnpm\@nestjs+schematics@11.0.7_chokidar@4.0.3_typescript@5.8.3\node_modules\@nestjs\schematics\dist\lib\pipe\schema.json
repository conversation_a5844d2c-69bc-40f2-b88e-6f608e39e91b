{"$schema": "http://json-schema.org/schema", "$id": "SchematicsNestPipe", "title": "Nest Pipe Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the pipe.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the pipe?"}, "path": {"type": "string", "format": "path", "description": "The path to create the pipe."}, "language": {"type": "string", "description": "Nest pipe language (ts/js)."}, "sourceRoot": {"type": "string", "description": "Nest pipe source root directory."}, "flat": {"type": "boolean", "default": true, "description": "Flag to indicate if a directory is created."}, "spec": {"type": "boolean", "default": true, "description": "Specifies if a spec file is generated."}, "specFileSuffix": {"type": "string", "default": "spec", "description": "Specifies the file suffix of spec files."}}, "required": ["name"]}